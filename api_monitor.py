#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API限频监控器 - 确保Binance API调用不被限频
监控API权重、实现智能限频、资金费率实时校准
"""

import time
import requests
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from functools import wraps
import json
import threading
from collections import deque, defaultdict
import hmac
import hashlib

class BinanceRateLimitMonitor:
    """Binance API限频监控器"""
    
    def __init__(self):
        # 各接口的权重限制 (每分钟)
        self.weight_limits = {
            'spot': 1200,           # 现货API权重限制
            'futures': 2400,        # 期货API权重限制
            'spot_order': 100,      # 现货下单限制(每10秒)
            'futures_order': 300    # 期货下单限制(每10秒)
        }
        
        # 当前权重使用情况
        self.current_weights = defaultdict(int)
        self.weight_history = defaultdict(deque)
        
        # API调用历史 (用于限频)
        self.call_history = defaultdict(deque)
        
        # 监控状态
        self.monitoring_active = True
        self.alert_thresholds = {
            'warning': 0.7,   # 70%使用率预警
            'danger': 0.85,   # 85%使用率危险
            'critical': 0.95  # 95%使用率临界
        }
        
        # 错误统计
        self.error_stats = {
            '429': 0,  # 限频错误
            '418': 0,  # IP被封
            '403': 0,  # 权限错误
            'timeout': 0  # 超时错误
        }
        
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('api_monitor.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('APIMonitor')
    
    def monitor_api_usage(self, response_headers, endpoint_type='spot'):
        """监控API使用情况"""
        
        current_time = time.time()
        
        # 获取响应头中的权重信息
        weight_1m = int(response_headers.get('X-MBX-USED-WEIGHT-1M', 0))
        weight_1d = int(response_headers.get('X-MBX-USED-WEIGHT-1D', 0))
        order_count_10s = int(response_headers.get('X-MBX-ORDER-COUNT-10S', 0))
        order_count_1d = int(response_headers.get('X-MBX-ORDER-COUNT-1D', 0))
        
        # 更新当前权重
        self.current_weights[f'{endpoint_type}_1m'] = weight_1m
        self.current_weights[f'{endpoint_type}_1d'] = weight_1d
        self.current_weights[f'{endpoint_type}_order_10s'] = order_count_10s
        self.current_weights[f'{endpoint_type}_order_1d'] = order_count_1d
        
        # 记录权重历史
        self.weight_history[f'{endpoint_type}_1m'].append((current_time, weight_1m))
        
        # 保持最近1小时的记录
        cutoff_time = current_time - 3600
        while (self.weight_history[f'{endpoint_type}_1m'] and 
               self.weight_history[f'{endpoint_type}_1m'][0][0] < cutoff_time):
            self.weight_history[f'{endpoint_type}_1m'].popleft()
        
        # 检查限制
        limit_check = self.check_rate_limits(endpoint_type, weight_1m, order_count_10s)
        
        # 记录监控日志
        self.logger.info(f"API使用率监控 - {endpoint_type}:")
        self.logger.info(f"  权重(1分钟): {weight_1m}/{self.weight_limits.get(endpoint_type, 1200)}")
        self.logger.info(f"  订单(10秒): {order_count_10s}")
        self.logger.info(f"  状态: {limit_check['status']}")
        
        return limit_check
    
    def check_rate_limits(self, endpoint_type, current_weight, current_orders):
        """检查速率限制"""
        
        weight_limit = self.weight_limits.get(endpoint_type, 1200)
        order_limit = self.weight_limits.get(f'{endpoint_type}_order', 100)
        
        # 计算使用率
        weight_usage = current_weight / weight_limit
        order_usage = current_orders / order_limit if order_limit > 0 else 0
        
        # 确定状态
        max_usage = max(weight_usage, order_usage)
        
        if max_usage >= self.alert_thresholds['critical']:
            status = 'CRITICAL'
            action = 'PAUSE'
        elif max_usage >= self.alert_thresholds['danger']:
            status = 'DANGER'
            action = 'SLOW_DOWN'
        elif max_usage >= self.alert_thresholds['warning']:
            status = 'WARNING'
            action = 'MONITOR'
        else:
            status = 'NORMAL'
            action = 'CONTINUE'
        
        result = {
            'status': status,
            'action': action,
            'weight_usage': weight_usage,
            'order_usage': order_usage,
            'max_usage': max_usage,
            'safe_to_proceed': max_usage < self.alert_thresholds['danger']
        }
        
        # 发出预警
        if status in ['WARNING', 'DANGER', 'CRITICAL']:
            self.logger.warning(f"⚠️ API使用率{status}: {max_usage:.1%}")
            
            if status == 'CRITICAL':
                self.logger.error(f"🚨 API使用率过高，建议暂停请求")
        
        return result
    
    def implement_rate_limiting(self, max_calls_per_minute=60, endpoint_type='spot'):
        """实现智能限频装饰器"""
        
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                current_time = time.time()
                call_key = f"{endpoint_type}_{func.__name__}"
                
                # 清理1分钟前的调用记录
                cutoff_time = current_time - 60
                while (self.call_history[call_key] and 
                       self.call_history[call_key][0] < cutoff_time):
                    self.call_history[call_key].popleft()
                
                # 检查是否超过限制
                if len(self.call_history[call_key]) >= max_calls_per_minute:
                    sleep_time = 60 - (current_time - self.call_history[call_key][0])
                    if sleep_time > 0:
                        self.logger.info(f"🔄 API限频等待 {sleep_time:.1f}秒...")
                        time.sleep(sleep_time)
                
                # 记录调用时间
                self.call_history[call_key].append(current_time)
                
                # 执行函数
                return func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    def handle_api_errors(self, response):
        """处理API错误"""
        
        if response.status_code == 429:
            self.error_stats['429'] += 1
            retry_after = int(response.headers.get('Retry-After', 60))
            self.logger.error(f"🚨 API限频错误(429)，等待{retry_after}秒")
            time.sleep(retry_after)
            return 'retry'
            
        elif response.status_code == 418:
            self.error_stats['418'] += 1
            self.logger.error(f"🚨 IP被封(418)，需要更换IP或等待")
            return 'ip_banned'
            
        elif response.status_code == 403:
            self.error_stats['403'] += 1
            self.logger.error(f"❌ API权限错误(403)")
            return 'permission_denied'
            
        elif response.status_code >= 500:
            self.logger.error(f"⚠️ 服务器错误({response.status_code})")
            time.sleep(5)  # 服务器错误等待5秒
            return 'server_error'
        
        return 'success'
    
    def get_monitoring_report(self):
        """获取监控报告"""
        
        current_time = time.time()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'current_weights': dict(self.current_weights),
            'error_statistics': dict(self.error_stats),
            'status_summary': {},
            'recommendations': []
        }
        
        # 生成状态摘要
        for endpoint_type in ['spot', 'futures']:
            weight_key = f'{endpoint_type}_1m'
            if weight_key in self.current_weights:
                weight = self.current_weights[weight_key]
                limit = self.weight_limits.get(endpoint_type, 1200)
                usage = weight / limit
                
                report['status_summary'][endpoint_type] = {
                    'weight': weight,
                    'limit': limit,
                    'usage_rate': usage,
                    'status': self.get_status_from_usage(usage)
                }
        
        # 生成建议
        total_errors = sum(self.error_stats.values())
        if total_errors > 10:
            report['recommendations'].append("频繁出现API错误，建议检查网络和API配置")
        
        if self.error_stats['429'] > 0:
            report['recommendations'].append("存在限频错误，建议降低请求频率")
        
        if self.error_stats['418'] > 0:
            report['recommendations'].append("IP被封，建议更换网络或使用代理")
        
        return report
    
    def get_status_from_usage(self, usage):
        """根据使用率获取状态"""
        if usage >= self.alert_thresholds['critical']:
            return 'CRITICAL'
        elif usage >= self.alert_thresholds['danger']:
            return 'DANGER'
        elif usage >= self.alert_thresholds['warning']:
            return 'WARNING'
        else:
            return 'NORMAL'

class FundingRateCalibrator:
    """资金费率实时校准器"""
    
    def __init__(self, binance_api_key="", binance_secret=""):
        self.api_key = binance_api_key
        self.secret = binance_secret
        self.base_url = "https://fapi.binance.com"
        
        self.local_calculations = []
        self.binance_records = []
        self.rate_monitor = BinanceRateLimitMonitor()
        
    def _generate_signature(self, params):
        """生成API签名"""
        if not self.secret:
            return ""
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return hmac.new(
            self.secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    @BinanceRateLimitMonitor().implement_rate_limiting(max_calls_per_minute=30, endpoint_type='futures')
    def get_real_funding_rate(self, symbol="BTCUSDT"):
        """获取实时资金费率"""
        
        endpoint = "/fapi/v1/fundingRate"
        params = {"symbol": symbol, "limit": 1}
        
        try:
            response = requests.get(
                self.base_url + endpoint,
                params=params,
                timeout=10
            )
            
            # 监控API使用情况
            self.rate_monitor.monitor_api_usage(response.headers, 'futures')
            
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    funding_rate = float(data[0]['fundingRate'])
                    funding_time = int(data[0]['fundingTime'])
                    
                    # 记录实际费率
                    self.binance_records.append({
                        'timestamp': datetime.now(),
                        'symbol': symbol,
                        'funding_rate': funding_rate,
                        'funding_time': funding_time
                    })
                    
                    return funding_rate, funding_time
            else:
                # 处理API错误
                error_action = self.rate_monitor.handle_api_errors(response)
                if error_action == 'retry':
                    return self.get_real_funding_rate(symbol)
        
        except Exception as e:
            self.rate_monitor.logger.error(f"获取资金费率失败: {e}")
            self.rate_monitor.error_stats['timeout'] += 1
        
        return 0.0001, 0  # 默认值
    
    @BinanceRateLimitMonitor().implement_rate_limiting(max_calls_per_minute=10, endpoint_type='futures')
    def get_funding_history(self, symbol="BTCUSDT", days=7):
        """获取资金费率历史"""
        
        if not self.api_key:
            self.rate_monitor.logger.warning("未配置API Key，无法获取历史数据")
            return []
        
        endpoint = "/fapi/v1/fundingRate"
        
        # 计算时间范围
        end_time = int(time.time() * 1000)
        start_time = end_time - (days * 24 * 60 * 60 * 1000)
        
        params = {
            "symbol": symbol,
            "startTime": start_time,
            "endTime": end_time,
            "limit": 1000,
            "timestamp": int(time.time() * 1000)
        }
        
        # 添加签名
        if self.secret:
            params["signature"] = self._generate_signature(params)
        
        headers = {}
        if self.api_key:
            headers["X-MBX-APIKEY"] = self.api_key
        
        try:
            response = requests.get(
                self.base_url + endpoint,
                params=params,
                headers=headers,
                timeout=15
            )
            
            # 监控API使用情况
            self.rate_monitor.monitor_api_usage(response.headers, 'futures')
            
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                error_action = self.rate_monitor.handle_api_errors(response)
                if error_action == 'retry':
                    return self.get_funding_history(symbol, days)
        
        except Exception as e:
            self.rate_monitor.logger.error(f"获取资金费率历史失败: {e}")
        
        return []
    
    def calculate_local_funding(self, position_size, holding_hours, funding_rate=0.0001):
        """计算本地资金费率"""
        
        # 资金费率每8小时收取一次
        funding_periods = holding_hours / 8
        total_funding_cost = position_size * funding_rate * funding_periods
        
        # 记录本地计算
        self.local_calculations.append({
            'timestamp': datetime.now(),
            'position_size': position_size,
            'holding_hours': holding_hours,
            'funding_rate': funding_rate,
            'calculated_cost': total_funding_cost
        })
        
        return total_funding_cost
    
    def compare_with_binance_bill(self, local_costs, binance_statement=None):
        """与Binance账单对比"""
        
        print("📊 资金费率精确度校准...")
        
        if not binance_statement:
            print("⚠️ 未提供Binance账单，使用模拟数据")
            # 生成模拟Binance账单数据
            binance_statement = []
            for local_cost in local_costs[-10:]:  # 最近10笔
                simulated_cost = local_cost['calculated_cost'] * (1 + np.random.normal(0, 0.05))
                binance_statement.append({
                    'timestamp': local_cost['timestamp'],
                    'funding_cost': simulated_cost
                })
        
        errors = []
        comparisons = []
        
        # 对比本地计算与Binance实际
        for i, (local, binance) in enumerate(zip(local_costs, binance_statement)):
            if 'calculated_cost' in local and 'funding_cost' in binance:
                local_cost = local['calculated_cost']
                binance_cost = binance['funding_cost']
                
                if binance_cost != 0:
                    error_rate = abs(local_cost - binance_cost) / abs(binance_cost)
                    error_bp = error_rate * 10000  # 转为bp
                    
                    errors.append(error_bp)
                    comparisons.append({
                        'index': i,
                        'local_cost': local_cost,
                        'binance_cost': binance_cost,
                        'error_bp': error_bp,
                        'timestamp': local['timestamp']
                    })
                    
                    if error_bp > 5:  # 超过5bp
                        print(f"⚠️ #{i+1} 资金费率误差过大: {error_bp:.2f}bp")
        
        if errors:
            avg_error = np.mean(errors)
            median_error = np.median(errors)
            max_error = np.max(errors)
            
            print(f"📈 资金费率校准结果:")
            print(f"   对比样本数: {len(errors)}")
            print(f"   平均误差: {avg_error:.2f}bp")
            print(f"   中位误差: {median_error:.2f}bp")
            print(f"   最大误差: {max_error:.2f}bp")
            
            # 判断是否通过精确度要求
            accuracy_passed = avg_error <= 0.5  # 平均误差≤0.5bp
            
            if accuracy_passed:
                print("✅ 资金费率计算精确度合格")
            else:
                print("❌ 资金费率计算精确度需要改进")
                print("💡 建议: 调整资金费率计算公式或更新基础参数")
            
            return {
                'passed': accuracy_passed,
                'avg_error_bp': avg_error,
                'median_error_bp': median_error,
                'max_error_bp': max_error,
                'sample_count': len(errors),
                'comparisons': comparisons
            }
        
        print("❌ 无有效对比数据")
        return {'passed': False, 'avg_error_bp': 0, 'sample_count': 0}

def demo_api_monitoring():
    """演示API监控功能"""
    
    print("🎯 API限频监控器 - Demo")
    print("=" * 50)
    
    # 初始化监控器
    monitor = BinanceRateLimitMonitor()
    
    # 模拟API响应头
    mock_headers = {
        'X-MBX-USED-WEIGHT-1M': '450',     # 模拟权重使用
        'X-MBX-USED-WEIGHT-1D': '2500',
        'X-MBX-ORDER-COUNT-10S': '8',
        'X-MBX-ORDER-COUNT-1D': '125'
    }
    
    print("📊 模拟API调用监控:")
    
    # 监控多次调用
    for i in range(5):
        # 递增权重使用
        mock_headers['X-MBX-USED-WEIGHT-1M'] = str(450 + i * 150)
        
        result = monitor.monitor_api_usage(mock_headers, 'spot')
        
        print(f"调用#{i+1}: {result['status']} - 使用率{result['max_usage']:.1%}")
        
        if not result['safe_to_proceed']:
            print(f"⚠️ 建议{result['action']}")
        
        time.sleep(1)
    
    # 生成监控报告
    print("\n📋 监控报告:")
    report = monitor.get_monitoring_report()
    
    print(f"当前权重使用: {json.dumps(report['current_weights'], indent=2)}")
    print(f"错误统计: {json.dumps(report['error_statistics'], indent=2)}")
    
    if report['recommendations']:
        print("建议:")
        for rec in report['recommendations']:
            print(f"  - {rec}")

def demo_funding_calibration():
    """演示资金费率校准"""
    
    print("\n🎯 资金费率校准器 - Demo")
    print("=" * 50)
    
    # 初始化校准器 (无API密钥的演示模式)
    calibrator = FundingRateCalibrator()
    
    # 模拟本地资金费率计算
    print("💰 模拟本地资金费率计算:")
    
    local_costs = []
    for i in range(10):
        position_size = 10000 + i * 1000  # $10k-$19k
        holding_hours = 8 + i * 4         # 8-44小时
        funding_rate = 0.0001 + i * 0.00002  # 0.01%-0.019%
        
        cost = calibrator.calculate_local_funding(
            position_size, holding_hours, funding_rate
        )
        
        local_costs.append(calibrator.local_calculations[-1])
        
        print(f"  #{i+1}: ${position_size:,} × {holding_hours}h × {funding_rate:.5f} = ${cost:.2f}")
    
    # 校准对比
    print("\n📊 与Binance账单对比:")
    calibration_result = calibrator.compare_with_binance_bill(local_costs)
    
    if calibration_result['passed']:
        print("✅ 资金费率计算通过精确度验证")
    else:
        print("⚠️ 资金费率计算需要优化")

def main():
    """主函数"""
    
    # API监控演示
    demo_api_monitoring()
    
    # 资金费率校准演示
    demo_funding_calibration()
    
    print("\n✨ API监控器演示完成!")
    print("💡 可以集成到实盘系统中确保API稳定性")

if __name__ == "__main__":
    # 导入numpy用于演示
    import numpy as np
    main() 