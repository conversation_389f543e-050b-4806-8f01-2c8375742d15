// 账户服务 - 获取币安期货账户数据
import axios from 'axios'

export interface AccountBalance {
  asset: string
  walletBalance: string
  unrealizedProfit: string
  marginBalance: string
  maintMargin: string
  initialMargin: string
  positionInitialMargin: string
  openOrderInitialMargin: string
  crossWalletBalance: string
  crossUnPnl: string
  availableBalance: string
  maxWithdrawAmount: string
  marginAvailable?: boolean
  updateTime?: number
}

export interface Position {
  symbol: string
  initialMargin: string
  maintMargin: string
  unrealizedProfit: string
  positionInitialMargin: string
  openOrderInitialMargin: string
  leverage: string
  isolated: boolean
  entryPrice: string
  maxNotional: string
  bidNotional: string
  askNotional: string
  positionSide: string
  positionAmt: string
  updateTime: number
  markPrice?: string
  liquidationPrice?: string
  marginType?: string
  isolatedMargin?: string
  isAutoAddMargin?: string
  notional?: string
  isolatedWallet?: string
}

export interface AccountInfo {
  feeTier: number
  canTrade: boolean
  canDeposit: boolean
  canWithdraw: boolean
  updateTime: number
  multiAssetsMargin?: boolean
  tradeGroupId?: number
  totalInitialMargin: string
  totalMaintMargin: string
  totalWalletBalance: string
  totalUnrealizedProfit: string
  totalMarginBalance: string
  totalPositionInitialMargin: string
  totalOpenOrderInitialMargin: string
  totalCrossWalletBalance: string
  totalCrossUnPnl: string
  availableBalance: string
  maxWithdrawAmount: string
  assets: AccountBalance[]
  positions: Position[]
}

class AccountService {
  private apiKey: string = ''
  private apiSecret: string = ''
  private baseURL: string = 'https://fapi.binance.com'
  private accountInfo: AccountInfo | null = null
  private isConnected: boolean = false
  private updateInterval: NodeJS.Timeout | null = null

  constructor() {
    // 自动尝试从系统配置加载API凭证
    this.loadCredentialsFromSystemConfig()
  }

  // 设置API凭证
  setCredentials(apiKey: string, apiSecret: string) {
    this.apiKey = apiKey
    this.apiSecret = apiSecret
    console.log('🔑 API凭证已设置')
  }

  // 生成签名
  private async generateSignature(queryString: string): Promise<string> {
    try {
      // 使用浏览器原生的Web Crypto API替代Node.js的crypto模块
      const encoder = new TextEncoder()
      const keyData = encoder.encode(this.apiSecret)
      const messageData = encoder.encode(queryString)
      
      // 导入密钥
      const key = await crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'HMAC', hash: 'SHA-256' },
        false,
        ['sign']
      )
      
      // 生成签名
      const signature = await crypto.subtle.sign('HMAC', key, messageData)
      
      // 转换为十六进制字符串
      const signatureArray = new Uint8Array(signature)
      return Array.from(signatureArray)
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')
    } catch (error) {
      console.error('❌ 签名生成失败:', error)
      throw new Error('无法生成API签名，请检查浏览器兼容性')
    }
  }

  // 获取账户信息
  async getAccountInfo(): Promise<AccountInfo | null> {
    try {
      if (!this.apiKey || !this.apiSecret) {
        console.error('❌ API凭证未设置，无法获取真实账户数据')
        console.warn('📋 请在系统设置中配置 BINANCE_API_KEY 和 BINANCE_API_SECRET')
        this.isConnected = false
        return null
      }

      console.log('🔑 开始连接币安账户...')
      console.log('🌐 使用API Key:', this.apiKey.substring(0, 8) + '***')
      
      const timestamp = Date.now()
      const queryString = `timestamp=${timestamp}`
      
      console.log('🔐 生成API签名中...')
      const signature = await this.generateSignature(queryString)
      console.log('✅ API签名生成成功')

      // 🚀 关键修复：使用统一的axios配置 (包含代理设置)
      const axiosConfig = this.getAxiosConfig({
        headers: {
          'X-MBX-APIKEY': this.apiKey
        },
        params: {
          timestamp,
          signature
        }
      })

      console.log('📡 发送API请求到币安服务器...')
      const response = await axios.get(`${this.baseURL}/fapi/v2/account`, axiosConfig)

      this.accountInfo = response.data
      this.isConnected = true
      console.log('✅ 币安账户连接成功!')
      console.log('💰 账户权益:', response.data.totalMarginBalance, 'USDT')
      console.log('📊 活跃持仓数量:', response.data.positions.filter((p: any) => Math.abs(parseFloat(p.positionAmt)) > 0).length)
      return this.accountInfo

    } catch (error: any) {
      console.error('❌ 币安账户连接失败:', error)
      this.isConnected = false
      
      // 提供详细的错误诊断
      if (error.message?.includes('签名生成')) {
        console.error('🔐 签名生成错误 - 可能是浏览器兼容性问题')
        console.error('💡 解决方案: 请确保使用现代浏览器 (Chrome 60+, Firefox 57+)')
      } else if (error.response) {
        console.error('🚫 币安API响应错误:', error.response.status, error.response.data)
        if (error.response.status === 401) {
          console.error('🔐 认证失败：API Key或Secret Key不正确')
          console.error('💡 解决方案: 请在系统设置中检查API凭证')
        } else if (error.response.status === 403) {
          console.error('🚫 权限不足：API Key没有期货交易权限')
          console.error('💡 解决方案: 请在币安账户中启用期货交易权限')
        } else if (error.response.status === 429) {
          console.error('⏰ API请求频率限制')
          console.error('💡 解决方案: 请稍后重试')
        }
      } else if (error.request) {
        console.error('🌐 网络连接错误：无法连接到币安API服务器')
        console.error('💡 解决方案: 请检查网络连接和代理设置')
      } else {
        console.error('⚙️ 配置错误:', error.message)
      }
      
      return null
    }
  }

  // 从系统配置加载API凭证
  loadCredentialsFromSystemConfig(): boolean {
    try {
      const savedConfig = localStorage.getItem('binance_api_config')
      if (!savedConfig) {
        console.warn('⚠️ 未找到系统配置中的API凭证，请前往"系统设置"页面配置')
        return false
      }

      const config = JSON.parse(savedConfig)
      if (!config.apiKey || !config.secretKey) {
        console.warn('⚠️ 系统配置中的API凭证不完整，请检查"系统设置"页面')
        return false
      }

      this.setCredentials(config.apiKey, config.secretKey)
      console.log('✅ 从系统配置加载API凭证成功')
      console.log('📊 交易所:', config.exchange || 'binance')
      console.log('🧪 测试网络:', config.testnet ? '是' : '否')
      console.log('🌐 代理设置:', config.useProxy ? `启用 (${config.proxyHost || '127.0.0.1'}:${config.proxyPort || '7890'})` : '禁用')
      
      return true
    } catch (error) {
      console.error('❌ 加载系统配置失败:', error)
      return false
    }
  }

  // 获取系统配置状态
  getSystemConfigStatus() {
    try {
      const savedConfig = localStorage.getItem('binance_api_config')
      if (!savedConfig) {
        return {
          hasConfig: false,
          isValid: false,
          message: '未找到系统配置，请前往"系统设置"页面配置API凭证'
        }
      }

      const config = JSON.parse(savedConfig)
      const hasApiKey = !!config.apiKey
      const hasSecretKey = !!config.secretKey
      const isValid = hasApiKey && hasSecretKey

      return {
        hasConfig: true,
        isValid,
        exchange: config.exchange || 'binance',
        testnet: config.testnet || false,
        useProxy: config.useProxy || false,
        proxyHost: config.proxyHost || '127.0.0.1',
        proxyPort: config.proxyPort || '7890',
        message: isValid 
          ? `系统配置完整，API凭证可用${config.useProxy ? ' (已启用代理)' : ''}` 
          : '系统配置不完整，请检查API Key和Secret Key'
      }
    } catch (error) {
      return {
        hasConfig: false,
        isValid: false,
        message: '系统配置解析失败，请重新配置'
      }
    }
  }

  // 启动自动更新
  startAutoUpdate(intervalMs: number = 5000) {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
    }

    this.updateInterval = setInterval(async () => {
      await this.getAccountInfo()
    }, intervalMs)

    console.log(`🔄 账户数据自动更新已启动 (${intervalMs}ms间隔)`)
  }

  // 停止自动更新
  stopAutoUpdate() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
      console.log('⏹️ 账户数据自动更新已停止')
    }
  }

  // 获取当前缓存的账户信息
  getCachedAccountInfo(): AccountInfo | null {
    return this.accountInfo
  }

  // 获取连接状态
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  // 获取活跃持仓 (过滤掉数量为0的持仓)
  getActivePositions(): Position[] {
    if (!this.accountInfo) return []
    
    return this.accountInfo.positions.filter(position => {
      const positionAmt = parseFloat(position.positionAmt)
      return Math.abs(positionAmt) > 0.000001 // 过滤掉极小的持仓
    })
  }

  // 获取USDT余额信息
  getUSDTBalance(): AccountBalance | null {
    if (!this.accountInfo) return null
    
    return this.accountInfo.assets.find(asset => asset.asset === 'USDT') || null
  }

  // 计算总体统计
  getPortfolioStats() {
    if (!this.accountInfo) {
      return {
        totalEquity: 0,
        availableBalance: 0,
        usedMargin: 0,
        unrealizedPnl: 0,
        marginRatio: 0,
        activePositionsCount: 0
      }
    }

    const totalEquity = parseFloat(this.accountInfo.totalMarginBalance)
    const availableBalance = parseFloat(this.accountInfo.availableBalance) 
    const usedMargin = parseFloat(this.accountInfo.totalPositionInitialMargin)
    const unrealizedPnl = parseFloat(this.accountInfo.totalUnrealizedProfit)
    const activePositions = this.getActivePositions()
    const marginRatio = totalEquity > 0 ? (usedMargin / totalEquity) * 100 : 0

    return {
      totalEquity,
      availableBalance,
      usedMargin,
      unrealizedPnl,
      marginRatio,
      activePositionsCount: activePositions.length
    }
  }

  // 重置服务
  reset() {
    this.stopAutoUpdate()
    this.accountInfo = null
    this.isConnected = false
    this.apiKey = ''
    this.apiSecret = ''
  }

  // 清除存储的API凭证
  clearStoredCredentials() {
    // 只清理旧的存储方式，新方式使用系统配置
    console.log('🗑️ 请前往系统设置页面管理API凭证')
  }

  // 获取通用的axios配置 (包含代理设置)
  private getAxiosConfig(additionalConfig: any = {}): any {
    const systemConfig = this.getSystemConfigStatus()
    
    const baseConfig: any = {
      timeout: 30000, // 30秒超时
      ...additionalConfig
    }

    // 检查是否需要使用代理
    if (systemConfig.useProxy) {
      console.log(`🌐 使用Clash代理: ${systemConfig.proxyHost}:${systemConfig.proxyPort}`)
      
      baseConfig.proxy = {
        host: systemConfig.proxyHost,
        port: parseInt(systemConfig.proxyPort),
        protocol: 'http'
      }
    } else {
      console.log('🌐 直连币安API (未启用代理)')
    }

    return baseConfig
  }

  // 刷新系统配置
  refreshSystemConfig(): boolean {
    console.log('🔄 刷新系统配置...')
    return this.loadCredentialsFromSystemConfig()
  }

  // 检查是否有有效的API凭证
  hasValidCredentials(): boolean {
    return !!(this.apiKey && this.apiSecret)
  }
}

// 创建全局实例
export const accountService = new AccountService()

// 辅助函数：格式化数字显示
export const formatNumber = (value: string | number, decimals: number = 2): string => {
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return '0.00'
  return num.toFixed(decimals)
}

// 辅助函数：格式化大数字 (K, M, B)
export const formatLargeNumber = (value: string | number): string => {
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return '0'
  
  if (Math.abs(num) >= **********) {
    return (num / **********).toFixed(1) + 'B'
  } else if (Math.abs(num) >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (Math.abs(num) >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  
  return num.toFixed(2)
}

// 辅助函数：判断持仓方向
export const getPositionSide = (positionAmt: string): 'LONG' | 'SHORT' | 'NONE' => {
  const amt = parseFloat(positionAmt)
  if (amt > 0) return 'LONG'
  if (amt < 0) return 'SHORT'
  return 'NONE'
}

// 辅助函数：获取币种图标
export const getCryptoIcon = (symbol: string): string => {
  const iconMap: { [key: string]: string } = {
    'BTCUSDT': '₿',
    'ETHUSDT': '⟠', 
    'BNBUSDT': '🔶',
    'ADAUSDT': '🔵',
    'DOTUSDT': '⚫',
    'LINKUSDT': '🔗',
    'LTCUSDT': 'Ł',
    'XRPUSDT': '⭕'
  }
  
  return iconMap[symbol] || '💰'
} 