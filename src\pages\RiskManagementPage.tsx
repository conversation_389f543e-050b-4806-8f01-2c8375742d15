import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useStrategy } from '@/context/StrategyContext'
import { binanceApi, LiveTradingMetrics } from '@/services/binanceApi'
import { 
  AlertTriangle, 
  Shield, 
  TrendingDown, 
  Target,
  Activity,
  Settings,
  BarChart3,
  Eye,
  StopCircle,
  Play,
  Pause,
  Wifi,
  WifiOff,
  AlertCircle,
  CheckCircle,
  XCircle,
  X
} from 'lucide-react'



// 初始实盘状态（程序未启动）
const initialLiveState: LiveTradingMetrics = {
  isConnected: false,
  isTrading: false,
  accountBalance: 0,
  availableBalance: 0,
  currentPosition: 0,
  unrealizedPnL: 0,
  realizedPnL: 0,
  todayTrades: 0,
  currentDrawdown: 0,
  maxDrawdownToday: 0,
  riskLevel: 'LOW'
}

export function RiskManagementPage() {
  const [mode, setMode] = useState<'live' | 'backtest'>('live') // 默认实盘模式
  const [liveMetrics, setLiveMetrics] = useState<LiveTradingMetrics>(initialLiveState)
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected')
  const [notifications, setNotifications] = useState<Array<{id: string; type: 'success' | 'error' | 'info'; message: string}>>([])
  
  // 显示通知
  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    const notification = {
      id: Date.now().toString(),
      type,
      message
    }
    setNotifications(prev => [notification, ...prev.slice(0, 2)])
    
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id))
    }, 3000)
  }
  
  // 币安API连接
  const connectToBinance = async () => {
    setConnectionStatus('connecting')
    
    try {
      // 从localStorage读取API配置
      const savedConfig = localStorage.getItem('binance_api_config')
      if (!savedConfig) {
        setConnectionStatus('disconnected')
        showNotification('error', '请先在系统设置中配置API密钥')
        return
      }
      
      const config = JSON.parse(savedConfig)
      if (!config.apiKey || !config.secretKey) {
        setConnectionStatus('disconnected')
        showNotification('error', 'API密钥配置不完整，请检查设置')
        return
      }
      
      // 使用真实API配置连接
      const success = await binanceApi.connect(
        config.apiKey,
        config.secretKey,
        {
          enabled: config.useProxy || false,
          host: config.proxyHost || '127.0.0.1',
          port: config.proxyPort || '7890'
        }
      )
      
      if (success) {
        setConnectionStatus('connected')
        // 连接成功后立即获取数据
        await updateLiveMetrics()
        showNotification('success', '币安API连接成功')
      } else {
        setConnectionStatus('disconnected')
        showNotification('error', '连接币安API失败，请检查密钥和网络')
      }
    } catch (error) {
      setConnectionStatus('disconnected')
      showNotification('error', '连接过程中出现错误，请重试')
    }
  }

  // 更新实时指标
  const updateLiveMetrics = async () => {
    try {
      const metrics = await binanceApi.getLiveTradingMetrics()
      if (metrics) {
        setLiveMetrics(metrics)
      }
    } catch (error) {
      console.error('更新实时指标失败:', error)
    }
  }

  const startTrading = async () => {
    if (!liveMetrics.isConnected) {
      showNotification('error', '请先连接币安API')
      return
    }
    
    const success = await binanceApi.startTrading()
    if (success) {
      await updateLiveMetrics()
      showNotification('success', '策略终极版已启动')
    }
  }

  const stopTrading = async () => {
    const success = await binanceApi.stopTrading()
    if (success) {
      await updateLiveMetrics()
      showNotification('info', '交易已暂停')
    }
  }

  const emergencyStop = async () => {
    const success = await binanceApi.emergencyStop()
    if (success) {
      await updateLiveMetrics()
      showNotification('error', '紧急停止执行成功')
    }
  }

  // 实时数据更新
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    
    if (liveMetrics.isConnected) {
      interval = setInterval(updateLiveMetrics, 5000) // 每5秒更新一次
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [liveMetrics.isConnected])

  // 断开连接时清理
  useEffect(() => {
    return () => {
      binanceApi.disconnect()
    }
  }, [])

  // 实盘模式渲染
  const renderLiveMode = () => (
    <div className="space-y-6">
      {/* 通知区域 */}
      {notifications.length > 0 && (
        <div className="space-y-3">
          {notifications.map((notification) => (
            <Card key={notification.id} className={`${
              notification.type === 'success' ? 'border-green-500/30 bg-green-500/10' :
              notification.type === 'error' ? 'border-red-500/30 bg-red-500/10' :
              'border-blue-500/30 bg-blue-500/10'
            }`}>
              <CardContent className="pt-4">
                <div className="flex items-start gap-3">
                  {notification.type === 'success' ? (
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  ) : notification.type === 'error' ? (
                    <XCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                  )}
                  <div className="flex-1">
                    <p className={`text-sm ${
                      notification.type === 'success' ? 'text-green-400' :
                      notification.type === 'error' ? 'text-red-400' :
                      'text-blue-400'
                    }`}>
                      {notification.message}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setNotifications(prev => prev.filter(n => n.id !== notification.id))}
                    className="p-1 h-6 w-6"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 连接状态和控制 */}
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            {liveMetrics.isConnected ? (
              <Wifi className="w-5 h-5 text-green-400" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-400" />
            )}
            币安实盘连接状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Badge className={liveMetrics.isConnected ? 
                'bg-green-500/20 text-green-400 border-green-500/30' : 
                'bg-red-500/20 text-red-400 border-red-500/30'
              }>
                {connectionStatus === 'connecting' ? '连接中...' : 
                 liveMetrics.isConnected ? '已连接' : '未连接'}
              </Badge>
              
              {liveMetrics.isConnected && (
                <Badge className={liveMetrics.isTrading ? 
                  'bg-blue-500/20 text-blue-400 border-blue-500/30' : 
                  'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                }>
                  {liveMetrics.isTrading ? '交易中' : '待命中'}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {!liveMetrics.isConnected ? (
                <Button 
                  onClick={connectToBinance}
                  disabled={connectionStatus === 'connecting'}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {connectionStatus === 'connecting' ? '连接中...' : '连接币安'}
                </Button>
              ) : (
                <>
                  {!liveMetrics.isTrading ? (
                    <Button 
                      onClick={startTrading}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Play className="w-4 h-4 mr-2" />
                      开始交易
                    </Button>
                  ) : (
                    <Button 
                      onClick={stopTrading}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      <Pause className="w-4 h-4 mr-2" />
                      暂停交易
                    </Button>
                  )}
                  <Button onClick={emergencyStop} className="bg-red-600 hover:bg-red-700">
                    <StopCircle className="w-4 h-4 mr-2" />
                    紧急停止
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 实盘账户状态 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">账户余额</p>
                <p className="text-2xl font-bold text-white">
                  ${liveMetrics.accountBalance.toLocaleString()}
                </p>
                <p className="text-xs text-slate-400 mt-1">USDT</p>
              </div>
              <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <Target className="w-5 h-5 text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">当前仓位</p>
                <p className="text-2xl font-bold text-yellow-400">
                  {liveMetrics.currentPosition.toFixed(1)}%
                </p>
                <p className="text-xs text-yellow-300 mt-1">资金占用</p>
              </div>
              <div className="w-10 h-10 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">今日PnL</p>
                <p className={`text-2xl font-bold ${liveMetrics.realizedPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {liveMetrics.realizedPnL >= 0 ? '+' : ''}${liveMetrics.realizedPnL.toFixed(2)}
                </p>
                <p className="text-xs text-slate-400 mt-1">已实现损益</p>
              </div>
              <div className={`w-10 h-10 ${liveMetrics.realizedPnL >= 0 ? 'bg-green-500/20' : 'bg-red-500/20'} rounded-lg flex items-center justify-center`}>
                <TrendingDown className={`w-5 h-5 ${liveMetrics.realizedPnL >= 0 ? 'text-green-400 rotate-180' : 'text-red-400'}`} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">今日交易</p>
                <p className="text-2xl font-bold text-purple-400">{liveMetrics.todayTrades}</p>
                <p className="text-xs text-purple-300 mt-1">执行次数</p>
              </div>
              <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                <Activity className="w-5 h-5 text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 实盘风险监控 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Shield className="w-5 h-5" />
              实时风险监控
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-slate-400">当前回撤</span>
              <span className={`font-bold ${liveMetrics.currentDrawdown > 10 ? 'text-red-400' : 'text-green-400'}`}>
                {liveMetrics.currentDrawdown.toFixed(2)}%
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-slate-400">今日最大回撤</span>
              <span className={`font-bold ${liveMetrics.maxDrawdownToday > 5 ? 'text-red-400' : 'text-yellow-400'}`}>
                {liveMetrics.maxDrawdownToday.toFixed(2)}%
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-slate-400">风险等级</span>
              <Badge className={
                liveMetrics.riskLevel === 'CRITICAL' ? 'bg-red-500/20 text-red-400 border-red-500/30' :
                liveMetrics.riskLevel === 'HIGH' ? 'bg-orange-500/20 text-orange-400 border-orange-500/30' :
                liveMetrics.riskLevel === 'MEDIUM' ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' :
                'bg-green-500/20 text-green-400 border-green-500/30'
              }>
                {liveMetrics.riskLevel === 'LOW' ? '低风险' :
                 liveMetrics.riskLevel === 'MEDIUM' ? '中等风险' :
                 liveMetrics.riskLevel === 'HIGH' ? '高风险' : '极高风险'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Settings className="w-5 h-5" />
              实盘参数设置
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-slate-400">最大回撤限制</span>
              <span className="text-white font-bold">15%</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-slate-400">最大仓位限制</span>
              <span className="text-white font-bold">30%</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-slate-400">紧急停损</span>
              <Switch checked={true} />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-slate-400">实时监控</span>
              <Switch checked={liveMetrics.isConnected} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 程序未启动提示 */}
      {!liveMetrics.isConnected && (
        <Card className="bg-yellow-500/10 border border-yellow-500/30">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <AlertCircle className="w-8 h-8 text-yellow-400" />
              <div>
                <h3 className="text-lg font-semibold text-yellow-400">程序未启动</h3>
                <p className="text-yellow-300 mt-1">
                  当前显示的是初始状态。请连接币安API并启动策略终极版开始实盘交易。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )

  // 回测模式渲染（保留原有逻辑）
  const renderBacktestMode = () => {
    const { activeStrategy } = useStrategy()
    
    if (!activeStrategy) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-white mb-2">没有回测数据</h2>
            <p className="text-slate-400">请先加载策略终极版回测结果</p>
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-6">
        {/* 回测数据展示 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">历史最大回撤</p>
                  <p className="text-2xl font-bold text-orange-400">-{activeStrategy.maxDrawdown.toFixed(2)}%</p>
                  <p className="text-xs text-orange-300 mt-1">策略终极版</p>
                </div>
                <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center">
                  <TrendingDown className="w-5 h-5 text-orange-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">年化收益率</p>
                  <p className="text-2xl font-bold text-green-400">{activeStrategy.profitPercent.toFixed(2)}%</p>
                  <p className="text-xs text-green-300 mt-1">历史表现</p>
                </div>
                <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <Target className="w-5 h-5 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">历史交易频率</p>
                  <p className="text-2xl font-bold text-blue-400">{(activeStrategy.trades / 1977).toFixed(1)}</p>
                  <p className="text-xs text-blue-300 mt-1">次/天</p>
                </div>
                <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <Activity className="w-5 h-5 text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">总交易次数</p>
                  <p className="text-2xl font-bold text-purple-400">{activeStrategy.trades.toLocaleString()}</p>
                  <p className="text-xs text-purple-300 mt-1">回测期间</p>
                </div>
                <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="bg-blue-500/10 border border-blue-500/30">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <Eye className="w-8 h-8 text-blue-400" />
              <div>
                <h3 className="text-lg font-semibold text-blue-400">回测模式</h3>
                <p className="text-blue-300 mt-1">
                  当前显示的是策略终极版的历史回测数据，实际表现可能因市场条件而异。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和模式切换 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">风险管理</h1>
          <p className="text-slate-400 mt-1">智能策略终极版 - 实盘风险监控与控制</p>
        </div>
        <Tabs value={mode} onValueChange={(value) => setMode(value as 'live' | 'backtest')}>
          <TabsList className="bg-slate-800 border-slate-700">
            <TabsTrigger value="live" className="text-white">实盘模式</TabsTrigger>
            <TabsTrigger value="backtest" className="text-white">回测模式</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* 根据模式渲染不同内容 */}
      {mode === 'live' ? renderLiveMode() : renderBacktestMode()}
    </div>
  )
} 