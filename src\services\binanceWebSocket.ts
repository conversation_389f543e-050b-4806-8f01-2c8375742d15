// 币安U本位合约WebSocket实时数据服务

export interface TickerData {
  symbol: string
  price: number
  bid: number
  ask: number
  volume24h: number
  change24h: number
  changePercent24h: number
  high24h: number
  low24h: number
  lastUpdate: number
}

export interface OrderBookData {
  symbol: string
  bids: [string, string][]
  asks: [string, string][]
  lastUpdateId: number
}

export interface TradeData {
  symbol: string
  price: number
  quantity: number
  time: number
  isBuyerMaker: boolean
}

export interface AccountUpdateData {
  eventType: string
  eventTime: number
  balances: Array<{
    asset: string
    walletBalance: string
    crossWalletBalance: string
    balanceChange: string
  }>
  positions: Array<{
    symbol: string
    positionAmount: string
    entryPrice: string
    breakEvenPrice: string
    markPrice: string
    unrealizedPnL: string
    marginType: string
    isolatedWallet: string
    positionSide: string
  }>
}

type WebSocketCallback = (data: any) => void

class BinanceWebSocketService {
  private connections: Map<string, WebSocket> = new Map()
  private callbacks: Map<string, WebSocketCallback[]> = new Map()
  private reconnectAttempts: Map<string, number> = new Map()
  private maxReconnectAttempts: number = 5
  private reconnectDelay: number = 1000
  private isConnected: boolean = false
  private activeSymbol: string | null = null

  // 检查是否已连接
  getConnectionStatus(): boolean {
    if (!this.isConnected || !this.activeSymbol) {
      return false
    }
    
    // 检查是否有活跃的WebSocket连接
    let hasActiveConnection = false
    this.connections.forEach((ws) => {
      if (ws.readyState === WebSocket.OPEN) {
        hasActiveConnection = true
      }
    })
    
    return hasActiveConnection
  }

  // 获取当前活跃币种
  getActiveSymbol(): string | null {
    return this.activeSymbol
  }

  // 启动监控（仅在开始交易时调用）
  startMonitoring(symbol: string): void {
    console.log(`开始监控 ${symbol} (币安U本位合约)`)
    this.activeSymbol = symbol.toUpperCase()
    this.isConnected = true
    
    // 清理之前的连接
    this.disconnectAll()
    
    // 注意：不在这里直接订阅，让调用方单独设置回调
    console.log(`WebSocket服务已准备好监控 ${this.activeSymbol}`)
  }

  // 停止监控
  stopMonitoring(): void {
    console.log('停止监控')
    this.isConnected = false
    this.activeSymbol = null
    
    // 强制断开所有连接
    this.disconnectAll()
    
    // 清理所有回调
    this.callbacks.clear()
    
    // 清理重连尝试
    this.reconnectAttempts.clear()
  }

  // 订阅Ticker数据（币安期货）
  subscribeTicker(symbol: string, callback?: (data: TickerData) => void): void {
    const streamName = `${symbol.toLowerCase()}@ticker`
    
    if (callback) {
      if (!this.callbacks.has(streamName)) {
        this.callbacks.set(streamName, [])
      }
      this.callbacks.get(streamName)!.push(callback)
    }

    // 币安期货WebSocket端点
    this.connectWebSocket(streamName, (data) => {
      if (data.e === '24hrTicker') {
        const tickerData: TickerData = {
          symbol: data.s,
          price: parseFloat(data.c),
          bid: parseFloat(data.b) || 0, // 24hrTicker可能不包含bid/ask，默认为0
          ask: parseFloat(data.a) || 0,
          volume24h: parseFloat(data.v),
          change24h: parseFloat(data.P),
          changePercent24h: parseFloat(data.P),
          high24h: parseFloat(data.h),
          low24h: parseFloat(data.l),
          lastUpdate: data.E
        }
        
        // 通知所有订阅者
        const callbacks = this.callbacks.get(streamName) || []
        callbacks.forEach(cb => cb(tickerData))
      }
    })
  }

  // 订阅最佳买卖价数据（币安期货BookTicker）
  subscribeBookTicker(symbol: string, callback?: (data: { symbol: string, bid: number, ask: number, bidQty: number, askQty: number }) => void): void {
    const streamName = `${symbol.toLowerCase()}@bookTicker`
    
    if (callback) {
      if (!this.callbacks.has(streamName)) {
        this.callbacks.set(streamName, [])
      }
      this.callbacks.get(streamName)!.push(callback)
    }

    this.connectWebSocket(streamName, (data) => {
      if (data.e === 'bookTicker') {
        const bookTickerData = {
          symbol: data.s,
          bid: parseFloat(data.b),
          ask: parseFloat(data.a),
          bidQty: parseFloat(data.B),
          askQty: parseFloat(data.A)
        }
        
        const callbacks = this.callbacks.get(streamName) || []
        callbacks.forEach(cb => cb(bookTickerData))
      }
    })
  }

  // 订阅深度数据（币安期货）
  subscribeDepth(symbol: string, callback?: (data: OrderBookData) => void): void {
    const streamName = `${symbol.toLowerCase()}@depth5@100ms`
    
    if (callback) {
      if (!this.callbacks.has(streamName)) {
        this.callbacks.set(streamName, [])
      }
      this.callbacks.get(streamName)!.push(callback)
    }

    this.connectWebSocket(streamName, (data) => {
      if (data.e === 'depthUpdate') {
        const depthData: OrderBookData = {
          symbol: data.s,
          bids: data.b || [],
          asks: data.a || [],
          lastUpdateId: data.u || 0
        }
        
        const callbacks = this.callbacks.get(streamName) || []
        callbacks.forEach(cb => cb(depthData))
      }
    })
  }

  // 订阅交易数据（币安期货）
  subscribeTrades(symbol: string, callback?: (data: TradeData) => void): void {
    const streamName = `${symbol.toLowerCase()}@aggTrade`
    
    if (callback) {
      if (!this.callbacks.has(streamName)) {
        this.callbacks.set(streamName, [])
      }
      this.callbacks.get(streamName)!.push(callback)
    }

    this.connectWebSocket(streamName, (data) => {
      if (data.e === 'aggTrade') {
        const tradeData: TradeData = {
          symbol: data.s,
          price: parseFloat(data.p),
          quantity: parseFloat(data.q),
          time: data.T,
          isBuyerMaker: data.m
        }
        
        const callbacks = this.callbacks.get(streamName) || []
        callbacks.forEach(cb => cb(tradeData))
      }
    })
  }

  // 订阅账户更新（需要listenKey）
  subscribeAccountUpdate(listenKey: string, callback: (data: AccountUpdateData) => void): void {
    const wsUrl = `wss://fstream.binance.com/ws/${listenKey}`
    const ws = new WebSocket(wsUrl)

    ws.onopen = () => {
      console.log('账户数据流连接已建立')
    }

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        if (data.e === 'ACCOUNT_UPDATE') {
          const accountData: AccountUpdateData = {
            eventType: data.e,
            eventTime: data.E,
            balances: data.a.B || [],
            positions: data.a.P || []
          }
          callback(accountData)
        }
      } catch (error) {
        console.error('账户数据解析错误:', error)
      }
    }

    ws.onclose = () => {
      console.log('账户数据流连接已关闭')
    }

    ws.onerror = (error) => {
      console.error('账户数据流错误:', error)
    }

    this.connections.set('accountUpdate', ws)
  }

  // 创建WebSocket连接（币安期货）
  private connectWebSocket(streamName: string, callback: WebSocketCallback): void {
    if (this.connections.has(streamName)) {
      const existingWs = this.connections.get(streamName)
      if (existingWs?.readyState === WebSocket.OPEN) {
        console.log(`WebSocket连接已存在: ${streamName}`)
        return // 已有有效连接
      } else {
        // 清理无效连接
        existingWs?.close()
        this.connections.delete(streamName)
      }
    }

    // 币安U本位合约WebSocket端点
    const wsUrl = `wss://fstream.binance.com/ws/${streamName}`
    console.log(`正在连接WebSocket: ${wsUrl}`)
    
    const ws = new WebSocket(wsUrl)

    ws.onopen = () => {
      console.log(`币安期货WebSocket连接已建立: ${streamName}`)
      this.reconnectAttempts.set(streamName, 0)
    }

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        
        // 添加详细的数据接收日志
        if (data.e === '24hrTicker') {
          console.log(`📨 [${streamName}] 收到Ticker数据:`, {
            symbol: data.s,
            price: parseFloat(data.c).toFixed(2),
            bid: data.b ? parseFloat(data.b).toFixed(2) : 'N/A',
            ask: data.a ? parseFloat(data.a).toFixed(2) : 'N/A',
            change24h: parseFloat(data.P).toFixed(2) + '%',
            time: new Date(data.E).toLocaleTimeString()
          })
        }
        
        // 添加bookTicker数据日志
        if (data.e === 'bookTicker') {
          console.log(`📨 [${streamName}] 收到BookTicker数据:`, {
            symbol: data.s,
            bid: parseFloat(data.b).toFixed(2),
            ask: parseFloat(data.a).toFixed(2),
            time: new Date().toLocaleTimeString()
          })
        }
        
        callback(data)
      } catch (error) {
        console.error('WebSocket数据解析错误:', error)
      }
    }

    ws.onclose = (event) => {
      console.log(`WebSocket连接已关闭: ${streamName}, code: ${event.code}, reason: ${event.reason}`)
      this.connections.delete(streamName)
      if (this.isConnected && this.activeSymbol) {
        this.attemptReconnect(streamName, callback)
      }
    }

    ws.onerror = (error) => {
      console.error(`WebSocket连接错误: ${streamName}`, error)
    }

    this.connections.set(streamName, ws)
  }

  // 重连机制
  private attemptReconnect(streamName: string, callback: WebSocketCallback): void {
    if (!this.isConnected || !this.activeSymbol) {
      console.log(`监控已停止，取消重连: ${streamName}`)
      return
    }
    
    const attempts = this.reconnectAttempts.get(streamName) || 0
    
    if (attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(streamName, attempts + 1)
      
      setTimeout(() => {
        if (this.isConnected && this.activeSymbol) {
          console.log(`尝试重连 ${streamName} (${attempts + 1}/${this.maxReconnectAttempts})`)
          this.connectWebSocket(streamName, callback)
        }
      }, this.reconnectDelay * Math.pow(2, attempts))
    } else {
      console.error(`WebSocket重连失败，已达到最大尝试次数: ${streamName}`)
    }
  }

  // 取消订阅
  unsubscribe(symbol: string, type: 'ticker' | 'depth' | 'trade'): void {
    let streamName = `${symbol.toLowerCase()}@${type}`
    if (type === 'depth') {
      streamName = `${symbol.toLowerCase()}@depth5@100ms`
    } else if (type === 'trade') {
      streamName = `${symbol.toLowerCase()}@aggTrade`
    }
    
    const ws = this.connections.get(streamName)
    if (ws) {
      ws.close()
      this.connections.delete(streamName)
    }
    
    this.callbacks.delete(streamName)
    this.reconnectAttempts.delete(streamName)
  }

  // 断开所有连接
  disconnectAll(): void {
    this.connections.forEach((ws, streamName) => {
      ws.close()
      console.log(`已断开WebSocket连接: ${streamName}`)
    })
    
    this.connections.clear()
    this.callbacks.clear()
    this.reconnectAttempts.clear()
  }

  // 获取所有连接状态
  getAllConnectionStatus(): { [streamName: string]: boolean } {
    const status: { [streamName: string]: boolean } = {}
    
    this.connections.forEach((ws, streamName) => {
      status[streamName] = ws.readyState === WebSocket.OPEN
    })
    
    return status
  }
}

export const binanceWebSocket = new BinanceWebSocketService() 