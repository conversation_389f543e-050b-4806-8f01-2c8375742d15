// 策略终极版 - 技术指标计算服务 (基于真实K线数据)

import { binanceKlineService, KlineData, KlineInterval } from './binanceKlineService'

export enum MarketState {
  SIDEWAYS = "震荡",           // 43.6%时间分布
  UPTREND = "上涨趋势",        // 25.1%时间分布
  DOWNTREND = "下跌趋势",      // 20.2%时间分布
  VOLATILE = "高波动",         // 9.3%时间分布
  BREAKOUT = "突破",           // 1.7%时间分布
  SUPER_TREND = "超强趋势"     // 0.1%时间分布
}

export enum StrategyMode {
  GRID = "网格策略",           // 9.5% - 稳定基础
  TREND = "趋势策略",          // 39.2% - 核心策略
  SCALPING = "超短线策略",     // 50.6% - 主力策略
  SUPER_TREND = "超强趋势"     // 0.7% - 爆发利器
}

export interface StrategyIndicators {
  // 市场状态
  marketState: MarketState
  marketStateConfidence: number
  
  // 当前策略模式
  currentMode: StrategyMode
  modeConfidence: number
  
  // 移动平均线 (完整斐波那契周期)
  ma_3: number
  ma_5: number
  ma_8: number
  ma_13: number
  ma_21: number
  ma_34: number
  ma_55: number
  ma_89: number
  ema_8: number
  ema_21: number
  
  // RSI指标 (多周期)
  rsi_5: number
  rsi_7: number
  rsi_14: number
  rsi_21: number
  
  // 动量指标 (多时间框架) - 基于K线收盘价
  momentum_1: number   // 1根K线前
  momentum_2: number   // 2根K线前
  momentum_4: number   // 4根K线前
  momentum_8: number   // 8根K线前
  momentum_16: number  // 16根K线前
  
  // 策略信号强度
  gridSignal: number        // 网格信号 0-100
  trendSignal: number       // 趋势信号 0-100
  scalpingSignal: number    // 超短线信号 0-100
  superTrendSignal: number  // 超强趋势信号 0-100
  
  // 风险指标
  volatility: number
  drawdownRisk: number
  leverageUsage: number
  
  // 数据状态
  dataReady: boolean
  klineCount: number
  
  lastUpdate: number
}

class StrategyIndicatorService {
  private currentSymbol: string | null = null
  private currentInterval: KlineInterval = '1m' // 使用1分钟K线作为基础
  private isInitialized: boolean = false
  
  // 策略参数 (来自文档)
  private readonly params = {
    grid_spacing: 0.004,           // 网格间距0.4%
    trend_threshold: 0.008,        // 趋势判断0.8%
    scalp_threshold: 0.0015,       // 超短线触发0.15%
    breakout_threshold: 0.006,     // 突破阈值0.6%
    super_trend_threshold: 0.02,   // 超强趋势2%
    momentum_factor: 0.003         // 动量因子0.3%
  }

  // 初始化K线数据 (替代updatePrice方法)
  async initializeKlineData(symbol: string, interval: KlineInterval = '1m'): Promise<StrategyIndicators> {
    try {
      console.log(`🚀 初始化K线指标计算: ${symbol} ${interval}`)
      
      this.currentSymbol = symbol
      this.currentInterval = interval
      
      // 1. 获取历史K线数据 (确保有足够数据计算MA89)
      await binanceKlineService.fetchHistoricalKlines(symbol, interval, 200)
      
      // 2. 订阅实时K线更新
      binanceKlineService.subscribeKline(symbol, interval, (kline: KlineData) => {
        console.log(`📊 收到新K线: ${symbol} 收盘价: ${kline.close}`)
        // K线更新会自动触发指标重新计算
      })
      
      this.isInitialized = true
      
      // 3. 计算初始指标
      return this.calculateIndicators()
      
    } catch (error) {
      console.error('K线数据初始化失败:', error)
      return this.getEmptyIndicators()
    }
  }

  // 获取当前指标 (基于缓存的K线数据)
  getCurrentIndicators(): StrategyIndicators {
    if (!this.isInitialized || !this.currentSymbol) {
      return this.getEmptyIndicators()
    }
    
    return this.calculateIndicators()
  }

  // 计算所有指标 (基于K线数据)
  private calculateIndicators(): StrategyIndicators {
    if (!this.currentSymbol || !this.currentInterval) {
      return this.getEmptyIndicators()
    }

    // 获取K线数据
    const allKlines = binanceKlineService.getKlineData(this.currentSymbol, this.currentInterval)
    const isReady = binanceKlineService.isDataReady(this.currentSymbol, this.currentInterval)
    
    // 🔧 关键修复：只使用已完成的历史K线，排除最后一根可能未完成的K线
    // 这样可以避免技术指标因实时价格变动而频繁跳动
    const klines = allKlines.length > 1 ? allKlines.slice(0, -1) : allKlines
    
    console.log(`📊 指标计算: 总K线${allKlines.length}根, 使用历史完整K线${klines.length}根 (排除当前未完成K线)`)
    
    if (klines.length < 89) {
      console.log(`⚠️ K线数据不足: ${klines.length}/89, 等待更多数据...`)
      return this.getPartialIndicators(klines, isReady)
    }

    return {
      // 市场状态识别
      marketState: this.identifyMarketState(klines),
      marketStateConfidence: this.calculateStateConfidence(),
      
      // 策略模式识别
      currentMode: this.identifyStrategyMode(klines),
      modeConfidence: this.calculateModeConfidence(),
      
      // 移动平均线 (基于收盘价)
      ma_3: this.calculateMAFromKlines(klines, 3),
      ma_5: this.calculateMAFromKlines(klines, 5),
      ma_8: this.calculateMAFromKlines(klines, 8),
      ma_13: this.calculateMAFromKlines(klines, 13),
      ma_21: this.calculateMAFromKlines(klines, 21),
      ma_34: this.calculateMAFromKlines(klines, 34),
      ma_55: this.calculateMAFromKlines(klines, 55),
      ma_89: this.calculateMAFromKlines(klines, 89),
      ema_8: this.calculateEMAFromKlines(klines, 8),
      ema_21: this.calculateEMAFromKlines(klines, 21),
      
      // RSI指标
      rsi_5: this.calculateRSIFromKlines(klines, 5),
      rsi_7: this.calculateRSIFromKlines(klines, 7),
      rsi_14: this.calculateRSIFromKlines(klines, 14),
      rsi_21: this.calculateRSIFromKlines(klines, 21),
      
      // 动量指标 (基于K线收盘价)
      momentum_1: this.calculateMomentumFromKlines(klines, 1),
      momentum_2: this.calculateMomentumFromKlines(klines, 2),
      momentum_4: this.calculateMomentumFromKlines(klines, 4),
      momentum_8: this.calculateMomentumFromKlines(klines, 8),
      momentum_16: this.calculateMomentumFromKlines(klines, 16),
      
      // 策略信号强度
      gridSignal: this.calculateGridSignalFromKlines(klines),
      trendSignal: this.calculateTrendSignalFromKlines(klines),
      scalpingSignal: this.calculateScalpingSignalFromKlines(klines),
      superTrendSignal: this.calculateSuperTrendSignalFromKlines(klines),
      
      // 风险指标
      volatility: this.calculateVolatilityFromKlines(klines),
      drawdownRisk: this.calculateDrawdownRisk(),
      leverageUsage: this.calculateLeverageUsage(),
      
      // 数据状态
      dataReady: isReady,
      klineCount: klines.length, // 显示实际用于计算的K线数量
      
      lastUpdate: Date.now()
    }
  }

  // 基于K线计算移动平均线
  private calculateMAFromKlines(klines: KlineData[], period: number): number {
    if (klines.length < period) return 0
    
    const closePrices = klines.slice(-period).map(k => k.close)
    return closePrices.reduce((sum, price) => sum + price, 0) / period
  }

  // 基于K线计算EMA (标准算法)
  private calculateEMAFromKlines(klines: KlineData[], period: number): number {
    if (klines.length < period) return 0
    
    const multiplier = 2 / (period + 1)
    
    // 使用前period个数据的SMA作为EMA初始值
    let ema = 0
    for (let i = 0; i < period; i++) {
      ema += klines[i].close
    }
    ema /= period
    
    // 从第period个数据开始计算EMA
    for (let i = period; i < klines.length; i++) {
      ema = (klines[i].close * multiplier) + (ema * (1 - multiplier))
    }
    
    return ema
  }

  // 基于K线计算RSI (标准Wilder平滑算法)
  private calculateRSIFromKlines(klines: KlineData[], period: number): number {
    if (klines.length < period + 1) return 50
    
    // 计算价格变化
    const changes = []
    for (let i = 1; i < klines.length; i++) {
      changes.push(klines[i].close - klines[i - 1].close)
    }
    
    if (changes.length < period) return 50
    
    // 初始平均增益和损失 (前period个周期)
    let avgGain = 0
    let avgLoss = 0
    
    for (let i = 0; i < period; i++) {
      if (changes[i] > 0) {
        avgGain += changes[i]
      } else {
        avgLoss += Math.abs(changes[i])
      }
    }
    
    avgGain /= period
    avgLoss /= period
    
    // Wilder平滑 (剩余周期)
    for (let i = period; i < changes.length; i++) {
      const gain = changes[i] > 0 ? changes[i] : 0
      const loss = changes[i] < 0 ? Math.abs(changes[i]) : 0
      
      avgGain = ((avgGain * (period - 1)) + gain) / period
      avgLoss = ((avgLoss * (period - 1)) + loss) / period
    }
    
    if (avgLoss === 0) return 100
    if (avgGain === 0) return 0
    
    const rs = avgGain / avgLoss
    return 100 - (100 / (1 + rs))
  }

  // 基于K线计算动量
  private calculateMomentumFromKlines(klines: KlineData[], periods: number): number {
    if (klines.length < periods + 1) return 0
    
    const current = klines[klines.length - 1].close
    const previous = klines[klines.length - 1 - periods].close
    
    return (current / previous) - 1
  }

  // 基于K线计算波动率
  private calculateVolatilityFromKlines(klines: KlineData[]): number {
    if (klines.length < 20) return 0
    
    const returns = []
    for (let i = 1; i < klines.length; i++) {
      returns.push((klines[i].close / klines[i - 1].close) - 1)
    }
    
    const mean = returns.reduce((a, b) => a + b, 0) / returns.length
    const variance = returns.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / returns.length
    
    return Math.sqrt(variance)
  }

  // 市场状态识别 (基于K线)
  private identifyMarketState(klines: KlineData[]): MarketState {
    if (klines.length < 21) return MarketState.SIDEWAYS
    
    const momentum_1 = this.calculateMomentumFromKlines(klines, 1)
    const momentum_4 = this.calculateMomentumFromKlines(klines, 4)
    const volatility = this.calculateVolatilityFromKlines(klines)
    const ma_8 = this.calculateMAFromKlines(klines, 8)
    const ma_21 = this.calculateMAFromKlines(klines, 21)
    const currentPrice = klines[klines.length - 1].close

    // 超强趋势 (2%单向动量)
    if (Math.abs(momentum_4) > this.params.super_trend_threshold) {
      return MarketState.SUPER_TREND
    }

    // 突破 (0.6%快速变化)
    if (Math.abs(momentum_1) > this.params.breakout_threshold) {
      return MarketState.BREAKOUT
    }

    // 高波动 (波动率>3%)
    if (volatility > 0.03) {
      return MarketState.VOLATILE
    }

    // 趋势判断 (基于均线和动量)
    const trendStrength = momentum_4
    const maConfirm = ma_8 > ma_21 // MA8在MA21之上确认上升趋势
    if (trendStrength > this.params.trend_threshold && currentPrice > ma_21 && maConfirm) {
      return MarketState.UPTREND
    } else if (trendStrength < -this.params.trend_threshold && currentPrice < ma_21 && !maConfirm) {
      return MarketState.DOWNTREND
    }

    // 默认震荡
    return MarketState.SIDEWAYS
  }

  // 策略模式识别
  private identifyStrategyMode(klines: KlineData[]): StrategyMode {
    const marketState = this.identifyMarketState(klines)
    const momentum_1 = this.calculateMomentumFromKlines(klines, 1)
    const volatility = this.calculateVolatilityFromKlines(klines)

    // 超强趋势策略 (0.7%概率)
    if (marketState === MarketState.SUPER_TREND) {
      return StrategyMode.SUPER_TREND
    }

    // 超短线策略 (50.6%概率 - 主力)
    if (Math.abs(momentum_1) > this.params.scalp_threshold && volatility > 0.001) {
      return StrategyMode.SCALPING
    }

    // 趋势策略 (39.2%概率 - 核心)
    if (marketState === MarketState.UPTREND || marketState === MarketState.DOWNTREND) {
      return StrategyMode.TREND
    }

    // 网格策略 (9.5%概率 - 基础)
    return StrategyMode.GRID
  }

  // 其他计算方法保持不变，但改为从K线获取数据...
  private calculateGridSignalFromKlines(klines: KlineData[]): number {
    if (klines.length < 21) return 0
    
    const volatility = this.calculateVolatilityFromKlines(klines)
    const momentum_1 = Math.abs(this.calculateMomentumFromKlines(klines, 1))
    const momentum_2 = Math.abs(this.calculateMomentumFromKlines(klines, 2))
    
    // 多重移动平均线收敛检测
    const ma_3 = this.calculateMAFromKlines(klines, 3)
    const ma_5 = this.calculateMAFromKlines(klines, 5)
    const ma_8 = this.calculateMAFromKlines(klines, 8)
    const ma_13 = this.calculateMAFromKlines(klines, 13)
    const ma_21 = this.calculateMAFromKlines(klines, 21)
    
    if (ma_21 === 0) return 0
    
    // 修正：更严格的网格策略条件 (9.5%历史频率)
    const volFactor = volatility > 0.0005 && volatility < 0.015 ? Math.max(0, 1 - volatility * 50) : 0.1
    const momentumFactor = momentum_1 < 0.001 ? 1 : Math.max(0, 1 - momentum_1 * 800)
    const stabilityFactor = momentum_2 < 0.0008 ? 1 : Math.max(0, 1 - momentum_2 * 1200)
    const maConvergence = this.calculateMAConvergence(ma_3, ma_5, ma_8, ma_13, ma_21)
    
    const gridStrength = (
      volFactor * 0.4 + 
      momentumFactor * 0.3 + 
      stabilityFactor * 0.2 + 
      maConvergence * 0.1
    )
    
    // 限制网格策略信号强度，符合9.5%频率
    return Math.min(45, gridStrength * 60)
  }

  private calculateTrendSignalFromKlines(klines: KlineData[]): number {
    if (klines.length < 34) return 0
    
    const momentum_4 = this.calculateMomentumFromKlines(klines, 4)
    const momentum_8 = this.calculateMomentumFromKlines(klines, 8)
    
    const ma_8 = this.calculateMAFromKlines(klines, 8)
    const ma_13 = this.calculateMAFromKlines(klines, 13)
    const ma_21 = this.calculateMAFromKlines(klines, 21)
    const ma_34 = this.calculateMAFromKlines(klines, 34)
    
    if (ma_34 === 0) return 0
    
    // 修正：提升趋势策略信号强度 (39.2%历史频率 - 核心策略)
    const momentumStrength = Math.min(1, Math.abs(momentum_4) / 0.008)  // 降低阈值
    const mediumMomentum = Math.min(1, Math.abs(momentum_8) / 0.015)    // 降低阈值
    const maAlignment = this.calculateMAAlignment(ma_8, ma_13, ma_21, ma_34)
    const maStrength = Math.min(1, Math.abs(ma_8 - ma_34) / ma_34 / 0.008)
    const trendBoost = 1.4  // 核心策略增强系数
    
    const combinedStrength = (
      momentumStrength * 0.4 + 
      mediumMomentum * 0.3 + 
      maAlignment * 0.25 + 
      maStrength * 0.05
    )
    
    return Math.min(100, combinedStrength * trendBoost * 85)
  }

  private calculateScalpingSignalFromKlines(klines: KlineData[]): number {
    if (klines.length < 5) return 0
    
    const momentum_1 = Math.abs(this.calculateMomentumFromKlines(klines, 1))
    const volatility = this.calculateVolatilityFromKlines(klines)
    
    // 修正：符合策略文档的超短线条件 (50.6%历史频率)
    const momentumFactor = Math.min(1, momentum_1 / 0.0015)  // 降低到0.15%阈值
    const volFactor = volatility > 0.0008 && volatility < 0.025 ? 1 : 0.4  // 扩大波动率窗口
    const frequencyBoost = 1.8  // 提升基础信号强度，符合主力策略地位
    
    return Math.min(100, momentumFactor * volFactor * frequencyBoost * 85)
  }

  private calculateSuperTrendSignalFromKlines(klines: KlineData[]): number {
    if (klines.length < 16) return 0
    
    const momentum_4 = Math.abs(this.calculateMomentumFromKlines(klines, 4))
    const momentum_16 = Math.abs(this.calculateMomentumFromKlines(klines, 16))
    
    const shortMomentum = momentum_4 > 0.015 ? 1 : momentum_4 / 0.015
    const longMomentum = momentum_16 > 0.03 ? 1 : momentum_16 / 0.03
    
    return Math.min(100, Math.min(shortMomentum, longMomentum) * 90)
  }

  // 保持现有的辅助方法...
  private calculateMAAlignment(ma_8: number, ma_13: number, ma_21: number, ma_34: number): number {
    if (ma_8 === 0 || ma_13 === 0 || ma_21 === 0 || ma_34 === 0) return 0
    
    const upTrendAlign = ma_8 > ma_13 && ma_13 > ma_21 && ma_21 > ma_34
    const downTrendAlign = ma_8 < ma_13 && ma_13 < ma_21 && ma_21 < ma_34
    
    if (upTrendAlign || downTrendAlign) {
      return 1.0
    }
    
    let alignmentScore = 0
    if ((ma_8 > ma_13) === (ma_13 > ma_21)) alignmentScore += 0.33
    if ((ma_13 > ma_21) === (ma_21 > ma_34)) alignmentScore += 0.33
    if ((ma_8 > ma_21) === (ma_21 > ma_34)) alignmentScore += 0.34
    
    return alignmentScore
  }

  private calculateMAConvergence(ma_3: number, ma_5: number, ma_8: number, ma_13: number, ma_21: number): number {
    if (ma_3 === 0 || ma_5 === 0 || ma_8 === 0 || ma_13 === 0 || ma_21 === 0) return 0
    
    const range_short = Math.abs(ma_3 - ma_8) / ma_8
    const range_medium = Math.abs(ma_5 - ma_13) / ma_13
    const range_long = Math.abs(ma_8 - ma_21) / ma_21
    
    const convergence = Math.max(0, 1 - (range_short + range_medium + range_long) * 100)
    
    return convergence
  }

  // 修复：基于真实数据计算回撤风险
  private calculateDrawdownRisk(): number {
    if (!this.currentSymbol || !this.currentInterval) return 15
    
    const allKlines = binanceKlineService.getKlineData(this.currentSymbol, this.currentInterval)
    // 只使用已完成的历史K线
    const klines = allKlines.length > 1 ? allKlines.slice(0, -1) : allKlines
    if (klines.length < 20) return 15
    
    // 计算最近50期的最大回撤
    let maxPrice = klines[0].close
    let maxDrawdown = 0
    
    for (let i = 1; i < Math.min(klines.length, 50); i++) {
      const currentPrice = klines[i].close
      if (currentPrice > maxPrice) {
        maxPrice = currentPrice
      } else {
        const drawdown = (maxPrice - currentPrice) / maxPrice
        maxDrawdown = Math.max(maxDrawdown, drawdown)
      }
    }
    
    // 转换为百分比，添加缓冲
    return Math.min(30, Math.max(5, maxDrawdown * 100 * 1.2))
  }

  // 修复：基于策略模式计算杠杆使用
  private calculateLeverageUsage(): number {
    if (!this.currentSymbol || !this.currentInterval) return 1.27
    
    const allKlines = binanceKlineService.getKlineData(this.currentSymbol, this.currentInterval)
    // 只使用已完成的历史K线
    const klines = allKlines.length > 1 ? allKlines.slice(0, -1) : allKlines
    if (klines.length < 10) return 1.27
    
    const volatility = this.calculateVolatilityFromKlines(klines)
    const marketState = this.identifyMarketState(klines)
    
    // 基于市场状态和波动率动态调整杠杆
    let baseLeverage = 1.27
    
    if (marketState === MarketState.SUPER_TREND) {
      baseLeverage = 2.5  // 超强趋势允许更高杠杆
    } else if (marketState === MarketState.UPTREND || marketState === MarketState.DOWNTREND) {
      baseLeverage = 1.8  // 趋势行情适中杠杆
    } else if (marketState === MarketState.VOLATILE) {
      baseLeverage = 1.1  // 高波动降低杠杆
    }
    
    // 波动率调整：波动率越高，杠杆越低
    const volAdjustment = Math.max(0.5, Math.min(1.2, 1 - (volatility - 0.01) * 10))
    
    return Math.min(2.64, Math.max(1.0, baseLeverage * volAdjustment))
  }

  // 修复：基于技术指标一致性计算市场状态置信度
  private calculateStateConfidence(): number {
    if (!this.currentSymbol || !this.currentInterval) return 75
    
    const allKlines = binanceKlineService.getKlineData(this.currentSymbol, this.currentInterval)
    // 只使用已完成的历史K线
    const klines = allKlines.length > 1 ? allKlines.slice(0, -1) : allKlines
    if (klines.length < 21) return 75
    
    // 计算各指标的一致性
    const momentum_1 = this.calculateMomentumFromKlines(klines, 1)
    const momentum_4 = this.calculateMomentumFromKlines(klines, 4)
    const volatility = this.calculateVolatilityFromKlines(klines)
    const ma_8 = this.calculateMAFromKlines(klines, 8)
    const ma_21 = this.calculateMAFromKlines(klines, 21)
    
    // 趋势一致性检查
    const shortTermTrend = momentum_1 > 0 ? 1 : -1
    const mediumTermTrend = momentum_4 > 0 ? 1 : -1
    const maTrend = ma_8 > ma_21 ? 1 : -1
    
    const trendConsistency = Math.abs(shortTermTrend + mediumTermTrend + maTrend) / 3
    
    // 波动率稳定性 (低波动率 = 高置信度)
    const volStability = Math.max(0, 1 - volatility * 100)
    
    // 综合置信度
    const confidence = (trendConsistency * 0.6 + volStability * 0.4) * 100
    
    return Math.min(95, Math.max(65, confidence))
  }

  // 修复：基于模式识别准确度计算置信度
  private calculateModeConfidence(): number {
    if (!this.currentSymbol || !this.currentInterval) return 80
    
    const allKlines = binanceKlineService.getKlineData(this.currentSymbol, this.currentInterval)
    // 只使用已完成的历史K线
    const klines = allKlines.length > 1 ? allKlines.slice(0, -1) : allKlines
    if (klines.length < 21) return 80
    
    const currentMode = this.identifyStrategyMode(klines)
    const volatility = this.calculateVolatilityFromKlines(klines)
    const momentum_1 = Math.abs(this.calculateMomentumFromKlines(klines, 1))
    
    let confidence = 80
    
    // 基于策略模式特征的置信度计算
    switch (currentMode) {
      case StrategyMode.SUPER_TREND:
        // 超强趋势需要强动量确认
        confidence = momentum_1 > 0.02 ? 95 : 60
        break
      case StrategyMode.SCALPING:
        // 超短线需要适中波动率
        confidence = (volatility > 0.001 && volatility < 0.025) ? 90 : 70
        break
      case StrategyMode.TREND:
        // 趋势策略需要持续动量
        const momentum_4 = Math.abs(this.calculateMomentumFromKlines(klines, 4))
        confidence = momentum_4 > 0.008 ? 85 : 75
        break
      case StrategyMode.GRID:
        // 网格策略需要低波动率
        confidence = volatility < 0.015 ? 85 : 70
        break
    }
    
    return Math.min(95, Math.max(60, confidence))
  }

  // 获取空指标 (数据不足时)
  private getEmptyIndicators(): StrategyIndicators {
    return {
      marketState: MarketState.SIDEWAYS,
      marketStateConfidence: 0,
      currentMode: StrategyMode.GRID,
      modeConfidence: 0,
      ma_3: 0, ma_5: 0, ma_8: 0, ma_13: 0, ma_21: 0, ma_34: 0, ma_55: 0, ma_89: 0,
      ema_8: 0, ema_21: 0,
      rsi_5: 50, rsi_7: 50, rsi_14: 50, rsi_21: 50,
      momentum_1: 0, momentum_2: 0, momentum_4: 0, momentum_8: 0, momentum_16: 0,
      gridSignal: 0, trendSignal: 0, scalpingSignal: 0, superTrendSignal: 0,
      volatility: 0, drawdownRisk: 0, leverageUsage: 1.0,
      dataReady: false, klineCount: 0,
      lastUpdate: Date.now()
    }
  }

  // 获取部分指标 (数据不足时的优雅降级)
  private getPartialIndicators(klines: KlineData[], isReady: boolean): StrategyIndicators {
    const indicators = this.getEmptyIndicators()
    indicators.dataReady = isReady
    indicators.klineCount = klines.length
    
    // 计算可用的指标
    if (klines.length >= 3) {
      indicators.ma_3 = this.calculateMAFromKlines(klines, 3)
    }
    if (klines.length >= 5) {
      indicators.ma_5 = this.calculateMAFromKlines(klines, 5)
      indicators.rsi_5 = this.calculateRSIFromKlines(klines, 5)
    }
    if (klines.length >= 8) {
      indicators.ma_8 = this.calculateMAFromKlines(klines, 8)
      indicators.ema_8 = this.calculateEMAFromKlines(klines, 8)
      indicators.rsi_7 = this.calculateRSIFromKlines(klines, 7)
    }
    // ... 其他可用指标
    
    return indicators
  }

  // 重置服务
  reset(): void {
    this.currentSymbol = null
    this.isInitialized = false
    // binanceKlineService.disconnectAll() // 暂时注释，方法不存在
  }

  // 获取数据状态
  getDataStatus() {
    return {
      initialized: this.isInitialized,
      symbol: this.currentSymbol,
      interval: this.currentInterval,
      klineService: binanceKlineService.getServiceStatus()
    }
  }
}

// 创建全局实例
export const strategyIndicators = new StrategyIndicatorService()

// 保持现有的显示信息函数...
export const getStrategyModeInfo = (mode: StrategyMode) => {
  const modeConfig = {
    [StrategyMode.GRID]: {
      icon: '🟦',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20',
      borderColor: 'border-blue-500/30',
      description: '稳定基础策略',
      frequency: '9.5%'
    },
    [StrategyMode.TREND]: {
      icon: '📈',
      color: 'text-green-400',
      bgColor: 'bg-green-500/20',
      borderColor: 'border-green-500/30',
      description: '核心策略',
      frequency: '39.2%'
    },
    [StrategyMode.SCALPING]: {
      icon: '⚡',
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/20',
      borderColor: 'border-yellow-500/30',
      description: '主力策略',
      frequency: '50.6%'
    },
    [StrategyMode.SUPER_TREND]: {
      icon: '🚀',
      color: 'text-red-400',
      bgColor: 'bg-red-500/20',
      borderColor: 'border-red-500/30',
      description: '爆发利器',
      frequency: '0.7%'
    }
  }
  
  return modeConfig[mode]
}

export const getMarketStateInfo = (state: MarketState) => {
  const stateConfig = {
    [MarketState.SIDEWAYS]: {
      icon: '↔️',
      color: 'text-gray-400',
      description: '震荡整理',
      frequency: '43.6%'
    },
    [MarketState.UPTREND]: {
      icon: '📈',
      color: 'text-green-400',
      description: '上涨趋势',
      frequency: '25.1%'
    },
    [MarketState.DOWNTREND]: {
      icon: '📉',
      color: 'text-red-400',
      description: '下跌趋势',
      frequency: '20.2%'
    },
    [MarketState.VOLATILE]: {
      icon: '⚡',
      color: 'text-yellow-400',
      description: '高波动',
      frequency: '9.3%'
    },
    [MarketState.BREAKOUT]: {
      icon: '💥',
      color: 'text-orange-400',
      description: '突破',
      frequency: '1.7%'
    },
    [MarketState.SUPER_TREND]: {
      icon: '🚀',
      color: 'text-purple-400',
      description: '超强趋势',
      frequency: '0.1%'
    }
  }
  
  return stateConfig[state]
} 