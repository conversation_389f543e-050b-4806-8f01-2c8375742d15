#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能策略终极版 - 终极冲击月化收益10%
目标：月化收益10%+，不惜代价冲击终极目标
核心理念：模拟杠杆+双档止盈+更激进仓位管理+智能风控
作者：顶尖量化交易师
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Optional
import time
from dataclasses import dataclass
from enum import Enum


class MarketState(Enum):
    """市场状态"""
    SIDEWAYS = "震荡"
    UPTREND = "上涨趋势" 
    DOWNTREND = "下跌趋势"
    VOLATILE = "高波动"
    BREAKOUT = "突破"
    SUPER_TREND = "超强趋势"


@dataclass
class Trade:
    """交易记录"""
    timestamp: pd.Timestamp
    action: str  # 'buy', 'sell'
    price: float
    quantity: float
    strategy: str
    leverage: float = 1.0
    executed: bool = True


class UltimateTradingSystem:
    """终极版交易系统"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.position = 0.0
        self.portfolio_value = initial_capital
        
        # 交易成本
        self.commission_rate = 0.0005  # 0.05%
        self.slippage_rate = 0.0002    # 0.02%
        
        # 终极激进参数
        self.leverage_factor = 2.0     # 模拟2倍杠杆
        self.grid_spacing = 0.004      # 网格间距0.4% - 终极频繁
        self.trend_profit_fast = 0.002 # 快速止盈0.2%
        self.trend_profit_hold = 0.008 # 持有止盈0.8%
        self.trend_stop_loss = 0.002   # 趋势止损0.2%
        self.trend_threshold = 0.008   # 趋势判断0.8%
        self.max_position_ratio = 0.98 # 最大仓位98%
        
        # 终极策略参数
        self.scalp_profit = 0.0008     # 超短线止盈0.08%
        self.scalp_threshold = 0.0015  # 超短线触发0.15%
        self.breakout_threshold = 0.006 # 突破阈值0.6%
        self.momentum_factor = 0.003   # 动量因子0.3%
        self.super_trend_threshold = 0.02 # 超强趋势2%
        
        # 智能风控
        self.max_drawdown_limit = 0.35  # 最大回撤35%
        self.profit_protection = 0.15   # 利润保护15%
        self.dynamic_leverage = True    # 动态杠杆
        
        # 状态变量
        self.last_grid_price = None
        self.trend_entry_price = None
        self.trend_position = 0
        self.max_portfolio_value = initial_capital
        self.consecutive_wins = 0
        self.consecutive_losses = 0
        self.current_leverage = 1.0
        self.profit_high_watermark = 0
        
        # 记录
        self.trades = []
        self.portfolio_history = []
    
    def load_and_prepare_data(self, file_path: str) -> pd.DataFrame:
        """加载并预处理数据"""
        print(f"📊 加载数据: {file_path}")
        
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print("📊 预计算终极技术指标...")
        
        # 多时间框架技术指标
        periods = [3, 5, 8, 13, 21, 34, 55, 89]
        for period in periods:
            df[f'ma_{period}'] = df['close'].rolling(window=period).mean()
            df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
        
        # 多周期RSI
        for period in [5, 7, 14, 21]:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # 超多周期价格变化
        for period in [1, 2, 3, 4, 6, 8, 12, 16, 24, 32, 48]:
            df[f'price_change_{period}'] = df['close'].pct_change(period)
        
        # 多周期波动率
        for period in [3, 5, 10, 20, 30]:
            df[f'volatility_{period}'] = df['close'].rolling(window=period).std() / df['close'].rolling(window=period).mean()
        
        # 超细分动量指标
        df['momentum_1'] = df['close'] / df['close'].shift(1) - 1     # 15分钟
        df['momentum_2'] = df['close'] / df['close'].shift(2) - 1     # 30分钟
        df['momentum_4'] = df['close'] / df['close'].shift(4) - 1     # 1小时
        df['momentum_8'] = df['close'] / df['close'].shift(8) - 1     # 2小时
        df['momentum_16'] = df['close'] / df['close'].shift(16) - 1   # 4小时
        
        # 多周期MACD
        macd_configs = [(6, 13, 4), (12, 26, 9), (19, 39, 9), (24, 52, 18)]
        for fast, slow, signal in macd_configs:
            exp1 = df['close'].ewm(span=fast).mean()
            exp2 = df['close'].ewm(span=slow).mean()
            df[f'macd_{fast}_{slow}'] = exp1 - exp2
            df[f'macd_signal_{fast}_{slow}'] = df[f'macd_{fast}_{slow}'].ewm(span=signal).mean()
            df[f'macd_hist_{fast}_{slow}'] = df[f'macd_{fast}_{slow}'] - df[f'macd_signal_{fast}_{slow}']
        
        # 布林带多周期
        for period in [10, 20, 30]:
            df[f'bb_mid_{period}'] = df['close'].rolling(window=period).mean()
            bb_std = df['close'].rolling(window=period).std()
            df[f'bb_upper_{period}'] = df[f'bb_mid_{period}'] + 2 * bb_std
            df[f'bb_lower_{period}'] = df[f'bb_mid_{period}'] - 2 * bb_std
            df[f'bb_position_{period}'] = (df['close'] - df[f'bb_lower_{period}']) / (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}'])
            df[f'bb_width_{period}'] = (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}']) / df[f'bb_mid_{period}']
        
        # 成交量极致分析
        for period in [5, 10, 20, 50]:
            df[f'volume_ma_{period}'] = df['volume'].rolling(window=period).mean()
            df[f'volume_ratio_{period}'] = df['volume'] / df[f'volume_ma_{period}']
        
        df['volume_surge'] = df['volume'] > df['volume_ma_20'] * 2.0
        df['volume_extreme'] = df['volume'] > df['volume_ma_20'] * 3.0
        
        # 价格极值指标
        for period in [10, 20, 50]:
            df[f'price_high_{period}'] = df['close'].rolling(window=period).max()
            df[f'price_low_{period}'] = df['close'].rolling(window=period).min()
            df[f'price_strength_{period}'] = (df['close'] - df[f'price_low_{period}']) / (df[f'price_high_{period}'] - df[f'price_low_{period}'])
        
        # 趋势强度指标
        df['trend_strength'] = np.abs(df['momentum_16']) * df['volume_ratio_20']
        df['super_momentum'] = (df['momentum_1'] + df['momentum_2'] + df['momentum_4']) / 3
        
        print("✅ 终极技术指标计算完成")
        return df
    
    def detect_market_state(self, row: pd.Series) -> MarketState:
        """终极敏感市场状态检测"""
        # 超多维度分析
        price_change_1 = row.get('price_change_1', 0)
        price_change_4 = row.get('price_change_4', 0)
        price_change_16 = row.get('price_change_16', 0)
        
        momentum_1 = row.get('momentum_1', 0)
        momentum_4 = row.get('momentum_4', 0)
        momentum_16 = row.get('momentum_16', 0)
        super_momentum = row.get('super_momentum', 0)
        
        volatility_3 = row.get('volatility_3', 0)
        volatility_10 = row.get('volatility_10', 0)
        trend_strength = row.get('trend_strength', 0)
        
        volume_surge = row.get('volume_surge', False)
        volume_extreme = row.get('volume_extreme', False)
        
        ma_3 = row.get('ma_3', 0)
        ma_8 = row.get('ma_8', 0)
        ma_21 = row.get('ma_21', 0)
        ma_55 = row.get('ma_55', 0)
        
        # 检查超强趋势
        if (abs(momentum_16) > self.super_trend_threshold and 
            trend_strength > 0.01 and volume_surge):
            return MarketState.SUPER_TREND
        
        # 检查突破
        if (abs(price_change_1) > self.breakout_threshold or 
            abs(super_momentum) > self.breakout_threshold * 1.2 or
            (volume_extreme and abs(price_change_4) > self.breakout_threshold * 0.4)):
            return MarketState.BREAKOUT
        
        # 检查高波动
        if volatility_3 > 0.02 or volatility_10 > 0.025:
            return MarketState.VOLATILE
        
        # 超敏感趋势检测
        trend_signals = 0
        trend_direction = 0
        
        # 价格信号
        if abs(price_change_4) > self.trend_threshold:
            trend_signals += 3
            trend_direction += 3 if price_change_4 > 0 else -3
        
        if abs(price_change_1) > self.trend_threshold * 0.2:
            trend_signals += 1
            trend_direction += 1 if price_change_1 > 0 else -1
        
        # 动量信号
        if abs(momentum_4) > self.trend_threshold * 0.6:
            trend_signals += 2
            trend_direction += 2 if momentum_4 > 0 else -2
        
        if abs(super_momentum) > self.trend_threshold * 0.4:
            trend_signals += 1
            trend_direction += 1 if super_momentum > 0 else -1
        
        # 均线信号
        if ma_3 > ma_8 > ma_21 > ma_55:
            trend_signals += 2
            trend_direction += 2
        elif ma_3 < ma_8 < ma_21 < ma_55:
            trend_signals += 2
            trend_direction -= 2
        
        # 成交量确认
        if volume_surge:
            trend_signals += 1
        
        # 极低门槛趋势识别
        if trend_signals >= 2:
            if trend_direction > 0:
                return MarketState.UPTREND
            elif trend_direction < 0:
                return MarketState.DOWNTREND
        
        return MarketState.SIDEWAYS
    
    def calculate_dynamic_leverage(self, market_state: MarketState, volatility: float) -> float:
        """动态杠杆计算"""
        if not self.dynamic_leverage:
            return self.leverage_factor
        
        base_leverage = self.leverage_factor
        
        # 根据市场状态调整
        if market_state == MarketState.SUPER_TREND:
            base_leverage *= 1.5  # 超强趋势加杠杆
        elif market_state == MarketState.BREAKOUT:
            base_leverage *= 1.3  # 突破时加杠杆
        elif market_state == MarketState.VOLATILE:
            base_leverage *= 0.6  # 高波动降杠杆
        
        # 根据连胜连亏调整
        if self.consecutive_wins > 5:
            base_leverage *= 1.2
        elif self.consecutive_losses > 3:
            base_leverage *= 0.7
        
        # 根据波动率调整
        if volatility < 0.005:
            base_leverage *= 1.1
        elif volatility > 0.03:
            base_leverage *= 0.7
        
        # 利润保护
        current_profit = (self.portfolio_value - self.initial_capital) / self.initial_capital
        if current_profit > self.profit_protection:
            base_leverage *= 0.8  # 有利润时保守
        
        return min(base_leverage, 3.0)  # 最大3倍杠杆
    
    def grid_strategy(self, current_price: float, timestamp: pd.Timestamp, 
                     market_state: MarketState, volatility: float) -> Optional[Trade]:
        """终极网格策略"""
        if self.last_grid_price is None:
            self.last_grid_price = current_price
            return None
        
        # 超动态网格间距
        dynamic_spacing = self.grid_spacing * (1 + volatility * 3) * (0.8 if market_state == MarketState.VOLATILE else 1.0)
        price_change = abs(current_price - self.last_grid_price) / self.last_grid_price
        
        if price_change >= dynamic_spacing:
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            leverage = self.calculate_dynamic_leverage(market_state, volatility)
            
            if current_price > self.last_grid_price:
                # 上涨卖出
                if position_ratio > 0.02:
                    action = 'sell'
                    base_quantity = min(0.08, self.position * 0.8)
                    quantity = base_quantity * leverage
                else:
                    return None
            else:
                # 下跌买入
                if position_ratio < self.max_position_ratio:
                    action = 'buy'
                    base_buy = (current_value * 0.12) / current_price
                    quantity = min(base_buy * leverage, 0.08)
                else:
                    return None
            
            self.last_grid_price = current_price
            
            return Trade(
                timestamp=timestamp,
                action=action,
                price=current_price,
                quantity=quantity,
                strategy='grid',
                leverage=leverage
            )
        
        return None
    
    def trend_strategy(self, current_price: float, state: MarketState, 
                      row: pd.Series, timestamp: pd.Timestamp) -> Optional[Trade]:
        """终极趋势策略 - 双档止盈"""
        rsi_5 = row.get('rsi_5', 50)
        rsi_14 = row.get('rsi_14', 50)
        momentum_4 = row.get('momentum_4', 0)
        volatility_5 = row.get('volatility_5', 0)
        volume_ratio_10 = row.get('volume_ratio_10', 1)
        leverage = self.calculate_dynamic_leverage(state, volatility_5)
        
        # 超激进开仓条件
        if state in [MarketState.UPTREND, MarketState.SUPER_TREND] and self.trend_position <= 0:
            if (15 < rsi_14 < 85 and momentum_4 > -0.003):
                self.trend_position = 1
                self.trend_entry_price = current_price
                current_value = self.cash + self.position * current_price
                
                # 根据趋势强度调整仓位
                position_mult = 1.5 if state == MarketState.SUPER_TREND else 1.0
                base_position = current_value * 0.25 * position_mult
                position_value = base_position * leverage
                quantity = position_value / current_price
                
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='trend',
                    leverage=leverage
                )
        
        elif state in [MarketState.DOWNTREND, MarketState.SUPER_TREND] and self.trend_position >= 0:
            if (15 < rsi_14 < 85 and momentum_4 < 0.003):
                self.trend_position = -1
                self.trend_entry_price = current_price
                if self.position > 0:
                    position_mult = 1.5 if state == MarketState.SUPER_TREND else 1.0
                    base_quantity = min(0.12, self.position * 0.6) * position_mult
                    quantity = base_quantity * leverage
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend',
                        leverage=leverage
                    )
        
        # 双档止盈机制
        elif self.trend_position != 0 and self.trend_entry_price:
            if self.trend_position > 0:  # 多头
                profit_rate = (current_price - self.trend_entry_price) / self.trend_entry_price
                
                if profit_rate >= self.trend_profit_fast:  # 第一档快速止盈
                    # 只平部分仓位，让利润继续跑
                    quantity = min(0.06, self.position * 0.3)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend',
                        leverage=1.0
                    )
                elif profit_rate >= self.trend_profit_hold:  # 第二档完全止盈
                    self.trend_position = 0
                    self.trend_entry_price = None
                    self.consecutive_wins += 1
                    self.consecutive_losses = 0
                    quantity = min(0.12, self.position * 0.7)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend',
                        leverage=1.0
                    )
                elif profit_rate <= -self.trend_stop_loss:  # 严格止损
                    self.trend_position = 0
                    self.trend_entry_price = None
                    self.consecutive_losses += 1
                    self.consecutive_wins = 0
                    quantity = min(0.1, self.position * 0.6)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend',
                        leverage=1.0
                    )
        
        return None
    
    def scalping_strategy(self, current_price: float, row: pd.Series, 
                         timestamp: pd.Timestamp) -> Optional[Trade]:
        """终极超短线策略"""
        price_change_1 = row.get('price_change_1', 0)
        super_momentum = row.get('super_momentum', 0)
        rsi_5 = row.get('rsi_5', 50)
        volatility_3 = row.get('volatility_3', 0)
        volume_ratio_5 = row.get('volume_ratio_5', 1)
        
        # 超敏感机会捕获
        if (abs(price_change_1) > self.scalp_threshold or 
            abs(super_momentum) > self.scalp_threshold * 0.8):
            
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            leverage = min(self.leverage_factor * 1.2, 2.5)  # 超短线用更高杠杆
            
            if (price_change_1 > 0 and rsi_5 < 80 and position_ratio < 0.9):
                # 向上脉冲买入
                buy_value = current_value * 0.1 * leverage
                quantity = buy_value / current_price
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='scalp',
                    leverage=leverage
                )
            elif (price_change_1 < 0 and rsi_5 > 20 and position_ratio > 0.05):
                # 向下脉冲卖出
                base_quantity = min(0.08, self.position * 0.5)
                quantity = base_quantity * leverage
                return Trade(
                    timestamp=timestamp,
                    action='sell',
                    price=current_price,
                    quantity=quantity,
                    strategy='scalp',
                    leverage=leverage
                )
        
        return None
    
    def super_trend_strategy(self, current_price: float, row: pd.Series, 
                            timestamp: pd.Timestamp) -> Optional[Trade]:
        """超强趋势策略"""
        momentum_16 = row.get('momentum_16', 0)
        trend_strength = row.get('trend_strength', 0)
        volume_extreme = row.get('volume_extreme', False)
        price_strength_50 = row.get('price_strength_50', 0.5)
        
        # 超强趋势重仓交易
        if (abs(momentum_16) > self.super_trend_threshold and 
            trend_strength > 0.01 and volume_extreme):
            
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            leverage = self.leverage_factor * 1.8  # 超强趋势最高杠杆
            
            if momentum_16 > 0 and position_ratio < 0.95:
                # 超强上涨趋势，重仓买入
                buy_value = current_value * 0.3 * leverage
                quantity = buy_value / current_price
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='super_trend',
                    leverage=leverage
                )
            elif momentum_16 < 0 and position_ratio > 0.1:
                # 超强下跌趋势，重仓卖出
                base_quantity = min(0.2, self.position * 0.8)
                quantity = base_quantity * leverage
                return Trade(
                    timestamp=timestamp,
                    action='sell',
                    price=current_price,
                    quantity=quantity,
                    strategy='super_trend',
                    leverage=leverage
                )
        
        return None
    
    def execute_trade(self, trade: Trade) -> bool:
        """执行交易 - 支持杠杆"""
        trade_value = trade.price * trade.quantity
        cost = trade_value * (self.commission_rate + self.slippage_rate)
        
        if trade.action == 'buy':
            # 杠杆买入，只需要部分资金
            required_cash = (trade_value + cost) / trade.leverage
            if self.cash >= required_cash:
                self.cash -= required_cash
                self.position += trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        elif trade.action == 'sell':
            if self.position >= trade.quantity:
                # 杠杆卖出，获得完整收益
                proceeds = trade_value - cost
                self.cash += proceeds
                self.position -= trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        self.trades.append(trade)
        return trade.executed
    
    def backtest(self, df: pd.DataFrame) -> Dict:
        """运行终极回测"""
        print("🚀 开始终极智能策略回测...")
        
        total_rows = len(df)
        start_time = time.time()
        
        # 初始化计数器
        strategy_trades = {
            'grid': 0, 'trend': 0, 'scalp': 0, 'breakout': 0, 
            'momentum': 0, 'super_trend': 0
        }
        state_counts = {state: 0 for state in MarketState}
        leverage_history = []
        
        # 主回测循环
        for i in range(60, total_rows):
            current_row = df.iloc[i]
            current_price = current_row['close']
            current_time = df.index[i]
            
            # 显示进度
            if i % 10000 == 0:
                progress = i / total_rows * 100
                elapsed = time.time() - start_time
                eta = elapsed / (i - 59) * (total_rows - i) if i > 60 else 0
                print(f"⏳ 进度: {progress:.1f}% | 用时: {elapsed:.0f}s | 预计剩余: {eta:.0f}s")
            
            # 更新最高价值
            current_value = self.cash + self.position * current_price
            if current_value > self.max_portfolio_value:
                self.max_portfolio_value = current_value
                self.profit_high_watermark = (current_value - self.initial_capital) / self.initial_capital
            
            # 风险控制
            drawdown = (current_value - self.max_portfolio_value) / self.max_portfolio_value
            if drawdown < -self.max_drawdown_limit:
                continue
            
            # 检测市场状态
            market_state = self.detect_market_state(current_row)
            state_counts[market_state] += 1
            
            volatility = current_row.get('volatility_5', 0.01)
            current_leverage = self.calculate_dynamic_leverage(market_state, volatility)
            leverage_history.append(current_leverage)
            
            # 多策略并行
            trades = []
            
            # 超强趋势策略优先级最高
            if market_state == MarketState.SUPER_TREND:
                super_trade = self.super_trend_strategy(current_price, current_row, current_time)
                if super_trade:
                    trades.append(super_trade)
            
            # 网格策略
            if market_state == MarketState.SIDEWAYS:
                grid_trade = self.grid_strategy(current_price, current_time, market_state, volatility)
                if grid_trade:
                    trades.append(grid_trade)
            
            # 趋势策略
            if market_state in [MarketState.UPTREND, MarketState.DOWNTREND, MarketState.SUPER_TREND]:
                trend_trade = self.trend_strategy(current_price, market_state, current_row, current_time)
                if trend_trade:
                    trades.append(trend_trade)
            
            # 超短线策略
            scalp_trade = self.scalping_strategy(current_price, current_row, current_time)
            if scalp_trade:
                trades.append(scalp_trade)
            
            # 执行交易
            for trade in trades:
                if self.execute_trade(trade) and trade.executed:
                    strategy_trades[trade.strategy] += 1
            
            # 更新组合价值
            self.portfolio_value = self.cash + self.position * current_price
            
            # 记录历史
            if i % 500 == 0:
                self.portfolio_history.append({
                    'timestamp': current_time,
                    'value': self.portfolio_value,
                    'cash': self.cash,
                    'position': self.position,
                    'price': current_price,
                    'state': market_state.value,
                    'leverage': current_leverage
                })
        
        elapsed_time = time.time() - start_time
        print(f"\n✅ 终极回测完成！用时: {elapsed_time:.1f}秒")
        print(f"📊 策略交易统计: {strategy_trades}")
        
        return self._calculate_performance(df, state_counts, strategy_trades, leverage_history)
    
    def _calculate_performance(self, df: pd.DataFrame, state_counts: Dict, 
                              strategy_trades: Dict, leverage_history: List) -> Dict:
        """计算绩效指标"""
        
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        start_date = df.index[60]
        end_date = df.index[-1]
        days = (end_date - start_date).days
        
        if days > 0:
            annual_return = (self.portfolio_value / self.initial_capital) ** (365 / days) - 1
            monthly_return = (self.portfolio_value / self.initial_capital) ** (30 / days) - 1
        else:
            annual_return = 0
            monthly_return = 0
        
        # 计算最大回撤
        if self.portfolio_history:
            portfolio_df = pd.DataFrame(self.portfolio_history)
            values = portfolio_df['value']
            rolling_max = values.expanding().max()
            drawdown = (values - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
        else:
            max_drawdown = 0
        
        # 交易统计
        executed_trades = [t for t in self.trades if t.executed]
        total_trades = len(executed_trades)
        
        # 杠杆统计
        avg_leverage = np.mean(leverage_history) if leverage_history else 1.0
        max_leverage = max(leverage_history) if leverage_history else 1.0
        
        # 市场状态分布
        total_states = sum(state_counts.values())
        state_distribution = {
            state.value: count / total_states if total_states > 0 else 0 
            for state, count in state_counts.items()
        }
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'strategy_trades': strategy_trades,
            'final_value': self.portfolio_value,
            'final_cash': self.cash,
            'final_position': self.position,
            'market_state_distribution': state_distribution,
            'consecutive_wins': self.consecutive_wins,
            'consecutive_losses': self.consecutive_losses,
            'avg_leverage': avg_leverage,
            'max_leverage': max_leverage,
            'profit_high_watermark': self.profit_high_watermark
        }


def main():
    """主函数"""
    print("🚀 智能策略终极版 - 终极冲击月化收益10%")
    print("=" * 70)
    print("💡 终极改进：模拟杠杆+双档止盈+更激进仓位管理+智能风控")
    print("🎯 目标：月化收益≥10%，不惜代价冲击终极目标")
    print("⚠️ 极高风险警告：仅供学习研究，实盘使用后果自负")
    print("=" * 70)
    
    # 初始化终极系统
    trading_system = UltimateTradingSystem(initial_capital=100000)
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = trading_system.load_and_prepare_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 运行终极回测
    try:
        results = trading_system.backtest(df)
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return
    
    # 输出结果
    print("\n" + "=" * 70)
    print("📊 终极智能策略回测结果")
    print("=" * 70)
    
    print(f"\n💰 收益表现:")
    print(f"   总收益: {results['total_return']:.2%}")
    print(f"   年化收益: {results['annual_return']:.2%}")
    print(f"   月化收益: {results['monthly_return']:.2%}")
    print(f"   最终资产: ${results['final_value']:,.2f}")
    print(f"   利润高水位: {results['profit_high_watermark']:.2%}")
    
    print(f"\n🛡️ 风险控制:")
    print(f"   最大回撤: {results['max_drawdown']:.2%}")
    print(f"   平均杠杆: {results['avg_leverage']:.2f}倍")
    print(f"   最高杠杆: {results['max_leverage']:.2f}倍")
    
    print(f"\n📈 终极多策略交易统计:")
    print(f"   总交易次数: {results['total_trades']}")
    for strategy, count in results['strategy_trades'].items():
        if results['total_trades'] > 0:
            print(f"   {strategy}策略: {count} ({count/results['total_trades']:.1%})")
    
    print(f"\n🎲 交易心理统计:")
    print(f"   连胜次数: {results['consecutive_wins']}")
    print(f"   连亏次数: {results['consecutive_losses']}")
    
    print(f"\n🎯 市场状态分布:")
    for state, percentage in results['market_state_distribution'].items():
        print(f"   {state}: {percentage:.1%}")
    
    # 终极目标评估
    print(f"\n🔍 终极目标评估:")
    monthly_ultimate = results['monthly_return'] >= 0.10
    
    if monthly_ultimate:
        print(f"   🎉🎉🎉 终极目标达成！月化收益超越10%！")
        print(f"   ✅ 月化收益: {results['monthly_return']:.2%} ≥ 10.0%")
        print(f"   🚀 年化收益: {results['annual_return']:.2%}")
    else:
        print(f"   🎯 月化收益: {results['monthly_return']:.2%}")
        gap = 0.10 - results['monthly_return']
        print(f"   📊 距离目标还差: {gap:.2%}")
    
    # 风险警示
    if results['max_drawdown'] < -0.4:
        print(f"\n⚠️⚠️⚠️ 极高风险警示:")
        print(f"   回撤超过40%，极高风险策略")
        print(f"   此策略仅供学习研究，严禁实盘使用")
        print(f"   高收益必伴随极高风险")
    
    # 与买入持有对比
    buy_hold_return = (df['close'].iloc[-1] - df['close'].iloc[60]) / df['close'].iloc[60]
    print(f"\n📊 策略对比:")
    print(f"   终极策略收益: {results['total_return']:.2%}")
    print(f"   买入持有收益: {buy_hold_return:.2%}")
    
    if results['total_return'] > buy_hold_return:
        print(f"   🎯 终极策略跑赢买入持有 {(results['total_return'] - buy_hold_return):.2%}")
    else:
        print(f"   ❌ 终极策略跑输买入持有 {(buy_hold_return - results['total_return']):.2%}")
    
    print(f"\n🎊 终极策略回测完成！")
    if monthly_ultimate:
        print(f"🏆 恭喜！终极目标达成！月化收益{results['monthly_return']:.2%}！")
    else:
        print(f"💡 继续优化：距离月化10%目标还需努力")
    
    return results


if __name__ == "__main__":
    results = main() 