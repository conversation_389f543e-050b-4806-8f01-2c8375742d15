# 核心数据处理
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# 机器学习
scikit-learn>=1.1.0
xgboost>=1.6.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0

# 回测框架
backtrader>=**********
zipline-reloaded>=2.2.0

# 技术指标
TA-Lib>=0.4.24
pandas-ta>=0.3.14b

# 数据获取
yfinance>=0.1.74
ccxt>=2.0.0
requests>=2.28.0

# 并行计算
joblib>=1.2.0
numba>=0.56.0

# 测试
pytest>=7.1.0
pytest-cov>=3.0.0

# 工具
tqdm>=4.64.0
python-dotenv>=0.20.0
pyyaml>=6.0

# Jupyter (可选)
jupyter>=1.0.0
ipywidgets>=7.7.0

# 数据库 (可选)
sqlalchemy>=1.4.0
pymongo>=4.2.0 