import React from 'react'
import { NavLink } from 'react-router-dom'
import {
  BarChart3,
  Monitor,
  Settings,
  FileText,
  Activity,
  Target,
  Shield,
  ScrollText,
  History
} from 'lucide-react'

interface NavItem {
  name: string
  path: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string
}

const navItems: NavItem[] = [
  {
    name: '仪表盘',
    path: '/',
    icon: BarChart3,
  },
  {
    name: '实时监控',
    path: '/monitor',
    icon: Monitor,
    badge: 'Live'
  },
  {
    name: '策略配置',
    path: '/strategy',
    icon: Target,
  },
  {
    name: '风控管理',
    path: '/risk',
    icon: Shield,
  },
  {
    name: '交易历史',
    path: '/history',
    icon: History,
  },
  {
    name: '性能分析',
    path: '/analytics',
    icon: Activity,
  },
  {
    name: '日志',
    path: '/logs',
    icon: ScrollText,
  },
  {
    name: '报告中心',
    path: '/reports',
    icon: FileText,
  },
  {
    name: '系统设置',
    path: '/settings',
    icon: Settings,
  },
]

export function Sidebar() {
  return (
    <div className="w-64 bg-card border-r border-border">
      {/* Logo区域 */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-primary flex items-center justify-center">
            <BarChart3 className="w-5 h-5 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-lg font-semibold">量化交易系统</h1>
            <p className="text-xs text-muted-foreground">v1.0 Alpha</p>
          </div>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="p-4">
        <div className="sidebar-nav">
          {navItems.map((item) => {
            const Icon = item.icon
            return (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) =>
                  `sidebar-nav-item ${isActive ? 'active' : ''}`
                }
              >
                <Icon className="w-5 h-5" />
                <span className="flex-1">{item.name}</span>
                {item.badge && (
                  <span className="px-2 py-0.5 text-xs bg-primary text-primary-foreground rounded-full">
                    {item.badge}
                  </span>
                )}
              </NavLink>
            )
          })}
        </div>
      </nav>

      {/* 底部状态 */}
      <div className="absolute bottom-0 left-0 right-0 p-4">
        <div className="flex items-center gap-3 px-3 py-2 rounded-lg bg-muted/50 w-fit">
          <div className="pulse-dot success"></div>
          <div>
            <p className="text-sm font-medium">系统运行正常</p>
            <p className="text-xs text-muted-foreground">连接时间: 2h 34m</p>
          </div>
        </div>
      </div>
    </div>
  )
} 