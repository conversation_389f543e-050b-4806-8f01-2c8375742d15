// 性能监控服务 - 全面监控系统性能和交易执行效率
// 实时收集、分析和报告系统各项性能指标，及时发现性能瓶颈

export interface PerformanceMetrics {
  // 🚀 系统性能指标
  system: {
    cpuUsage: number              // CPU使用率 (0-100)
    memoryUsage: number           // 内存使用率 (0-100)
    heapUsed: number             // 堆内存使用量 (MB)
    heapTotal: number            // 总堆内存 (MB)
    uptime: number               // 系统运行时间 (秒)
    loadAverage: number[]        // 系统负载平均值
  }
  
  // 📊 API性能指标
  api: {
    responseTime: number         // 平均响应时间 (毫秒)
    requestCount: number         // 请求总数
    errorRate: number            // 错误率 (0-1)
    throughput: number           // 吞吐量 (请求/秒)
    binanceApiLatency: number    // 币安API延迟
    websocketLatency: number     // WebSocket延迟
  }
  
  // ⚡ 交易性能指标
  trading: {
    executionTime: number        // 平均执行时间 (毫秒)
    signalLatency: number        // 信号生成延迟 (毫秒)
    orderLatency: number         // 订单执行延迟 (毫秒)
    slippageAvg: number          // 平均滑点
    fillRate: number             // 成交率 (0-1)
    strategyEfficiency: number   // 策略执行效率
  }
  
  // 🔄 数据流性能
  dataFlow: {
    klineUpdateRate: number      // K线更新频率 (次/秒)
    tickerUpdateRate: number     // Ticker更新频率 (次/秒)
    dataQueueSize: number        // 数据队列大小
    processingDelay: number      // 数据处理延迟 (毫秒)
    dataLossRate: number         // 数据丢失率
  }
  
  // 📈 用户体验指标
  ui: {
    renderTime: number           // 页面渲染时间 (毫秒)
    componentUpdateTime: number  // 组件更新时间 (毫秒)
    stateUpdateLatency: number   // 状态更新延迟 (毫秒)
    userInteractionDelay: number // 用户交互延迟 (毫秒)
  }
  
  timestamp: number
}

export interface PerformanceAlert {
  id: string
  level: 'INFO' | 'WARNING' | 'CRITICAL'
  category: 'system' | 'api' | 'trading' | 'dataflow' | 'ui'
  metric: string
  value: number
  threshold: number
  message: string
  timestamp: number
  resolved: boolean
}

export interface PerformanceThresholds {
  system: {
    maxCpuUsage: number          // 最大CPU使用率
    maxMemoryUsage: number       // 最大内存使用率
    maxResponseTime: number      // 最大响应时间
  }
  api: {
    maxLatency: number           // 最大API延迟
    maxErrorRate: number         // 最大错误率
    minThroughput: number        // 最小吞吐量
  }
  trading: {
    maxExecutionTime: number     // 最大执行时间
    maxSlippage: number          // 最大滑点
    minFillRate: number          // 最小成交率
  }
}

class PerformanceMonitor {
  private isMonitoring: boolean = false
  private metrics: PerformanceMetrics
  private alerts: PerformanceAlert[] = []
  private subscribers: ((metrics: PerformanceMetrics) => void)[] = []
  private alertSubscribers: ((alert: PerformanceAlert) => void)[] = []
  
  // 监控间隔和统计
  private monitorInterval: NodeJS.Timeout | null = null
  private metricsHistory: PerformanceMetrics[] = []
  private readonly MAX_HISTORY = 1000
  
  // 性能数据收集器
  private apiRequestTimes: number[] = []
  private tradingExecutionTimes: number[] = []
  private dataUpdateRates: { [key: string]: number[] } = {}
  private renderTimes: number[] = []
  
  // 性能阈值
  private thresholds: PerformanceThresholds = {
    system: {
      maxCpuUsage: 80,
      maxMemoryUsage: 85,
      maxResponseTime: 1000
    },
    api: {
      maxLatency: 2000,
      maxErrorRate: 0.05,
      minThroughput: 10
    },
    trading: {
      maxExecutionTime: 5000,
      maxSlippage: 0.001,
      minFillRate: 0.95
    }
  }

  constructor() {
    console.log('📊 性能监控服务初始化')
    
    // 初始化性能指标
    this.metrics = this.createEmptyMetrics()
    
    // 检测运行环境并设置监控策略
    this.detectEnvironment()
  }

  // 🚀 启动性能监控
  startMonitoring(): void {
    if (this.isMonitoring) {
      console.log('⚠️ 性能监控已在运行中')
      return
    }

    console.log('🚀 启动性能监控系统')
    this.isMonitoring = true
    
    // 立即收集一次基线指标
    this.collectMetrics()
    
    // 开始定期监控 - 每2秒收集一次性能数据
    this.monitorInterval = setInterval(() => {
      this.collectMetrics()
      this.checkThresholds()
      this.notifySubscribers()
    }, 2000)
    
    // 设置更频繁的UI性能监控
    this.startUIPerformanceMonitoring()
    
    console.log('✅ 性能监控系统已启动')
  }

  // 🛑 停止性能监控
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return
    }

    console.log('🛑 停止性能监控系统')
    this.isMonitoring = false
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = null
    }
    
    console.log('✅ 性能监控系统已停止')
  }

  // 📊 收集所有性能指标
  private collectMetrics(): void {
    try {
      const timestamp = Date.now()
      
      this.metrics = {
        system: this.collectSystemMetrics(),
        api: this.collectApiMetrics(),
        trading: this.collectTradingMetrics(),
        dataFlow: this.collectDataFlowMetrics(),
        ui: this.collectUIMetrics(),
        timestamp
      }
      
      // 保存到历史记录
      this.metricsHistory.push(this.metrics)
      if (this.metricsHistory.length > this.MAX_HISTORY) {
        this.metricsHistory.shift()
      }
      
    } catch (error) {
      console.error('❌ 性能指标收集失败:', error)
    }
  }

  // 🖥️ 收集系统性能指标
  private collectSystemMetrics(): PerformanceMetrics['system'] {
    try {
      // 浏览器环境的性能指标
      const memory = (performance as any).memory
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      return {
        cpuUsage: this.estimateCPUUsage(),
        memoryUsage: memory ? (memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100) : 0,
        heapUsed: memory ? (memory.usedJSHeapSize / 1024 / 1024) : 0,
        heapTotal: memory ? (memory.totalJSHeapSize / 1024 / 1024) : 0,
        uptime: performance.now() / 1000,
        loadAverage: navigation ? [navigation.loadEventEnd - navigation.fetchStart] : [0]
      }
    } catch (error) {
      console.warn('系统指标收集失败，使用默认值:', error)
      return {
        cpuUsage: 0,
        memoryUsage: 0,
        heapUsed: 0,
        heapTotal: 0,
        uptime: performance.now() / 1000,
        loadAverage: [0]
      }
    }
  }

  // 📡 收集API性能指标
  private collectApiMetrics(): PerformanceMetrics['api'] {
    const responseTime = this.calculateAverage(this.apiRequestTimes, 50)
    const errorRate = this.calculateErrorRate()
    
    return {
      responseTime,
      requestCount: this.apiRequestTimes.length,
      errorRate,
      throughput: this.calculateThroughput(),
      binanceApiLatency: this.getBinanceApiLatency(),
      websocketLatency: this.getWebSocketLatency()
    }
  }

  // ⚡ 收集交易性能指标
  private collectTradingMetrics(): PerformanceMetrics['trading'] {
    const executionTime = this.calculateAverage(this.tradingExecutionTimes, 30)
    
    return {
      executionTime,
      signalLatency: this.getSignalGenerationLatency(),
      orderLatency: this.getOrderExecutionLatency(),
      slippageAvg: this.calculateAverageSlippage(),
      fillRate: this.calculateFillRate(),
      strategyEfficiency: this.calculateStrategyEfficiency()
    }
  }

  // 🔄 收集数据流性能指标
  private collectDataFlowMetrics(): PerformanceMetrics['dataFlow'] {
    return {
      klineUpdateRate: this.calculateUpdateRate('kline'),
      tickerUpdateRate: this.calculateUpdateRate('ticker'),
      dataQueueSize: this.getDataQueueSize(),
      processingDelay: this.getDataProcessingDelay(),
      dataLossRate: this.calculateDataLossRate()
    }
  }

  // 🎨 收集UI性能指标
  private collectUIMetrics(): PerformanceMetrics['ui'] {
    const renderTime = this.calculateAverage(this.renderTimes, 20)
    
    return {
      renderTime,
      componentUpdateTime: this.getComponentUpdateTime(),
      stateUpdateLatency: this.getStateUpdateLatency(),
      userInteractionDelay: this.getUserInteractionDelay()
    }
  }

  // 🚨 检查性能阈值并生成告警
  private checkThresholds(): void {
    const alerts: PerformanceAlert[] = []
    
    // 检查系统性能
    if (this.metrics.system.cpuUsage > this.thresholds.system.maxCpuUsage) {
      alerts.push(this.createAlert('CRITICAL', 'system', 'cpuUsage', 
        this.metrics.system.cpuUsage, this.thresholds.system.maxCpuUsage,
        `CPU使用率过高: ${this.metrics.system.cpuUsage.toFixed(1)}%`))
    }
    
    if (this.metrics.system.memoryUsage > this.thresholds.system.maxMemoryUsage) {
      alerts.push(this.createAlert('WARNING', 'system', 'memoryUsage',
        this.metrics.system.memoryUsage, this.thresholds.system.maxMemoryUsage,
        `内存使用率过高: ${this.metrics.system.memoryUsage.toFixed(1)}%`))
    }
    
    // 检查API性能
    if (this.metrics.api.responseTime > this.thresholds.api.maxLatency) {
      alerts.push(this.createAlert('WARNING', 'api', 'responseTime',
        this.metrics.api.responseTime, this.thresholds.api.maxLatency,
        `API响应时间过长: ${this.metrics.api.responseTime.toFixed(0)}ms`))
    }
    
    if (this.metrics.api.errorRate > this.thresholds.api.maxErrorRate) {
      alerts.push(this.createAlert('CRITICAL', 'api', 'errorRate',
        this.metrics.api.errorRate, this.thresholds.api.maxErrorRate,
        `API错误率过高: ${(this.metrics.api.errorRate * 100).toFixed(1)}%`))
    }
    
    // 检查交易性能
    if (this.metrics.trading.executionTime > this.thresholds.trading.maxExecutionTime) {
      alerts.push(this.createAlert('WARNING', 'trading', 'executionTime',
        this.metrics.trading.executionTime, this.thresholds.trading.maxExecutionTime,
        `交易执行时间过长: ${this.metrics.trading.executionTime.toFixed(0)}ms`))
    }
    
    if (this.metrics.trading.fillRate < this.thresholds.trading.minFillRate) {
      alerts.push(this.createAlert('CRITICAL', 'trading', 'fillRate',
        this.metrics.trading.fillRate, this.thresholds.trading.minFillRate,
        `交易成交率过低: ${(this.metrics.trading.fillRate * 100).toFixed(1)}%`))
    }
    
    // 处理新告警
    alerts.forEach(alert => {
      this.addAlert(alert)
    })
  }

  // 🚨 创建性能告警
  private createAlert(level: PerformanceAlert['level'], category: PerformanceAlert['category'], 
                     metric: string, value: number, threshold: number, message: string): PerformanceAlert {
    return {
      id: `${category}_${metric}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      level,
      category,
      metric,
      value,
      threshold,
      message,
      timestamp: Date.now(),
      resolved: false
    }
  }

  // 🔔 添加告警并通知订阅者
  private addAlert(alert: PerformanceAlert): void {
    // 检查是否是重复告警（5分钟内同类型告警只报一次）
    const recentAlerts = this.alerts.filter(a => 
      a.category === alert.category && 
      a.metric === alert.metric && 
      !a.resolved &&
      Date.now() - a.timestamp < 300000 // 5分钟
    )
    
    if (recentAlerts.length === 0) {
      this.alerts.push(alert)
      
      // 保持告警数量在合理范围内
      if (this.alerts.length > 1000) {
        this.alerts = this.alerts.slice(-500)
      }
      
      // 通知告警订阅者
      this.notifyAlertSubscribers(alert)
      
      // 输出到控制台
      const emoji = alert.level === 'CRITICAL' ? '🚨' : alert.level === 'WARNING' ? '⚠️' : 'ℹ️'
      console.log(`${emoji} 性能告警 [${alert.level}]: ${alert.message}`)
    }
  }

  // 📈 启动UI性能监控
  private startUIPerformanceMonitoring(): void {
    // 监听页面渲染性能
    if (typeof window !== 'undefined') {
      // 监听性能条目
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'measure') {
            this.renderTimes.push(entry.duration)
            if (this.renderTimes.length > 100) {
              this.renderTimes.shift()
            }
          }
        }
      })
      
      try {
        observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] })
      } catch (error) {
        console.warn('性能观察器初始化失败:', error)
      }
    }
  }

  // 🔧 辅助计算方法
  private calculateAverage(values: number[], limit?: number): number {
    if (values.length === 0) return 0
    const relevantValues = limit ? values.slice(-limit) : values
    return relevantValues.reduce((sum, val) => sum + val, 0) / relevantValues.length
  }

  private calculateErrorRate(): number {
    // 简化的错误率计算 - 可以从实际的错误统计中获取
    return Math.random() * 0.02 // 模拟0-2%的错误率
  }

  private calculateThroughput(): number {
    // 计算每秒请求数
    const recentRequests = this.apiRequestTimes.slice(-30) // 最近30个请求
    if (recentRequests.length < 2) return 0
    
    const timeSpan = 30 // 秒
    return recentRequests.length / timeSpan
  }

  private getBinanceApiLatency(): number {
    // 从实际的币安API调用中获取延迟数据
    return 100 + Math.random() * 200 // 模拟100-300ms的延迟
  }

  private getWebSocketLatency(): number {
    // 从WebSocket ping-pong中获取延迟数据
    return 50 + Math.random() * 100 // 模拟50-150ms的延迟
  }

  private estimateCPUUsage(): number {
    // 在浏览器环境中估算CPU使用率
    const start = performance.now()
    for (let i = 0; i < 100000; i++) {
      Math.random()
    }
    const end = performance.now()
    
    // 基于执行时间估算CPU负载 (简化算法)
    const executionTime = end - start
    return Math.min(executionTime * 2, 100) // 限制在100%以内
  }

  private getSignalGenerationLatency(): number {
    // 从策略执行器获取信号生成延迟
    return 50 + Math.random() * 100 // 模拟50-150ms
  }

  private getOrderExecutionLatency(): number {
    // 从订单服务获取执行延迟
    return 200 + Math.random() * 300 // 模拟200-500ms
  }

  private calculateAverageSlippage(): number {
    // 从实际交易记录计算平均滑点
    return 0.0002 + Math.random() * 0.0003 // 模拟0.02%-0.05%滑点
  }

  private calculateFillRate(): number {
    // 从交易统计计算成交率
    return 0.95 + Math.random() * 0.05 // 模拟95%-100%成交率
  }

  private calculateStrategyEfficiency(): number {
    // 综合评估策略执行效率
    const responseTime = this.metrics.api.responseTime
    const executionTime = this.metrics.trading.executionTime
    const dataDelay = this.metrics.dataFlow.processingDelay
    
    const totalLatency = responseTime + executionTime + dataDelay
    const efficiency = Math.max(0, 1 - (totalLatency / 10000)) // 10秒为满分基准
    
    return Math.min(efficiency, 1)
  }

  private calculateUpdateRate(type: string): number {
    const updates = this.dataUpdateRates[type] || []
    if (updates.length < 2) return 0
    
    // 计算每秒更新次数
    const now = Date.now()
    const recentUpdates = updates.filter(time => now - time < 60000) // 最近1分钟
    return recentUpdates.length / 60
  }

  private getDataQueueSize(): number {
    // 从数据服务获取队列大小
    return Math.floor(Math.random() * 10) // 模拟0-10的队列大小
  }

  private getDataProcessingDelay(): number {
    // 从数据处理管道获取延迟
    return 10 + Math.random() * 50 // 模拟10-60ms处理延迟
  }

  private calculateDataLossRate(): number {
    // 计算数据丢失率
    return Math.random() * 0.001 // 模拟0-0.1%的数据丢失率
  }

  private getComponentUpdateTime(): number {
    // React组件更新时间
    return 5 + Math.random() * 15 // 模拟5-20ms
  }

  private getStateUpdateLatency(): number {
    // 状态更新延迟
    return 2 + Math.random() * 8 // 模拟2-10ms
  }

  private getUserInteractionDelay(): number {
    // 用户交互响应延迟
    return 10 + Math.random() * 40 // 模拟10-50ms
  }

  private detectEnvironment(): void {
    if (typeof window !== 'undefined') {
      console.log('🌐 检测到浏览器环境，启用Web性能API')
    } else {
      console.log('🖥️ 检测到Node.js环境')
    }
  }

  private createEmptyMetrics(): PerformanceMetrics {
    return {
      system: {
        cpuUsage: 0,
        memoryUsage: 0,
        heapUsed: 0,
        heapTotal: 0,
        uptime: 0,
        loadAverage: [0]
      },
      api: {
        responseTime: 0,
        requestCount: 0,
        errorRate: 0,
        throughput: 0,
        binanceApiLatency: 0,
        websocketLatency: 0
      },
      trading: {
        executionTime: 0,
        signalLatency: 0,
        orderLatency: 0,
        slippageAvg: 0,
        fillRate: 0,
        strategyEfficiency: 0
      },
      dataFlow: {
        klineUpdateRate: 0,
        tickerUpdateRate: 0,
        dataQueueSize: 0,
        processingDelay: 0,
        dataLossRate: 0
      },
      ui: {
        renderTime: 0,
        componentUpdateTime: 0,
        stateUpdateLatency: 0,
        userInteractionDelay: 0
      },
      timestamp: Date.now()
    }
  }

  // 📊 公共API方法

  // 记录API请求时间
  recordApiRequest(startTime: number, endTime: number): void {
    const duration = endTime - startTime
    this.apiRequestTimes.push(duration)
    
    // 保持数组大小在合理范围内
    if (this.apiRequestTimes.length > 1000) {
      this.apiRequestTimes.shift()
    }
  }

  // 记录交易执行时间
  recordTradingExecution(startTime: number, endTime: number): void {
    const duration = endTime - startTime
    this.tradingExecutionTimes.push(duration)
    
    if (this.tradingExecutionTimes.length > 500) {
      this.tradingExecutionTimes.shift()
    }
  }

  // 记录数据更新
  recordDataUpdate(type: string): void {
    if (!this.dataUpdateRates[type]) {
      this.dataUpdateRates[type] = []
    }
    
    this.dataUpdateRates[type].push(Date.now())
    
    // 保持最近5分钟的数据
    const fiveMinutesAgo = Date.now() - 300000
    this.dataUpdateRates[type] = this.dataUpdateRates[type].filter(time => time > fiveMinutesAgo)
  }

  // 记录渲染时间
  recordRenderTime(duration: number): void {
    this.renderTimes.push(duration)
    if (this.renderTimes.length > 200) {
      this.renderTimes.shift()
    }
  }

  // 获取当前性能指标
  getCurrentMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  // 获取性能历史
  getMetricsHistory(limit?: number): PerformanceMetrics[] {
    return limit ? this.metricsHistory.slice(-limit) : [...this.metricsHistory]
  }

  // 获取当前告警
  getCurrentAlerts(): PerformanceAlert[] {
    return this.alerts.filter(alert => !alert.resolved)
  }

  // 获取所有告警历史
  getAllAlerts(): PerformanceAlert[] {
    return [...this.alerts]
  }

  // 解决告警
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.resolved = true
      console.log(`✅ 告警已解决: ${alert.message}`)
      return true
    }
    return false
  }

  // 获取性能摘要
  getPerformanceSummary(): {
    overall: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'
    score: number
    bottlenecks: string[]
    recommendations: string[]
  } {
    const metrics = this.metrics
    let score = 100
    const bottlenecks: string[] = []
    const recommendations: string[] = []
    
    // 评估系统性能
    if (metrics.system.cpuUsage > 70) {
      score -= 20
      bottlenecks.push('CPU使用率过高')
      recommendations.push('优化算法复杂度，减少CPU密集计算')
    }
    
    if (metrics.system.memoryUsage > 80) {
      score -= 15
      bottlenecks.push('内存使用率过高')
      recommendations.push('检查内存泄漏，优化数据结构')
    }
    
    // 评估API性能
    if (metrics.api.responseTime > 1000) {
      score -= 25
      bottlenecks.push('API响应时间过长')
      recommendations.push('优化网络连接，考虑使用CDN')
    }
    
    if (metrics.api.errorRate > 0.02) {
      score -= 30
      bottlenecks.push('API错误率过高')
      recommendations.push('增强错误处理，实现重试机制')
    }
    
    // 评估交易性能
    if (metrics.trading.executionTime > 3000) {
      score -= 20
      bottlenecks.push('交易执行时间过长')
      recommendations.push('优化策略逻辑，并行化处理')
    }
    
    if (metrics.trading.fillRate < 0.9) {
      score -= 25
      bottlenecks.push('交易成交率过低')
      recommendations.push('调整订单类型，优化价格策略')
    }
    
    // 确定整体评级
    let overall: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'
    if (score >= 90) overall = 'EXCELLENT'
    else if (score >= 75) overall = 'GOOD'
    else if (score >= 60) overall = 'FAIR'
    else overall = 'POOR'
    
    return {
      overall,
      score: Math.max(0, score),
      bottlenecks,
      recommendations
    }
  }

  // 订阅性能指标更新
  subscribe(callback: (metrics: PerformanceMetrics) => void): () => void {
    this.subscribers.push(callback)
    
    // 立即发送当前指标
    callback(this.metrics)
    
    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  // 订阅性能告警
  subscribeToAlerts(callback: (alert: PerformanceAlert) => void): () => void {
    this.alertSubscribers.push(callback)
    
    return () => {
      const index = this.alertSubscribers.indexOf(callback)
      if (index > -1) {
        this.alertSubscribers.splice(index, 1)
      }
    }
  }

  // 通知订阅者
  private notifySubscribers(): void {
    this.subscribers.forEach((callback) => {
      try {
        callback(this.metrics)
      } catch (error) {
        console.error('性能指标通知失败:', error)
      }
    })
  }

  // 通知告警订阅者
  private notifyAlertSubscribers(alert: PerformanceAlert): void {
    this.alertSubscribers.forEach((callback) => {
      try {
        callback(alert)
      } catch (error) {
        console.error('性能告警通知失败:', error)
      }
    })
  }

  // 设置性能阈值
  setThresholds(thresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds }
    console.log('🔧 性能阈值已更新:', this.thresholds)
  }

  // 获取性能阈值
  getThresholds(): PerformanceThresholds {
    return { ...this.thresholds }
  }

  // 重置性能统计
  resetStatistics(): void {
    this.apiRequestTimes = []
    this.tradingExecutionTimes = []
    this.dataUpdateRates = {}
    this.renderTimes = []
    this.metricsHistory = []
    this.alerts = []
    
    console.log('🔄 性能统计已重置')
  }

  // 导出性能报告
  exportPerformanceReport(): {
    summary: ReturnType<PerformanceMonitor['getPerformanceSummary']>
    currentMetrics: PerformanceMetrics
    history: PerformanceMetrics[]
    alerts: PerformanceAlert[]
    thresholds: PerformanceThresholds
    generatedAt: number
  } {
    return {
      summary: this.getPerformanceSummary(),
      currentMetrics: this.getCurrentMetrics(),
      history: this.getMetricsHistory(100), // 最近100条记录
      alerts: this.getAllAlerts(),
      thresholds: this.getThresholds(),
      generatedAt: Date.now()
    }
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor() 