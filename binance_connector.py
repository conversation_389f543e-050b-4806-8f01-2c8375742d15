#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Binance API连接模板
"""

import requests
import hashlib
import hmac
import time
from typing import Dict, Optional
import json

class BinanceConnector:
    """Binance API连接器"""
    
    def __init__(self, api_key: str = "", secret_key: str = "", testnet: bool = True):
        self.api_key = api_key
        self.secret_key = secret_key
        self.testnet = testnet
        
        if testnet:
            self.base_url = "https://testnet.binance.vision"
        else:
            self.base_url = "https://api.binance.com"
    
    def _generate_signature(self, params: Dict) -> str:
        """生成API签名"""
        if not self.secret_key:
            return ""
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def get_account_info(self) -> Dict:
        """获取账户信息"""
        if not self.api_key:
            return {"error": "API Key未配置"}
        
        endpoint = "/api/v3/account"
        params = {"timestamp": int(time.time() * 1000)}
        params["signature"] = self._generate_signature(params)
        
        headers = {"X-MBX-APIKEY": self.api_key}
        
        try:
            response = requests.get(
                self.base_url + endpoint,
                params=params,
                headers=headers,
                timeout=10
            )
            return response.json()
        except Exception as e:
            return {"error": f"请求失败: {e}"}
    
    def get_ticker_price(self, symbol: str = "BTCUSDT") -> Dict:
        """获取价格信息"""
        endpoint = "/api/v3/ticker/price"
        params = {"symbol": symbol}
        
        try:
            response = requests.get(
                self.base_url + endpoint,
                params=params,
                timeout=10
            )
            return response.json()
        except Exception as e:
            return {"error": f"价格获取失败: {e}"}
    
    def place_test_order(self, symbol: str, side: str, quantity: float) -> Dict:
        """下测试订单"""
        if not self.api_key:
            return {"error": "API Key未配置"}
        
        endpoint = "/api/v3/order/test"
        params = {
            "symbol": symbol,
            "side": side,
            "type": "MARKET",
            "quantity": quantity,
            "timestamp": int(time.time() * 1000)
        }
        params["signature"] = self._generate_signature(params)
        
        headers = {"X-MBX-APIKEY": self.api_key}
        
        try:
            response = requests.post(
                self.base_url + endpoint,
                params=params,
                headers=headers,
                timeout=10
            )
            return response.json()
        except Exception as e:
            return {"error": f"订单失败: {e}"}

# 使用示例
if __name__ == "__main__":
    # 初始化连接器 (测试网)
    connector = BinanceConnector(testnet=True)
    
    # 获取BTC价格
    price_info = connector.get_ticker_price("BTCUSDT")
    print(f"BTC价格: {price_info}")
    
    # 注意: 实盘使用需要配置API密钥
    print("\n⚠️ 实盘使用说明:")
    print("1. 在Binance创建API密钥")
    print("2. 设置IP白名单")
    print("3. 配置API Key和Secret")
    print("4. 先在测试网验证")
