#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级量化交易策略回测系统 v2.0
目标：筛选回撤≤15%，月化收益≥10%的最优策略
使用纯Python实现技术指标，无需TA-Lib依赖
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta


class TechnicalIndicators:
    """技术指标计算类"""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """简单移动平均线"""
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """指数移动平均线"""
        return data.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """相对强弱指标"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD指标"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """布林带"""
        sma = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """随机指标KD"""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        return k_percent, d_percent
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """威廉指标"""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        return -100 * ((highest_high - close) / (highest_high - lowest_low))
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """平均真实波动范围"""
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        return tr.rolling(window=period).mean()


class AdvancedBacktester:
    """高级回测引擎"""
    
    def __init__(self, initial_capital: float = 100000, 
                 commission_rate: float = 0.0015,
                 slippage_rate: float = 0.0005):
        """
        初始化回测引擎
        
        Args:
            initial_capital: 初始资金
            commission_rate: 手续费率 (0.15%)
            slippage_rate: 滑点率 (0.05%)
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        self.data = None
        self.results = {}
        
    def load_data(self, csv_path: str) -> pd.DataFrame:
        """加载K线数据"""
        print(f"📊 正在加载数据: {csv_path}")
        
        # 读取数据
        df = pd.read_csv(csv_path)
        
        # 数据预处理
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        # 计算技术指标
        df = self._calculate_indicators(df)
        
        self.data = df
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print(f"📅 时间范围: {df.index[0]} 至 {df.index[-1]}")
        
        return df
        
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # 移动平均线
        df['ma_5'] = TechnicalIndicators.sma(close, 5)
        df['ma_10'] = TechnicalIndicators.sma(close, 10)
        df['ma_20'] = TechnicalIndicators.sma(close, 20)
        df['ma_50'] = TechnicalIndicators.sma(close, 50)
        df['ma_200'] = TechnicalIndicators.sma(close, 200)
        
        # 指数移动平均线
        df['ema_12'] = TechnicalIndicators.ema(close, 12)
        df['ema_26'] = TechnicalIndicators.ema(close, 26)
        df['ema_50'] = TechnicalIndicators.ema(close, 50)
        
        # MACD
        df['macd'], df['macd_signal'], df['macd_hist'] = TechnicalIndicators.macd(close)
        
        # RSI
        df['rsi_14'] = TechnicalIndicators.rsi(close, 14)
        df['rsi_21'] = TechnicalIndicators.rsi(close, 21)
        
        # 布林带
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = TechnicalIndicators.bollinger_bands(close, 20)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (close - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # KDJ
        df['k'], df['d'] = TechnicalIndicators.stochastic(high, low, close)
        df['j'] = 3 * df['k'] - 2 * df['d']
        
        # 威廉指标
        df['williams_r'] = TechnicalIndicators.williams_r(high, low, close, 14)
        
        # ATR (真实波动范围)
        df['atr'] = TechnicalIndicators.atr(high, low, close, 14)
        
        # 成交量指标
        df['volume_ma'] = TechnicalIndicators.sma(volume, 20)
        df['volume_ratio'] = volume / df['volume_ma']
        
        # 价格变化率
        df['price_change'] = df['close'].pct_change()
        df['price_change_5'] = df['close'].pct_change(5)
        
        return df


class TradingStrategies:
    """交易策略集合"""
    
    @staticmethod
    def dual_ma_crossover(df: pd.DataFrame, fast_period: int = 10, slow_period: int = 20) -> pd.DataFrame:
        """双均线交叉策略"""
        signals = pd.DataFrame(index=df.index)
        
        fast_ma = df[f'ma_{fast_period}']
        slow_ma = df[f'ma_{slow_period}']
        
        # 金叉买入，死叉卖出
        signals['position'] = np.where(fast_ma > slow_ma, 1, -1)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def macd_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """MACD策略"""
        signals = pd.DataFrame(index=df.index)
        
        # MACD金叉死叉
        macd_cross = np.where(df['macd'] > df['macd_signal'], 1, -1)
        
        # 添加RSI过滤
        rsi_filter = np.where((df['rsi_14'] > 30) & (df['rsi_14'] < 70), 1, 0)
        
        signals['position'] = macd_cross * rsi_filter
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def rsi_mean_reversion(df: pd.DataFrame, rsi_low: int = 25, rsi_high: int = 75) -> pd.DataFrame:
        """RSI均值回归策略"""
        signals = pd.DataFrame(index=df.index)
        
        # RSI超买超卖
        buy_signal = df['rsi_14'] < rsi_low
        sell_signal = df['rsi_14'] > rsi_high
        
        signals['position'] = 0
        signals.loc[buy_signal, 'position'] = 1
        signals.loc[sell_signal, 'position'] = -1
        
        # 前向填充持仓
        signals['position'] = signals['position'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def bollinger_bands_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """布林带策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 价格触及下轨买入，上轨卖出
        buy_condition = (df['close'] <= df['bb_lower']) & (df['rsi_14'] < 40)
        sell_condition = (df['close'] >= df['bb_upper']) & (df['rsi_14'] > 60)
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = -1
        
        signals['position'] = signals['position'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def triple_ma_trend(df: pd.DataFrame) -> pd.DataFrame:
        """三均线趋势策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 短中长均线排列
        bullish = (df['ma_5'] > df['ma_20']) & (df['ma_20'] > df['ma_50'])
        bearish = (df['ma_5'] < df['ma_20']) & (df['ma_20'] < df['ma_50'])
        
        # 结合RSI确认
        rsi_confirm_bull = df['rsi_14'] > 45
        rsi_confirm_bear = df['rsi_14'] < 55
        
        signals['position'] = 0
        signals.loc[bullish & rsi_confirm_bull, 'position'] = 1
        signals.loc[bearish & rsi_confirm_bear, 'position'] = -1
        
        signals['position'] = signals['position'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def kdj_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """KDJ策略"""
        signals = pd.DataFrame(index=df.index)
        
        # KDJ金叉死叉 + 位置确认
        buy_condition = (df['k'] > df['d']) & (df['j'] < 20) & (df['k'] < 30)
        sell_condition = (df['k'] < df['d']) & (df['j'] > 80) & (df['k'] > 70)
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = -1
        
        signals['position'] = signals['position'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def volume_price_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """量价配合策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 量价配合确认
        volume_surge = df['volume_ratio'] > 1.5
        price_breakout = df['close'] > df['ma_20']
        price_breakdown = df['close'] < df['ma_20']
        
        buy_condition = volume_surge & price_breakout & (df['rsi_14'] > 50)
        sell_condition = volume_surge & price_breakdown & (df['rsi_14'] < 50)
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = -1
        
        signals['position'] = signals['position'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def momentum_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """动量策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 计算价格动量
        price_momentum = df['close'].pct_change(10)  # 10期价格变化
        volume_momentum = df['volume'].pct_change(5)  # 5期成交量变化
        
        # 买入条件：价格上涨 + 成交量放大 + RSI未超买
        buy_condition = (price_momentum > 0.02) & (volume_momentum > 0.2) & (df['rsi_14'] < 70)
        
        # 卖出条件：价格下跌 + 成交量放大 + RSI未超卖
        sell_condition = (price_momentum < -0.02) & (volume_momentum > 0.2) & (df['rsi_14'] > 30)
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = -1
        
        signals['position'] = signals['position'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def atr_breakout_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """ATR突破策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 基于ATR的突破信号
        upper_breakout = df['close'] > (df['ma_20'] + 1.5 * df['atr'])
        lower_breakout = df['close'] < (df['ma_20'] - 1.5 * df['atr'])
        
        # 结合成交量确认
        volume_confirm = df['volume_ratio'] > 1.2
        
        buy_condition = upper_breakout & volume_confirm & (df['rsi_14'] > 50)
        sell_condition = lower_breakout & volume_confirm & (df['rsi_14'] < 50)
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = -1
        
        signals['position'] = signals['position'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals


class PerformanceAnalyzer:
    """绩效分析器"""
    
    @staticmethod
    def calculate_returns(df: pd.DataFrame, signals: pd.DataFrame, 
                         initial_capital: float, commission_rate: float, 
                         slippage_rate: float) -> Dict:
        """计算策略收益"""
        
        # 计算持仓收益
        df_copy = df.copy()
        df_copy['position'] = signals['position'].fillna(0)
        df_copy['signal'] = signals['signal'].fillna(0)
        
        # 价格变化
        df_copy['price_change'] = df_copy['close'].pct_change()
        
        # 策略收益（考虑手续费和滑点）
        df_copy['strategy_return'] = df_copy['position'].shift(1) * df_copy['price_change']
        
        # 交易成本
        trade_mask = df_copy['signal'] != 0
        total_cost_rate = commission_rate + slippage_rate
        df_copy.loc[trade_mask, 'strategy_return'] -= total_cost_rate
        
        # 累计收益
        df_copy['cumulative_return'] = (1 + df_copy['strategy_return']).cumprod()
        df_copy['portfolio_value'] = initial_capital * df_copy['cumulative_return']
        
        # 计算绩效指标
        total_return = df_copy['cumulative_return'].iloc[-1] - 1
        
        # 年化收益率
        days = (df_copy.index[-1] - df_copy.index[0]).days
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        
        # 月化收益率
        monthly_return = (1 + total_return) ** (30 / days) - 1 if days > 0 else 0
        
        # 最大回撤
        rolling_max = df_copy['portfolio_value'].expanding().max()
        drawdown = (df_copy['portfolio_value'] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 夏普比率
        risk_free_rate = 0.02  # 假设无风险利率2%
        excess_return = df_copy['strategy_return'] - risk_free_rate / (365 * 24 * 4)  # 15分钟数据
        sharpe_ratio = excess_return.mean() / excess_return.std() * np.sqrt(365 * 24 * 4) if excess_return.std() > 0 else 0
        
        # 胜率
        winning_trades = (df_copy['strategy_return'] > 0).sum()
        total_trades = (df_copy['strategy_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 交易次数
        trade_count = (df_copy['signal'].abs() > 0).sum()
        
        # 最大连续亏损
        losses = df_copy['strategy_return'] < 0
        max_consecutive_losses = 0
        current_losses = 0
        for loss in losses:
            if loss:
                current_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, current_losses)
            else:
                current_losses = 0
        
        # 计算收益波动率
        returns_std = df_copy['strategy_return'].std()
        annual_volatility = returns_std * np.sqrt(365 * 24 * 4)
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'win_rate': win_rate,
            'trade_count': trade_count,
            'max_consecutive_losses': max_consecutive_losses,
            'annual_volatility': annual_volatility,
            'final_value': df_copy['portfolio_value'].iloc[-1],
            'portfolio_series': df_copy['portfolio_value'],
            'drawdown_series': drawdown
        }


def main():
    """主函数：执行策略回测和筛选"""
    
    print("🚀 启动高级量化策略回测系统 v2.0")
    print("=" * 50)
    
    # 初始化回测引擎
    backester = AdvancedBacktester(
        initial_capital=100000,
        commission_rate=0.0015,  # 0.15% 手续费
        slippage_rate=0.0005     # 0.05% 滑点
    )
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = backester.load_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 策略列表
    strategies = {
        '双均线交叉(10,20)': lambda df: TradingStrategies.dual_ma_crossover(df, 10, 20),
        '双均线交叉(5,10)': lambda df: TradingStrategies.dual_ma_crossover(df, 5, 10),
        '双均线交叉(20,50)': lambda df: TradingStrategies.dual_ma_crossover(df, 20, 50),
        'MACD策略': TradingStrategies.macd_strategy,
        'RSI均值回归(25,75)': lambda df: TradingStrategies.rsi_mean_reversion(df, 25, 75),
        'RSI均值回归(20,80)': lambda df: TradingStrategies.rsi_mean_reversion(df, 20, 80),
        '布林带策略': TradingStrategies.bollinger_bands_strategy,
        '三均线趋势': TradingStrategies.triple_ma_trend,
        'KDJ策略': TradingStrategies.kdj_strategy,
        '量价配合策略': TradingStrategies.volume_price_strategy,
        '动量策略': TradingStrategies.momentum_strategy,
        'ATR突破策略': TradingStrategies.atr_breakout_strategy
    }
    
    # 回测所有策略
    results = {}
    print("\n📈 开始策略回测...")
    
    for strategy_name, strategy_func in strategies.items():
        print(f"\n🔍 测试策略: {strategy_name}")
        
        try:
            # 生成交易信号
            signals = strategy_func(df)
            
            # 计算绩效
            performance = PerformanceAnalyzer.calculate_returns(
                df, signals, backester.initial_capital,
                backester.commission_rate, backester.slippage_rate
            )
            
            results[strategy_name] = performance
            
            print(f"   ✅ 总收益: {performance['total_return']:.2%}")
            print(f"   📊 月化收益: {performance['monthly_return']:.2%}")
            print(f"   📉 最大回撤: {performance['max_drawdown']:.2%}")
            print(f"   🎯 夏普比率: {performance['sharpe_ratio']:.2f}")
            print(f"   🎲 胜率: {performance['win_rate']:.2%}")
            print(f"   🔄 交易次数: {performance['trade_count']}")
            
        except Exception as e:
            print(f"   ❌ 策略 {strategy_name} 回测失败: {e}")
    
    # 筛选符合条件的策略
    print("\n" + "=" * 60)
    print("🎯 策略筛选结果 (回撤≤15%, 月化收益≥10%)")
    print("=" * 60)
    
    qualified_strategies = {}
    for name, perf in results.items():
        if perf['max_drawdown'] >= -0.15 and perf['monthly_return'] >= 0.10:
            qualified_strategies[name] = perf
    
    if not qualified_strategies:
        print("❌ 没有策略完全符合筛选条件 (回撤≤15%, 月化收益≥10%)")
        print("\n🔍 让我们降低标准，寻找接近条件的策略:")
        print("\n📊 所有策略表现概览 (按夏普比率排序):")
        
        # 按夏普比率排序
        sorted_results = sorted(results.items(), key=lambda x: x[1]['sharpe_ratio'], reverse=True)
        
        # 创建结果表格
        summary_data = []
        for name, perf in sorted_results:
            summary_data.append({
                '策略名称': name,
                '总收益': f"{perf['total_return']:.2%}",
                '月化收益': f"{perf['monthly_return']:.2%}",
                '最大回撤': f"{perf['max_drawdown']:.2%}",
                '夏普比率': f"{perf['sharpe_ratio']:.2f}",
                '胜率': f"{perf['win_rate']:.2%}",
                '交易次数': perf['trade_count'],
                '年化波动': f"{perf['annual_volatility']:.2%}"
            })
        
        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False))
        
        # 寻找最优策略 (综合评分)
        print(f"\n🏆 综合评分最高的策略:")
        best_name, best_perf = sorted_results[0]
        print(f"   策略名称: {best_name}")
        print(f"   💰 总收益: {best_perf['total_return']:.2%}")
        print(f"   📈 月化收益: {best_perf['monthly_return']:.2%}")
        print(f"   📉 最大回撤: {best_perf['max_drawdown']:.2%}")
        print(f"   ⚡ 夏普比率: {best_perf['sharpe_ratio']:.2f}")
        print(f"   🎯 胜率: {best_perf['win_rate']:.2%}")
        print(f"   💎 最终资产: ${best_perf['final_value']:,.2f}")
        
        # 寻找回撤控制最好的策略
        best_drawdown_strategy = min(results.items(), key=lambda x: abs(x[1]['max_drawdown']))
        print(f"\n🛡️ 风险控制最佳策略: {best_drawdown_strategy[0]}")
        print(f"   最大回撤: {best_drawdown_strategy[1]['max_drawdown']:.2%}")
        print(f"   月化收益: {best_drawdown_strategy[1]['monthly_return']:.2%}")
        
    else:
        print(f"✅ 找到 {len(qualified_strategies)} 个符合条件的策略:")
        
        # 按夏普比率排序找出最优策略
        best_strategy = max(qualified_strategies.items(), 
                          key=lambda x: x[1]['sharpe_ratio'])
        
        print(f"\n🏆 最优策略: {best_strategy[0]}")
        print(f"   💰 总收益: {best_strategy[1]['total_return']:.2%}")
        print(f"   📈 月化收益: {best_strategy[1]['monthly_return']:.2%}")
        print(f"   📉 最大回撤: {best_strategy[1]['max_drawdown']:.2%}")
        print(f"   ⚡ 夏普比率: {best_strategy[1]['sharpe_ratio']:.2f}")
        print(f"   🎯 胜率: {best_strategy[1]['win_rate']:.2%}")
        print(f"   🔄 交易次数: {best_strategy[1]['trade_count']}")
        print(f"   💎 最终资产: ${best_strategy[1]['final_value']:,.2f}")
        
        # 详细结果表格
        print(f"\n📋 符合条件的策略详情:")
        qualified_data = []
        for name, perf in qualified_strategies.items():
            qualified_data.append({
                '策略名称': name,
                '总收益': f"{perf['total_return']:.2%}",
                '月化收益': f"{perf['monthly_return']:.2%}",
                '最大回撤': f"{perf['max_drawdown']:.2%}",
                '夏普比率': f"{perf['sharpe_ratio']:.2f}",
                '胜率': f"{perf['win_rate']:.2%}",
                '交易次数': perf['trade_count'],
                '最终资产': f"${perf['final_value']:,.0f}"
            })
        
        qualified_df = pd.DataFrame(qualified_data)
        qualified_df = qualified_df.sort_values('夏普比率', ascending=False)
        print(qualified_df.to_string(index=False))
    
    # 数据统计信息
    print(f"\n📊 数据统计信息:")
    print(f"   📅 回测时间跨度: {df.index[0]} 至 {df.index[-1]}")
    print(f"   📈 数据点数量: {len(df)} 个15分钟K线")
    print(f"   🕒 总回测天数: {(df.index[-1] - df.index[0]).days} 天")
    print(f"   📊 BTC价格范围: ${df['close'].min():,.2f} - ${df['close'].max():,.2f}")
    
    print(f"\n🎊 回测完成！")
    
    return results, qualified_strategies if qualified_strategies else None


if __name__ == "__main__":
    main() 