// 交易记录数据库服务
// 使用IndexedDB存储交易历史数据，便于分析和优化

import { MarketState } from './strategyExecutor'

// 交易记录数据结构
export interface TradeRecord {
  id: string                    // 唯一标识
  timestamp: number            // 时间戳
  symbol: string               // 交易对
  action: 'open' | 'close'     // 开仓/平仓
  side: 'buy' | 'sell'         // 买入/卖出
  price: number                // 成交价格
  quantity: number             // 数量
  leverage: number             // 杠杆倍数
  strategy: string             // 策略名称
  confidence: number           // 信号置信度
  reason: string               // 交易原因
  
  // 技术指标快照
  indicators: {
    rsi: number                // RSI指标
    ema: number                // EMA指标
    momentum: number           // 动量指标
    volatility: number         // 波动率
    volume: number             // 成交量
    marketState: MarketState   // 市场状态
    trendStrength: number      // 趋势强度
    supportLevel: number       // 支撑位
    resistanceLevel: number    // 阻力位
  }
  
  // 账户状态快照
  accountSnapshot: {
    totalEquity: number        // 总权益
    availableMargin: number    // 可用保证金
    usedMargin: number         // 已用保证金
    unrealizedPnl: number      // 未实现盈亏
    realizedPnl: number        // 已实现盈亏
    marginRatio: number        // 保证金比率
    positions: number          // 持仓数量
  }
  
  // 风险管理信息
  riskMetrics: {
    stopLossPrice?: number     // 止损价格
    takeProfitPrice?: number   // 止盈价格
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    drawdown: number           // 当前回撤
    exposureRatio: number      // 仓位暴露比例
    correlationRisk: number    // 相关性风险
  }
  
  // 执行结果
  execution: {
    orderId?: string           // 订单ID
    fillPrice: number          // 实际成交价格
    fillQuantity: number       // 实际成交数量
    commission: number         // 手续费
    slippage: number           // 滑点
    executionTime: number      // 执行耗时(ms)
    status: 'success' | 'failed' | 'partial'
    errorMessage?: string      // 错误信息
  }
  
  // 关联信息
  relatedTrades: string[]      // 关联交易ID
  parentTradeId?: string       // 父交易ID(用于止盈止损)
  isClosingTrade: boolean      // 是否为平仓交易
  pnl?: number                 // 此笔交易盈亏
}

// 策略性能汇总
export interface StrategyPerformance {
  id: string
  strategy: string
  symbol: string
  period: {
    start: number
    end: number
  }
  
  // 交易统计
  stats: {
    totalTrades: number
    winningTrades: number
    losingTrades: number
    winRate: number
    avgWin: number
    avgLoss: number
    profitFactor: number
    
    // 收益指标
    totalReturn: number
    annualizedReturn: number
    sharpeRatio: number
    maxDrawdown: number
    calmarRatio: number
    
    // 风险指标
    volatility: number
    var95: number              // 95% VaR
    maxConsecutiveLosses: number
    recoveryFactor: number
    
    // 执行质量
    avgSlippage: number
    avgCommission: number
    executionScore: number
  }
  
  // 按市场状态分类的表现
  performanceByMarket: {
    [key in MarketState]: {
      trades: number
      winRate: number
      avgReturn: number
      maxDrawdown: number
    }
  }
  
  // 时间分布
  timeDistribution: {
    hourly: number[]           // 24小时分布
    weekday: number[]          // 星期分布
    monthly: number[]          // 月度分布
  }
}

// 市场分析记录
export interface MarketAnalysis {
  id: string
  timestamp: number
  symbol: string
  
  // 价格数据
  priceData: {
    open: number
    high: number
    low: number
    close: number
    volume: number
    vwap: number               // 成交量加权平均价
  }
  
  // 技术分析
  technicalIndicators: {
    trend: {
      sma20: number
      sma50: number
      ema12: number
      ema26: number
      macd: number
      signal: number
      histogram: number
    }
    momentum: {
      rsi: number
      stochK: number
      stochD: number
      cci: number
      williams: number
    }
    volatility: {
      bb_upper: number
      bb_middle: number
      bb_lower: number
      atr: number
      volatility: number
    }
    volume: {
      obv: number              // 累积能量线
      volumeRate: number       // 量比
      moneyFlow: number        // 资金流量
    }
  }
  
  // 市场微观结构
  microstructure: {
    bidAskSpread: number
    marketDepth: number
    orderBookImbalance: number
    tickDirection: number
    liquidityScore: number
  }
  
  // 情绪指标
  sentiment: {
    fearGreedIndex: number
    volatilitySkew: number
    putCallRatio: number
    marginalBuyerStrength: number
  }
}

class TradingDatabase {
  private db: IDBDatabase | null = null
  private readonly DB_NAME = 'QuantTradingDB'
  private readonly DB_VERSION = 1
  
  constructor() {
    this.initDatabase()
  }

  // 初始化数据库
  private async initDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION)
      
      request.onerror = () => {
        console.error('❌ 数据库连接失败:', request.error)
        reject(request.error)
      }
      
      request.onsuccess = () => {
        this.db = request.result
        console.log('✅ 交易数据库连接成功')
        resolve()
      }
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        // 创建交易记录表
        if (!db.objectStoreNames.contains('trades')) {
          const tradeStore = db.createObjectStore('trades', { keyPath: 'id' })
          tradeStore.createIndex('timestamp', 'timestamp', { unique: false })
          tradeStore.createIndex('symbol', 'symbol', { unique: false })
          tradeStore.createIndex('strategy', 'strategy', { unique: false })
          tradeStore.createIndex('action', 'action', { unique: false })
          console.log('📊 创建交易记录表')
        }
        
        // 创建策略性能表
        if (!db.objectStoreNames.contains('performance')) {
          const perfStore = db.createObjectStore('performance', { keyPath: 'id' })
          perfStore.createIndex('strategy', 'strategy', { unique: false })
          perfStore.createIndex('symbol', 'symbol', { unique: false })
          perfStore.createIndex('period', 'period.start', { unique: false })
          console.log('📈 创建策略性能表')
        }
        
        // 创建市场分析表
        if (!db.objectStoreNames.contains('market_analysis')) {
          const analysisStore = db.createObjectStore('market_analysis', { keyPath: 'id' })
          analysisStore.createIndex('timestamp', 'timestamp', { unique: false })
          analysisStore.createIndex('symbol', 'symbol', { unique: false })
          console.log('🔍 创建市场分析表')
        }
        
        console.log('🚀 数据库结构初始化完成')
      }
    })
  }

  // 记录交易
  async recordTrade(trade: TradeRecord): Promise<void> {
    if (!this.db) {
      console.error('❌ 数据库未连接')
      return
    }
    
    try {
      const transaction = this.db.transaction(['trades'], 'readwrite')
      const store = transaction.objectStore('trades')
      
      await new Promise<void>((resolve, reject) => {
        const request = store.add(trade)
        request.onsuccess = () => {
          console.log(`✅ 交易记录已保存: ${trade.id}`)
          console.log(`📊 ${trade.action} ${trade.side} ${trade.quantity} ${trade.symbol} @ ${trade.price}`)
          console.log(`🎯 策略: ${trade.strategy} | 置信度: ${(trade.confidence * 100).toFixed(1)}%`)
          console.log(`📈 技术指标: RSI=${trade.indicators.rsi.toFixed(1)} EMA=${trade.indicators.ema.toFixed(2)} 波动率=${(trade.indicators.volatility * 100).toFixed(2)}%`)
          resolve()
        }
        request.onerror = () => reject(request.error)
      })
      
    } catch (error) {
      console.error('❌ 交易记录保存失败:', error)
    }
  }

  // 记录策略性能
  async recordPerformance(performance: StrategyPerformance): Promise<void> {
    if (!this.db) return
    
    try {
      const transaction = this.db.transaction(['performance'], 'readwrite')
      const store = transaction.objectStore('performance')
      
      await new Promise<void>((resolve, reject) => {
        const request = store.put(performance) // 使用put支持更新
        request.onsuccess = () => {
          console.log(`✅ 策略性能已更新: ${performance.strategy}`)
          console.log(`📊 胜率: ${performance.stats.winRate.toFixed(1)}% | 盈利因子: ${performance.stats.profitFactor.toFixed(2)}`)
          console.log(`📈 年化收益: ${(performance.stats.annualizedReturn * 100).toFixed(2)}% | 最大回撤: ${(performance.stats.maxDrawdown * 100).toFixed(2)}%`)
          resolve()
        }
        request.onerror = () => reject(request.error)
      })
      
    } catch (error) {
      console.error('❌ 策略性能记录失败:', error)
    }
  }

  // 记录市场分析
  async recordMarketAnalysis(analysis: MarketAnalysis): Promise<void> {
    if (!this.db) return
    
    try {
      const transaction = this.db.transaction(['market_analysis'], 'readwrite')
      const store = transaction.objectStore('market_analysis')
      
      await new Promise<void>((resolve, reject) => {
        const request = store.add(analysis)
        request.onsuccess = () => {
          console.log(`✅ 市场分析已保存: ${analysis.symbol}`)
          resolve()
        }
        request.onerror = () => reject(request.error)
      })
      
    } catch (error) {
      console.error('❌ 市场分析记录失败:', error)
    }
  }

  // 查询交易记录
  async getTrades(filters: {
    symbol?: string
    strategy?: string
    startTime?: number
    endTime?: number
    limit?: number
  } = {}): Promise<TradeRecord[]> {
    if (!this.db) return []
    
    try {
      const transaction = this.db.transaction(['trades'], 'readonly')
      const store = transaction.objectStore('trades')
      
      let request: IDBRequest
      if (filters.symbol) {
        request = store.index('symbol').getAll(filters.symbol)
      } else {
        request = store.getAll()
      }
      
      const trades = await new Promise<TradeRecord[]>((resolve, reject) => {
        request.onsuccess = () => resolve(request.result)
        request.onerror = () => reject(request.error)
      })
      
      // 应用过滤器
      let filteredTrades = trades
      
      if (filters.strategy) {
        filteredTrades = filteredTrades.filter(t => t.strategy === filters.strategy)
      }
      
      if (filters.startTime) {
        filteredTrades = filteredTrades.filter(t => t.timestamp >= filters.startTime!)
      }
      
      if (filters.endTime) {
        filteredTrades = filteredTrades.filter(t => t.timestamp <= filters.endTime!)
      }
      
      // 按时间倒序排列
      filteredTrades.sort((a, b) => b.timestamp - a.timestamp)
      
      if (filters.limit) {
        filteredTrades = filteredTrades.slice(0, filters.limit)
      }
      
      return filteredTrades
      
    } catch (error) {
      console.error('❌ 查询交易记录失败:', error)
      return []
    }
  }

  // 查询策略性能
  async getPerformance(strategy?: string, symbol?: string): Promise<StrategyPerformance[]> {
    if (!this.db) return []
    
    try {
      const transaction = this.db.transaction(['performance'], 'readonly')
      const store = transaction.objectStore('performance')
      
      const performances = await new Promise<StrategyPerformance[]>((resolve, reject) => {
        const request = store.getAll()
        request.onsuccess = () => resolve(request.result)
        request.onerror = () => reject(request.error)
      })
      
      let filtered = performances
      if (strategy) {
        filtered = filtered.filter(p => p.strategy === strategy)
      }
      if (symbol) {
        filtered = filtered.filter(p => p.symbol === symbol)
      }
      
      return filtered
      
    } catch (error) {
      console.error('❌ 查询策略性能失败:', error)
      return []
    }
  }

  // 获取交易统计
  async getTradeStatistics(symbol?: string, strategy?: string): Promise<{
    totalTrades: number
    winRate: number
    avgProfit: number
    maxDrawdown: number
    profitFactor: number
    recentTrades: TradeRecord[]
  }> {
    const trades = await this.getTrades({ symbol, strategy, limit: 1000 })
    
    if (trades.length === 0) {
      return {
        totalTrades: 0,
        winRate: 0,
        avgProfit: 0,
        maxDrawdown: 0,
        profitFactor: 0,
        recentTrades: []
      }
    }
    
    // 计算交易对
    const tradePairs = this.matchTradePairs(trades)
    const profits = tradePairs.map(pair => pair.pnl).filter(pnl => pnl !== undefined) as number[]
    
    const winningTrades = profits.filter(p => p > 0).length
    const winRate = profits.length > 0 ? (winningTrades / profits.length) * 100 : 0
    
    const avgProfit = profits.length > 0 ? profits.reduce((a, b) => a + b, 0) / profits.length : 0
    
    const wins = profits.filter(p => p > 0)
    const losses = profits.filter(p => p < 0)
    const avgWin = wins.length > 0 ? wins.reduce((a, b) => a + b, 0) / wins.length : 0
    const avgLoss = losses.length > 0 ? Math.abs(losses.reduce((a, b) => a + b, 0) / losses.length) : 0
    const profitFactor = avgLoss > 0 ? avgWin / avgLoss : 0
    
    // 计算最大回撤
    let equity = 0
    let maxEquity = 0
    let maxDrawdown = 0
    
    for (const profit of profits) {
      equity += profit
      if (equity > maxEquity) {
        maxEquity = equity
      }
      const drawdown = maxEquity > 0 ? (maxEquity - equity) / maxEquity : 0
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown
      }
    }
    
    return {
      totalTrades: trades.length,
      winRate,
      avgProfit,
      maxDrawdown: maxDrawdown * 100,
      profitFactor,
      recentTrades: trades.slice(0, 10)
    }
  }

  // 匹配交易对
  private matchTradePairs(trades: TradeRecord[]): Array<{openTrade: TradeRecord, closeTrade?: TradeRecord, pnl?: number}> {
    const pairs: Array<{openTrade: TradeRecord, closeTrade?: TradeRecord, pnl?: number}> = []
    const openTrades: TradeRecord[] = []
    
    for (const trade of trades.sort((a, b) => a.timestamp - b.timestamp)) {
      if (trade.action === 'open') {
        openTrades.push(trade)
      } else if (trade.action === 'close') {
        // 找到对应的开仓交易
        const openTradeIndex = openTrades.findIndex(ot => 
          ot.symbol === trade.symbol && 
          ot.side !== trade.side
        )
        
        if (openTradeIndex !== -1) {
          const openTrade = openTrades.splice(openTradeIndex, 1)[0]
          const pnl = this.calculatePnL(openTrade, trade)
          pairs.push({ openTrade, closeTrade: trade, pnl })
        }
      }
    }
    
    // 添加未平仓的交易
    for (const openTrade of openTrades) {
      pairs.push({ openTrade })
    }
    
    return pairs
  }

  // 计算盈亏
  private calculatePnL(openTrade: TradeRecord, closeTrade: TradeRecord): number {
    const direction = openTrade.side === 'buy' ? 1 : -1
    const priceChange = closeTrade.price - openTrade.price
    const pnl = direction * priceChange * openTrade.quantity * openTrade.leverage
    
    // 扣除手续费
    const commission = (openTrade.execution.commission || 0) + (closeTrade.execution.commission || 0)
    
    return pnl - commission
  }

  // 清理旧数据
  async cleanupOldData(days: number = 90): Promise<void> {
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000)
    
    if (!this.db) return
    
    try {
      const transaction = this.db.transaction(['trades', 'market_analysis'], 'readwrite')
      
      // 清理旧交易记录
      const tradeStore = transaction.objectStore('trades')
      const tradeIndex = tradeStore.index('timestamp')
      const tradeRange = IDBKeyRange.upperBound(cutoffTime)
      
      await new Promise<void>((resolve, reject) => {
        const request = tradeIndex.openCursor(tradeRange)
        request.onsuccess = () => {
          const cursor = request.result
          if (cursor) {
            cursor.delete()
            cursor.continue()
          } else {
            resolve()
          }
        }
        request.onerror = () => reject(request.error)
      })
      
      // 清理旧市场分析
      const analysisStore = transaction.objectStore('market_analysis')
      const analysisIndex = analysisStore.index('timestamp')
      
      await new Promise<void>((resolve, reject) => {
        const request = analysisIndex.openCursor(tradeRange)
        request.onsuccess = () => {
          const cursor = request.result
          if (cursor) {
            cursor.delete()
            cursor.continue()
          } else {
            resolve()
          }
        }
        request.onerror = () => reject(request.error)
      })
      
      console.log(`✅ 清理了${days}天前的旧数据`)
      
    } catch (error) {
      console.error('❌ 清理旧数据失败:', error)
    }
  }

  // 导出数据
  async exportData(startTime?: number, endTime?: number): Promise<{
    trades: TradeRecord[]
    performance: StrategyPerformance[]
    analysis: MarketAnalysis[]
  }> {
    const trades = await this.getTrades({ startTime, endTime })
    const performance = await this.getPerformance()
    
    // 简化导出，市场分析数据量较大
    const analysis: MarketAnalysis[] = []
    
    return { trades, performance, analysis }
  }
}

// 创建全局数据库实例
export const tradingDatabase = new TradingDatabase()
export default tradingDatabase 