#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能行情识别与动态策略切换系统 v3.0 - 高效版
核心优化：预计算指标，减少循环计算，提升性能
作者：顶尖量化交易师
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import time


class MarketState(Enum):
    """市场状态枚举"""
    SIDEWAYS = "震荡"  # 震荡行情
    UPTREND = "上涨趋势"  # 上涨趋势 
    DOWNTREND = "下跌趋势"  # 下跌趋势


@dataclass
class TradeSignal:
    """交易信号"""
    action: str  # 'buy', 'sell', 'hold'
    price: float
    quantity: float
    strategy_type: str  # 'grid', 'trend_follow'
    timestamp: pd.Timestamp
    target_profit: float = 0.005  # 目标利润率0.5%
    stop_loss: float = 0.01  # 止损1%


class TechnicalIndicators:
    """技术指标计算类 - 批量预计算"""
    
    @staticmethod
    def calculate_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """一次性计算所有需要的技术指标"""
        print("📊 预计算技术指标...")
        
        # 移动平均线
        df['ma_5'] = df['close'].rolling(window=5).mean()
        df['ma_20'] = df['close'].rolling(window=20).mean()
        df['ma_60'] = df['close'].rolling(window=60).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # ATR
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        df['atr'] = true_range.rolling(window=14).mean()
        
        # 价格变化率
        df['price_change_1h'] = df['close'].pct_change(4)  # 1小时变化率(4个15分钟)
        df['price_change_4h'] = df['close'].pct_change(16)  # 4小时变化率
        
        # 波动率
        df['volatility'] = df['close'].rolling(window=20).std() / df['close'].rolling(window=20).mean()
        
        print("✅ 技术指标计算完成")
        return df


class MarketStateDetector:
    """市场状态检测器 - 优化版"""
    
    def __init__(self):
        self.trend_threshold = 0.02  # 趋势阈值2%
        self.volatility_threshold = 0.015  # 波动率阈值1.5%
    
    def detect_market_state_batch(self, df: pd.DataFrame) -> List[MarketState]:
        """批量检测市场状态"""
        print("🔍 批量检测市场状态...")
        
        states = []
        
        for i in range(len(df)):
            if i < 60:  # 前60个数据点设为震荡
                states.append(MarketState.SIDEWAYS)
                continue
            
            # 获取当前数据
            current_data = df.iloc[i]
            
            # 趋势判断
            price_change_4h = current_data['price_change_4h']
            volatility = current_data['volatility']
            
            # 移动平均线趋势
            ma_trend = 0
            if current_data['ma_5'] > current_data['ma_20']:
                ma_trend += 1
            if current_data['ma_20'] > current_data['ma_60']:
                ma_trend += 1
            
            # 状态判断逻辑
            if abs(price_change_4h) > self.trend_threshold and volatility < self.volatility_threshold:
                # 明确趋势且波动率不高
                if price_change_4h > 0 and ma_trend >= 1:
                    states.append(MarketState.UPTREND)
                elif price_change_4h < 0 and ma_trend <= 0:
                    states.append(MarketState.DOWNTREND)
                else:
                    states.append(MarketState.SIDEWAYS)
            else:
                # 震荡行情
                states.append(MarketState.SIDEWAYS)
        
        print("✅ 市场状态检测完成")
        return states


class GridStrategy:
    """网格策略 - 简化版"""
    
    def __init__(self, grid_spacing: float = 0.01):
        self.grid_spacing = grid_spacing  # 网格间距1%
        self.grid_levels = []
        self.last_trade_price = None
    
    def initialize_grid(self, current_price: float):
        """初始化网格"""
        # 创建上下各5个网格
        self.grid_levels = []
        for i in range(-5, 6):
            level = current_price * (1 + i * self.grid_spacing)
            self.grid_levels.append(level)
        self.last_trade_price = current_price
    
    def get_signal(self, current_price: float, timestamp: pd.Timestamp) -> Optional[TradeSignal]:
        """获取网格信号"""
        if not self.grid_levels or not self.last_trade_price:
            return None
        
        # 检查是否触发网格
        price_change = abs(current_price - self.last_trade_price) / self.last_trade_price
        
        if price_change >= self.grid_spacing:
            if current_price > self.last_trade_price:
                # 价格上涨，卖出
                action = 'sell'
            else:
                # 价格下跌，买入
                action = 'buy'
            
            self.last_trade_price = current_price
            
            return TradeSignal(
                action=action,
                price=current_price,
                quantity=0.1,  # 固定数量
                strategy_type='grid',
                timestamp=timestamp,
                target_profit=0.005
            )
        
        return None


class TrendStrategy:
    """趋势跟踪策略 - 简化版"""
    
    def __init__(self):
        self.position = 0
        self.entry_price = None
    
    def get_signal(self, current_price: float, state: MarketState, 
                  rsi: float, timestamp: pd.Timestamp) -> Optional[TradeSignal]:
        """获取趋势信号"""
        
        if state == MarketState.UPTREND and self.position <= 0:
            # 上涨趋势且无多头仓位
            if rsi < 70:  # RSI不过热
                self.position = 1
                self.entry_price = current_price
                return TradeSignal(
                    action='buy',
                    price=current_price,
                    quantity=0.2,
                    strategy_type='trend_follow',
                    timestamp=timestamp,
                    target_profit=0.002  # 0.2%快速止盈
                )
        
        elif state == MarketState.DOWNTREND and self.position >= 0:
            # 下跌趋势且无空头仓位
            if rsi > 30:  # RSI不超卖
                self.position = -1
                self.entry_price = current_price
                return TradeSignal(
                    action='sell',
                    price=current_price,
                    quantity=0.2,
                    strategy_type='trend_follow',
                    timestamp=timestamp,
                    target_profit=0.002  # 0.2%快速止盈
                )
        
        elif self.position != 0 and self.entry_price:
            # 检查止盈
            if self.position > 0:  # 多头仓位
                profit_rate = (current_price - self.entry_price) / self.entry_price
                if profit_rate >= 0.002:  # 达到0.2%止盈
                    self.position = 0
                    self.entry_price = None
                    return TradeSignal(
                        action='sell',
                        price=current_price,
                        quantity=0.2,
                        strategy_type='trend_follow',
                        timestamp=timestamp,
                        target_profit=0.002
                    )
            
            elif self.position < 0:  # 空头仓位
                profit_rate = (self.entry_price - current_price) / self.entry_price
                if profit_rate >= 0.002:  # 达到0.2%止盈
                    self.position = 0
                    self.entry_price = None
                    return TradeSignal(
                        action='buy',
                        price=current_price,
                        quantity=0.2,
                        strategy_type='trend_follow',
                        timestamp=timestamp,
                        target_profit=0.002
                    )
        
        return None


class HighPerformanceTradingSystem:
    """高性能交易系统"""
    
    def __init__(self, initial_capital: float = 100000, 
                 commission_rate: float = 0.0005, slippage_rate: float = 0.0002):
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        
        # 账户状态
        self.cash = initial_capital
        self.position = 0
        self.portfolio_value = initial_capital
        
        # 策略组件
        self.grid_strategy = GridStrategy()
        self.trend_strategy = TrendStrategy()
        self.state_detector = MarketStateDetector()
        
        # 记录
        self.trades = []
        self.portfolio_history = []
    
    def load_data(self, file_path: str) -> pd.DataFrame:
        """加载数据"""
        print(f"📊 加载数据: {file_path}")
        
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        
        # 预计算所有技术指标
        df = TechnicalIndicators.calculate_all_indicators(df)
        
        return df
    
    def execute_trade(self, signal: TradeSignal) -> Dict:
        """执行交易"""
        # 计算交易成本
        trade_cost = signal.price * signal.quantity * (self.commission_rate + self.slippage_rate)
        
        if signal.action == 'buy':
            required_cash = signal.price * signal.quantity + trade_cost
            if self.cash >= required_cash:
                self.cash -= required_cash
                self.position += signal.quantity
                executed = True
            else:
                executed = False
        
        elif signal.action == 'sell':
            if self.position >= signal.quantity:
                self.cash += signal.price * signal.quantity - trade_cost
                self.position -= signal.quantity
                executed = True
            else:
                executed = False
        else:
            executed = False
        
        # 记录交易
        trade_record = {
            'timestamp': signal.timestamp,
            'action': signal.action,
            'price': signal.price,
            'quantity': signal.quantity,
            'strategy': signal.strategy_type,
            'executed': executed,
            'cost': trade_cost if executed else 0
        }
        
        self.trades.append(trade_record)
        return trade_record
    
    def backtest(self, df: pd.DataFrame) -> Dict:
        """高效回测"""
        print("🚀 开始高效智能策略回测...")
        
        # 预计算市场状态
        market_states = self.state_detector.detect_market_state_batch(df)
        
        # 初始化网格
        self.grid_strategy.initialize_grid(df['close'].iloc[60])
        
        total_rows = len(df)
        start_time = time.time()
        
        # 主回测循环
        for i in range(60, total_rows):  # 从第60个数据点开始
            current_data = df.iloc[i]
            current_price = current_data['close']
            current_time = df.index[i]
            current_state = market_states[i]
            
            # 显示进度
            if i % 10000 == 0:
                progress = i / total_rows * 100
                elapsed = time.time() - start_time
                eta = elapsed / (i - 59) * (total_rows - i)
                print(f"⏳ 进度: {progress:.1f}% | 已用时: {elapsed:.1f}s | 预计剩余: {eta:.1f}s")
            
            # 生成交易信号
            signals = []
            
            # 震荡行情 - 网格策略
            if current_state == MarketState.SIDEWAYS:
                grid_signal = self.grid_strategy.get_signal(current_price, current_time)
                if grid_signal:
                    signals.append(grid_signal)
            
            # 趋势行情 - 趋势跟踪
            elif current_state in [MarketState.UPTREND, MarketState.DOWNTREND]:
                trend_signal = self.trend_strategy.get_signal(
                    current_price, current_state, current_data['rsi'], current_time
                )
                if trend_signal:
                    signals.append(trend_signal)
            
            # 执行交易
            for signal in signals:
                self.execute_trade(signal)
            
            # 更新组合价值
            self.portfolio_value = self.cash + self.position * current_price
            
            # 记录组合历史（降低频率）
            if i % 100 == 0:  # 每100个点记录一次
                self.portfolio_history.append({
                    'timestamp': current_time,
                    'value': self.portfolio_value,
                    'cash': self.cash,
                    'position': self.position,
                    'price': current_price,
                    'state': current_state.value
                })
        
        print(f"\n✅ 回测完成！用时: {time.time() - start_time:.1f}秒")
        
        return self._calculate_performance(df, market_states)
    
    def _calculate_performance(self, df: pd.DataFrame, market_states: List[MarketState]) -> Dict:
        """计算绩效指标"""
        if not self.portfolio_history:
            return {}
        
        portfolio_df = pd.DataFrame(self.portfolio_history)
        portfolio_df.set_index('timestamp', inplace=True)
        
        # 基础收益指标
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        # 时间相关指标
        start_date = portfolio_df.index[0]
        end_date = portfolio_df.index[-1]
        days = (end_date - start_date).days
        
        annual_return = (self.portfolio_value / self.initial_capital) ** (365 / days) - 1 if days > 0 else 0
        monthly_return = (self.portfolio_value / self.initial_capital) ** (30 / days) - 1 if days > 0 else 0
        
        # 风险指标
        rolling_max = portfolio_df['value'].expanding().max()
        drawdown = (portfolio_df['value'] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 夏普比率
        portfolio_df['daily_return'] = portfolio_df['value'].pct_change()
        risk_free_rate = 0.02
        excess_returns = portfolio_df['daily_return'] - risk_free_rate / 365
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(365) if excess_returns.std() > 0 else 0
        
        # 交易统计
        executed_trades = [t for t in self.trades if t['executed']]
        total_trades = len(executed_trades)
        
        grid_trades = len([t for t in executed_trades if t['strategy'] == 'grid'])
        trend_trades = len([t for t in executed_trades if t['strategy'] == 'trend_follow'])
        
        # 胜率计算（简化）
        win_rate = 0.5  # 默认值，实际需要更复杂的计算
        
        # 市场状态统计
        state_stats = {}
        for state in MarketState:
            count = market_states.count(state)
            state_stats[state.value] = count / len(market_states) if market_states else 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'grid_trades': grid_trades,
            'trend_trades': trend_trades,
            'final_value': self.portfolio_value,
            'market_state_distribution': state_stats
        }


def main():
    """主函数：高效智能策略回测"""
    
    print("🧠 智能行情识别与动态策略切换系统 v3.0 - 高效版")
    print("=" * 70)
    print("💡 核心优化：预计算指标，减少循环计算，提升性能")
    print("🎯 目标：震荡做网格，趋势做追踪，快速止盈")
    print("=" * 70)
    
    # 初始化系统
    trading_system = HighPerformanceTradingSystem(
        initial_capital=100000,
        commission_rate=0.0005,  # 0.05% 手续费
        slippage_rate=0.0002     # 0.02% 滑点
    )
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = trading_system.load_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 运行回测
    try:
        results = trading_system.backtest(df)
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return
    
    # 输出结果
    print("\n" + "=" * 70)
    print("📊 高效智能策略回测结果")
    print("=" * 70)
    
    print(f"\n💰 收益表现:")
    print(f"   总收益: {results['total_return']:.2%}")
    print(f"   年化收益: {results['annual_return']:.2%}")
    print(f"   月化收益: {results['monthly_return']:.2%}")
    print(f"   最终资产: ${results['final_value']:,.2f}")
    
    print(f"\n🛡️ 风险控制:")
    print(f"   最大回撤: {results['max_drawdown']:.2%}")
    print(f"   夏普比率: {results['sharpe_ratio']:.2f}")
    
    print(f"\n📈 交易统计:")
    print(f"   总交易次数: {results['total_trades']}")
    if results['total_trades'] > 0:
        print(f"   网格交易: {results['grid_trades']} ({results['grid_trades']/results['total_trades']:.1%})")
        print(f"   趋势交易: {results['trend_trades']} ({results['trend_trades']/results['total_trades']:.1%})")
    print(f"   胜率: {results['win_rate']:.2%}")
    
    print(f"\n🎯 市场状态分布:")
    for state, percentage in results['market_state_distribution'].items():
        print(f"   {state}: {percentage:.1%}")
    
    # 策略评估
    print(f"\n🔍 策略评估:")
    
    # 检查是否符合目标
    target_met = (results['max_drawdown'] >= -0.15 and 
                  results['monthly_return'] >= 0.10)
    
    if target_met:
        print(f"   ✅ 恭喜！策略达到目标条件")
        print(f"   🎯 回撤控制: {results['max_drawdown']:.2%} (目标: ≤15%)")
        print(f"   🎯 月化收益: {results['monthly_return']:.2%} (目标: ≥10%)")
    else:
        print(f"   🔧 策略需要进一步优化")
        if results['max_drawdown'] < -0.15:
            print(f"   ⚠️ 回撤超限: {results['max_drawdown']:.2%} (目标: ≤15%)")
        if results['monthly_return'] < 0.10:
            print(f"   ⚠️ 收益不足: {results['monthly_return']:.2%} (目标: ≥10%)")
    
    print(f"\n🎊 高效智能策略回测完成！")
    
    return trading_system, results


if __name__ == "__main__":
    trading_system, results = main() 