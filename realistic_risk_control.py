#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现实风险控制策略系统 v5.0
重新定义目标：在比特币市场中寻找真正可行的风险控制策略
现实目标：最大回撤≤25%，年化收益≥30%（月化≥2.2%）
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from datetime import datetime, timedelta


class TechnicalIndicators:
    """技术指标计算"""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """简单移动平均线"""
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """指数移动平均线"""
        return data.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """相对强弱指标"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))


class RealisticBacktester:
    """现实回测引擎"""
    
    def __init__(self, initial_capital: float = 100000, 
                 max_drawdown_target: float = 0.25,  # 目标回撤25%
                 min_annual_return: float = 0.30,     # 目标年化30%
                 commission_rate: float = 0.0005,
                 slippage_rate: float = 0.0002):
        """
        初始化现实回测引擎
        
        Args:
            initial_capital: 初始资金
            max_drawdown_target: 目标最大回撤 (25%)
            min_annual_return: 目标最小年化收益 (30%)
            commission_rate: 手续费率 (0.05%)
            slippage_rate: 滑点率 (0.02%)
        """
        self.initial_capital = initial_capital
        self.max_drawdown_target = max_drawdown_target
        self.min_annual_return = min_annual_return
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        self.data = None
        
    def load_data(self, csv_path: str) -> pd.DataFrame:
        """加载K线数据"""
        print(f"📊 正在加载数据: {csv_path}")
        
        # 读取数据
        df = pd.read_csv(csv_path)
        
        # 数据预处理
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        # 计算技术指标
        df = self._calculate_indicators(df)
        
        self.data = df
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print(f"📅 时间范围: {df.index[0]} 至 {df.index[-1]}")
        
        return df
        
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # 移动平均线
        for period in [10, 20, 50, 100, 200]:
            df[f'ma_{period}'] = TechnicalIndicators.sma(close, period)
            df[f'ema_{period}'] = TechnicalIndicators.ema(close, period)
        
        # RSI
        df['rsi_14'] = TechnicalIndicators.rsi(close, 14)
        
        # 价格变化率
        df['returns_1h'] = close.pct_change(4)   # 1小时收益率
        df['returns_4h'] = close.pct_change(16)  # 4小时收益率
        df['returns_1d'] = close.pct_change(96)  # 1天收益率
        
        # 成交量指标
        df['volume_ma20'] = TechnicalIndicators.sma(volume, 20)
        df['volume_ratio'] = volume / df['volume_ma20']
        
        # 波动率
        df['volatility_20'] = close.pct_change().rolling(20).std()
        
        return df


class RealisticStrategies:
    """现实可行的策略"""
    
    @staticmethod
    def buy_and_hold_with_stops(df: pd.DataFrame) -> pd.DataFrame:
        """带止损的买入持有策略"""
        signals = pd.DataFrame(index=df.index)
        signals['position'] = 0.0
        
        # 初始买入
        position = 0.8  # 80%仓位
        entry_price = df['close'].iloc[0]
        stop_loss_pct = 0.20  # 20%止损
        
        for i in range(len(df)):
            current_price = df['close'].iloc[i]
            
            if position > 0:
                # 检查止损
                if current_price < entry_price * (1 - stop_loss_pct):
                    position = 0.0  # 止损
                    # 等待重新入场信号
                elif i > 0 and df['close'].iloc[i] > df['ma_50'].iloc[i] and position == 0:
                    # 重新入场：价格回到50日均线上方
                    position = 0.8
                    entry_price = current_price
            elif position == 0 and i > 50:  # 初始期后
                # 入场条件：价格在50日均线上方且RSI不过高
                if (df['close'].iloc[i] > df['ma_50'].iloc[i] and 
                    df['rsi_14'].iloc[i] < 70):
                    position = 0.8
                    entry_price = current_price
            
            signals['position'].iloc[i] = position
        
        signals['signal'] = signals['position'].diff()
        return signals
    
    @staticmethod
    def pyramiding_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """金字塔加仓策略"""
        signals = pd.DataFrame(index=df.index)
        signals['position'] = 0.0
        
        position = 0.0
        max_position = 0.9  # 最大90%仓位
        base_size = 0.2     # 基础仓位20%
        
        for i in range(len(df)):
            if i < 200:  # 等待指标稳定
                signals['position'].iloc[i] = 0
                continue
                
            # 趋势确认
            uptrend = (df['ma_20'].iloc[i] > df['ma_50'].iloc[i] > df['ma_100'].iloc[i])
            strong_momentum = df['returns_1d'].iloc[i] > 0.05  # 日涨幅>5%
            
            # 加仓条件
            if uptrend and strong_momentum and position < max_position:
                position = min(max_position, position + base_size)
            
            # 减仓条件
            elif not uptrend or df['rsi_14'].iloc[i] > 80:
                position *= 0.5  # 减半仓位
            
            # 紧急止损
            if df['returns_1d'].iloc[i] < -0.15:  # 单日跌幅>15%
                position = 0.0
                
            signals['position'].iloc[i] = position
        
        signals['signal'] = signals['position'].diff()
        return signals
    
    @staticmethod
    def trend_following_enhanced(df: pd.DataFrame) -> pd.DataFrame:
        """增强趋势跟踪策略"""
        signals = pd.DataFrame(index=df.index)
        signals['position'] = 0.0
        
        for i in range(200, len(df)):  # 从200开始，确保指标稳定
            # 多时间框架趋势确认
            short_trend = df['ema_10'].iloc[i] > df['ema_20'].iloc[i]
            medium_trend = df['ma_20'].iloc[i] > df['ma_50'].iloc[i]
            long_trend = df['ma_50'].iloc[i] > df['ma_100'].iloc[i]
            
            # 动量确认
            momentum_positive = (df['returns_1h'].iloc[i] > 0 and 
                               df['returns_4h'].iloc[i] > 0)
            
            # RSI过滤
            rsi_ok = 30 < df['rsi_14'].iloc[i] < 75
            
            # 成交量确认
            volume_ok = df['volume_ratio'].iloc[i] > 0.8
            
            # 综合信号
            if (short_trend and medium_trend and long_trend and 
                momentum_positive and rsi_ok and volume_ok):
                position = 0.7  # 70%仓位
            elif medium_trend and long_trend and rsi_ok:
                position = 0.4  # 40%仓位
            elif long_trend:
                position = 0.2  # 20%仓位
            else:
                position = 0.0  # 空仓
            
            signals['position'].iloc[i] = position
        
        signals['signal'] = signals['position'].diff()
        return signals
    
    @staticmethod
    def volatility_breakout(df: pd.DataFrame) -> pd.DataFrame:
        """波动率突破策略"""
        signals = pd.DataFrame(index=df.index)
        signals['position'] = 0.0
        
        for i in range(100, len(df)):
            # 计算波动率分位数
            vol_percentile = (df['volatility_20'].iloc[i] / 
                            df['volatility_20'].iloc[i-100:i].quantile(0.5))
            
            # 价格突破
            price_above_ma = df['close'].iloc[i] > df['ma_20'].iloc[i]
            strong_move = abs(df['returns_1d'].iloc[i]) > 0.03  # 日变动>3%
            
            # 趋势过滤
            in_uptrend = df['ma_20'].iloc[i] > df['ma_50'].iloc[i]
            
            if (price_above_ma and strong_move and in_uptrend and 
                vol_percentile < 1.5 and df['returns_1d'].iloc[i] > 0):
                position = 0.6  # 60%仓位
            elif in_uptrend and price_above_ma:
                position = 0.3  # 30%仓位
            else:
                position = 0.0
                
            signals['position'].iloc[i] = position
        
        signals['signal'] = signals['position'].diff()
        return signals
    
    @staticmethod
    def dca_with_momentum(df: pd.DataFrame) -> pd.DataFrame:
        """动量定投策略"""
        signals = pd.DataFrame(index=df.index)
        signals['position'] = 0.0
        
        # 每月定投一次 (30*24*4 = 2880个15分钟周期)
        dca_interval = 2880
        position = 0.0
        max_position = 0.8
        base_investment = 0.1
        
        for i in range(len(df)):
            # 定期定投
            if i % dca_interval == 0 and i > 0:
                # 只在趋势向上时定投
                if (df['ma_50'].iloc[i] > df['ma_100'].iloc[i] and 
                    df['returns_1d'].iloc[i] > -0.05):  # 日跌幅不超过5%
                    
                    # 根据RSI调整投资额
                    if df['rsi_14'].iloc[i] < 40:  # 超卖时加大投资
                        investment = base_investment * 1.5
                    elif df['rsi_14'].iloc[i] > 60:  # 偏高时减少投资
                        investment = base_investment * 0.5
                    else:
                        investment = base_investment
                    
                    position = min(max_position, position + investment)
            
            # 动态调整
            if df['rsi_14'].iloc[i] > 80:  # 极度超买时减仓
                position *= 0.8
            elif df['returns_1d'].iloc[i] < -0.20:  # 暴跌时减仓
                position *= 0.6
                
            signals['position'].iloc[i] = position
        
        signals['signal'] = signals['position'].diff()
        return signals
    
    @staticmethod
    def conservative_growth(df: pd.DataFrame) -> pd.DataFrame:
        """保守增长策略"""
        signals = pd.DataFrame(index=df.index)
        signals['position'] = 0.0
        
        for i in range(200, len(df)):
            # 超级保守的入场条件
            perfect_conditions = (
                df['ma_20'].iloc[i] > df['ma_50'].iloc[i] > df['ma_100'].iloc[i] > df['ma_200'].iloc[i] and  # 完美多头排列
                df['close'].iloc[i] > df['ma_20'].iloc[i] and  # 价格在均线上方
                40 < df['rsi_14'].iloc[i] < 65 and  # RSI适中
                df['returns_1d'].iloc[i] > 0 and  # 当日上涨
                df['volume_ratio'].iloc[i] > 1.0  # 成交量正常
            )
            
            if perfect_conditions:
                position = 0.5  # 50%仓位
            elif (df['ma_50'].iloc[i] > df['ma_100'].iloc[i] and 
                  df['close'].iloc[i] > df['ma_50'].iloc[i]):
                position = 0.2  # 20%仓位
            else:
                position = 0.0
                
            signals['position'].iloc[i] = position
        
        signals['signal'] = signals['position'].diff()
        return signals


class RealisticPerformanceAnalyzer:
    """现实绩效分析"""
    
    @staticmethod
    def calculate_returns(df: pd.DataFrame, signals: pd.DataFrame, 
                         initial_capital: float, commission_rate: float, 
                         slippage_rate: float) -> Dict:
        """计算策略收益"""
        
        df_copy = df.copy()
        df_copy['position'] = signals['position'].fillna(0)
        df_copy['signal'] = signals['signal'].fillna(0)
        
        # 价格变化
        df_copy['price_change'] = df_copy['close'].pct_change()
        
        # 策略收益
        df_copy['strategy_return'] = df_copy['position'].shift(1) * df_copy['price_change']
        
        # 交易成本
        trade_mask = df_copy['signal'] != 0
        total_cost_rate = commission_rate + slippage_rate
        df_copy['trade_cost'] = 0
        df_copy.loc[trade_mask, 'trade_cost'] = total_cost_rate * abs(df_copy.loc[trade_mask, 'signal'])
        
        # 净收益
        df_copy['net_return'] = df_copy['strategy_return'] - df_copy['trade_cost']
        
        # 累计收益
        df_copy['cumulative_return'] = (1 + df_copy['net_return']).cumprod()
        df_copy['portfolio_value'] = initial_capital * df_copy['cumulative_return']
        
        # 计算回撤
        rolling_max = df_copy['portfolio_value'].expanding().max()
        drawdown = (df_copy['portfolio_value'] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 计算其他指标
        total_return = df_copy['cumulative_return'].iloc[-1] - 1
        days = (df_copy.index[-1] - df_copy.index[0]).days
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        monthly_return = (1 + total_return) ** (30 / days) - 1 if days > 0 else 0
        
        # 夏普比率
        risk_free_rate = 0.02
        excess_return = df_copy['net_return'] - risk_free_rate / (365 * 24 * 4)
        sharpe_ratio = excess_return.mean() / excess_return.std() * np.sqrt(365 * 24 * 4) if excess_return.std() > 0 else 0
        
        # 其他指标
        winning_trades = (df_copy['net_return'] > 0).sum()
        total_trades = (df_copy['net_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        trade_count = (df_copy['signal'].abs() > 0).sum()
        total_cost = df_copy['trade_cost'].sum()
        
        # 风险调整收益
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0
        
        # 仓位统计
        max_position = df_copy['position'].max()
        avg_position = df_copy['position'].mean()
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'calmar_ratio': calmar_ratio,
            'win_rate': win_rate,
            'trade_count': trade_count,
            'total_cost': total_cost,
            'max_position': max_position,
            'avg_position': avg_position,
            'final_value': df_copy['portfolio_value'].iloc[-1],
            'portfolio_series': df_copy['portfolio_value'],
            'drawdown_series': drawdown
        }


def main():
    """主函数：现实风险控制策略回测"""
    
    print("💎 现实风险控制策略系统 v5.0")
    print("=" * 60)
    print("🎯 现实目标：回撤≤25%，年化收益≥30%")
    print("💡 理念：在加密货币市场中寻找真正可行的策略")
    print("=" * 60)
    
    # 初始化回测引擎
    backester = RealisticBacktester(
        initial_capital=100000,
        max_drawdown_target=0.25,  # 25%回撤限制
        min_annual_return=0.30,    # 30%年化收益目标
        commission_rate=0.0005,
        slippage_rate=0.0002
    )
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = backester.load_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 现实策略集合
    strategies = {
        '带止损买入持有': RealisticStrategies.buy_and_hold_with_stops,
        '金字塔加仓策略': RealisticStrategies.pyramiding_strategy,
        '增强趋势跟踪': RealisticStrategies.trend_following_enhanced,
        '波动率突破策略': RealisticStrategies.volatility_breakout,
        '动量定投策略': RealisticStrategies.dca_with_momentum,
        '保守增长策略': RealisticStrategies.conservative_growth
    }
    
    # 回测所有策略
    results = {}
    qualified_strategies = {}
    
    print("\n🔍 开始现实策略回测...")
    
    for strategy_name, strategy_func in strategies.items():
        print(f"\n💎 测试策略: {strategy_name}")
        
        try:
            # 生成交易信号
            signals = strategy_func(df)
            
            # 计算绩效
            performance = RealisticPerformanceAnalyzer.calculate_returns(
                df, signals, backester.initial_capital,
                backester.commission_rate, backester.slippage_rate
            )
            
            results[strategy_name] = performance
            
            print(f"   💰 总收益: {performance['total_return']:.2%}")
            print(f"   📈 年化收益: {performance['annual_return']:.2%}")
            print(f"   📊 月化收益: {performance['monthly_return']:.2%}")
            print(f"   📉 最大回撤: {performance['max_drawdown']:.2%}")
            print(f"   ⚡ 夏普比率: {performance['sharpe_ratio']:.2f}")
            print(f"   📐 卡尔玛比率: {performance['calmar_ratio']:.2f}")
            print(f"   🎯 胜率: {performance['win_rate']:.2%}")
            print(f"   📊 平均仓位: {performance['avg_position']:.1%}")
            print(f"   🔄 交易次数: {performance['trade_count']}")
            
            # 检查是否符合现实筛选条件
            if (performance['max_drawdown'] >= -0.25 and 
                performance['annual_return'] >= 0.30):
                qualified_strategies[strategy_name] = performance
                print(f"   ✅ 符合现实目标！")
            elif performance['max_drawdown'] >= -0.25:
                print(f"   🟡 回撤达标，收益需改进")
            else:
                print(f"   🟠 收益不错，回撤需控制")
                
        except Exception as e:
            print(f"   ❌ 策略 {strategy_name} 回测失败: {e}")
    
    # 结果汇总
    print("\n" + "=" * 70)
    print("🎯 现实策略筛选结果")
    print("=" * 70)
    
    # 按卡尔玛比率排序
    sorted_results = sorted(results.items(), 
                          key=lambda x: x[1]['calmar_ratio'] if x[1]['calmar_ratio'] > 0 else -999, 
                          reverse=True)
    
    # 创建结果表格
    print("\n📊 所有策略表现概览 (按风险调整收益排序):")
    summary_data = []
    for name, perf in sorted_results:
        符合条件 = "✅" if name in qualified_strategies else ("🟡" if perf['max_drawdown'] >= -0.25 else "🟠")
        summary_data.append({
            '策略名称': name,
            '符合条件': 符合条件,
            '总收益': f"{perf['total_return']:.1%}",
            '年化收益': f"{perf['annual_return']:.1%}",
            '最大回撤': f"{perf['max_drawdown']:.1%}",
            '夏普比率': f"{perf['sharpe_ratio']:.2f}",
            '卡尔玛比率': f"{perf['calmar_ratio']:.2f}",
            '平均仓位': f"{perf['avg_position']:.0%}",
            '交易次数': perf['trade_count']
        })
    
    summary_df = pd.DataFrame(summary_data)
    print(summary_df.to_string(index=False))
    
    # 分析结果
    if qualified_strategies:
        print(f"\n🎉 找到 {len(qualified_strategies)} 个符合现实目标的策略！")
        
        best_strategy = max(qualified_strategies.items(), 
                          key=lambda x: x[1]['calmar_ratio'])
        
        print(f"\n🏆 最优现实策略: {best_strategy[0]}")
        perf = best_strategy[1]
        print(f"   💰 总收益: {perf['total_return']:.2%}")
        print(f"   📈 年化收益: {perf['annual_return']:.2%}")
        print(f"   📊 月化收益: {perf['monthly_return']:.2%}")
        print(f"   📉 最大回撤: {perf['max_drawdown']:.2%}")
        print(f"   ⚡ 夏普比率: {perf['sharpe_ratio']:.2f}")
        print(f"   📐 卡尔玛比率: {perf['calmar_ratio']:.2f}")
        print(f"   💎 最终资产: ${perf['final_value']:,.2f}")
        
    else:
        print("🔍 没有策略完全符合现实目标，但让我们看看最接近的：")
        
        # 最佳风险调整收益
        if results:
            best_overall = max(results.items(), 
                             key=lambda x: x[1]['calmar_ratio'] if x[1]['calmar_ratio'] > 0 else -999)
            
            print(f"\n📐 最佳风险调整收益: {best_overall[0]}")
            perf = best_overall[1]
            print(f"   年化收益: {perf['annual_return']:.2%}")
            print(f"   最大回撤: {perf['max_drawdown']:.2%}")
            print(f"   卡尔玛比率: {perf['calmar_ratio']:.2f}")
            
            # 最佳回撤控制
            best_drawdown = min(results.items(), key=lambda x: abs(x[1]['max_drawdown']))
            print(f"\n🛡️ 最佳回撤控制: {best_drawdown[0]}")
            print(f"   最大回撤: {best_drawdown[1]['max_drawdown']:.2%}")
            print(f"   年化收益: {best_drawdown[1]['annual_return']:.2%}")
    
    # 与简单买入持有对比
    print(f"\n📈 市场基准对比:")
    market_return = (df['close'].iloc[-1] / df['close'].iloc[0] - 1)
    market_annual = (df['close'].iloc[-1] / df['close'].iloc[0]) ** (365 / (df.index[-1] - df.index[0]).days) - 1
    print(f"   简单买入持有总收益: {market_return:.2%}")
    print(f"   简单买入持有年化: {market_annual:.2%}")
    
    # 策略建议
    print(f"\n💡 实用建议:")
    print(f"   📊 在加密货币市场中，回撤25%以内已经是很好的风控")
    print(f"   🎯 年化30%是相对现实的目标（比传统资产高很多）")
    print(f"   🔄 关键是持续性和一致性，而非追求极端收益")
    print(f"   🛡️ 风险管理永远比收益最大化更重要")
    
    print(f"\n🎊 现实策略回测完成！")
    
    return results, qualified_strategies


if __name__ == "__main__":
    main() 