#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单监控仪表盘 - Streamlit版本
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta

# 页面配置
st.set_page_config(
    page_title="策略监控仪表盘",
    page_icon="📈",
    layout="wide"
)

def generate_mock_data():
    """生成模拟数据"""
    dates = pd.date_range(end=datetime.now(), periods=100, freq='H')
    
    # 模拟权益曲线
    returns = np.random.normal(0.001, 0.02, 100)
    equity = 10000 * (1 + returns).cumprod()
    
    # 模拟交易数据
    trades = pd.DataFrame({
        'time': np.random.choice(dates, 20),
        'symbol': ['BTCUSDT'] * 20,
        'side': np.random.choice(['BUY', 'SELL'], 20),
        'quantity': np.random.uniform(0.1, 1.0, 20),
        'price': np.random.uniform(45000, 55000, 20),
        'commission': np.random.uniform(1, 10, 20)
    })
    
    return dates, equity, trades

def main():
    st.title("🎯 策略监控仪表盘")
    st.sidebar.title("控制面板")
    
    # 生成数据
    dates, equity, trades = generate_mock_data()
    
    # 主要指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("当前权益", f"${equity[-1]:,.2f}", 
                 f"{(equity[-1]/equity[0]-1)*100:+.2f}%")
    
    with col2:
        daily_return = (equity[-1]/equity[-2]-1)*100
        st.metric("日收益率", f"{daily_return:+.2f}%")
    
    with col3:
        max_dd = ((equity / np.maximum.accumulate(equity)) - 1).min() * 100
        st.metric("最大回撤", f"{max_dd:.2f}%")
    
    with col4:
        sharpe = (np.diff(equity)/equity[:-1]).mean() / (np.diff(equity)/equity[:-1]).std() * np.sqrt(24*365)
        st.metric("夏普比率", f"{sharpe:.2f}")
    
    # 权益曲线图
    st.subheader("📈 权益曲线")
    fig_equity = go.Figure()
    fig_equity.add_trace(go.Scatter(
        x=dates, y=equity,
        mode='lines',
        name='权益曲线',
        line=dict(color='blue', width=2)
    ))
    fig_equity.update_layout(
        title="实时权益曲线",
        xaxis_title="时间",
        yaxis_title="权益 ($)",
        height=400
    )
    st.plotly_chart(fig_equity, use_container_width=True)
    
    # 交易记录
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📋 最近交易")
        st.dataframe(trades.tail(10))
    
    with col2:
        st.subheader("💰 费用分析")
        cost_breakdown = {
            'commission': trades['commission'].sum(),
            'slippage': trades['commission'].sum() * 0.5,
            'funding': trades['commission'].sum() * 0.3
        }
        
        fig_cost = px.pie(
            values=list(cost_breakdown.values()),
            names=list(cost_breakdown.keys()),
            title="费用构成"
        )
        st.plotly_chart(fig_cost, use_container_width=True)
    
    # 风险监控
    st.subheader("🛡️ 风险监控")
    risk_col1, risk_col2 = st.columns(2)
    
    with risk_col1:
        # VaR计算
        returns = np.diff(equity) / equity[:-1]
        var_95 = np.percentile(returns, 5) * equity[-1]
        st.metric("VaR (95%)", f"${var_95:,.2f}")
        
    with risk_col2:
        # 波动率
        volatility = returns.std() * np.sqrt(24*365) * 100
        st.metric("年化波动率", f"{volatility:.1f}%")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        st.error(f"仪表盘加载失败: {e}")
        st.info("请安装依赖: pip install streamlit plotly")
