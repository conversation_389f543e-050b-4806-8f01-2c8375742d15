import { useEffect, useState } from 'react'
import { MetricCard } from '@/components/common/MetricCard'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Shield,
  AlertTriangle,
  WifiOff,
  Zap,
  Play
} from 'lucide-react'
import type { DashboardMetrics, TradeRecord } from '@/types/dashboard'
import { getReturnColor } from '@/lib/utils'
import { binanceApi, LiveTradingMetrics } from '@/services/binanceApi'
import { strategyExecutor, StrategyStatus } from '@/services/strategyExecutor'

export function DashboardPage() {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [recentTrades, setRecentTrades] = useState<TradeRecord[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [strategyStatus, setStrategyStatus] = useState<StrategyStatus | null>(null)
  const [liveMetrics, setLiveMetrics] = useState<LiveTradingMetrics>({
    isConnected: false,
    isTrading: false,
    accountBalance: 0,
    availableBalance: 0,
    currentPosition: 0,
    unrealizedPnL: 0,
    realizedPnL: 0,
    todayTrades: 0,
    currentDrawdown: 0,
    maxDrawdownToday: 0,
    riskLevel: 'LOW'
  })

  // 更新实时数据
  const updateLiveData = async () => {
    try {
      const metrics = await binanceApi.getLiveTradingMetrics()
      if (metrics) {
        setLiveMetrics(metrics)
      }
    } catch (error) {
      console.error('获取实时数据失败:', error)
    }
  }

  // 更新策略状态
  const updateStrategyStatus = () => {
    try {
      if (strategyExecutor.isStrategyRunning()) {
        const status = strategyExecutor.getStrategyStatus()
        setStrategyStatus(status)
      } else {
        setStrategyStatus(null)
      }
    } catch (error) {
      console.error('获取策略状态失败:', error)
      setStrategyStatus(null)
    }
  }

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // 更新实时数据
        await updateLiveData()
        
        // 更新策略状态
        updateStrategyStatus()
        
        if (liveMetrics.isConnected) {
          // 如果连接了API，使用实时数据
          const liveDataMetrics: DashboardMetrics = {
            currentEquity: liveMetrics.accountBalance,
            initialEquity: 10000, // 假设初始资金，可以从配置获取
            totalReturn: liveMetrics.accountBalance > 0 ? (liveMetrics.accountBalance - 10000) / 10000 : 0,
            dailyReturn: liveMetrics.accountBalance > 0 ? liveMetrics.realizedPnL / liveMetrics.accountBalance : 0,
            maxDrawdown: -liveMetrics.currentDrawdown / 100,
            totalTrades: liveMetrics.todayTrades,
            totalCosts: 0, // 需要从API计算
            winRate: 0, // 需要从历史数据计算
            sharpeRatio: 0, // 需要从历史数据计算
            var95: liveMetrics.currentDrawdown / 100
          }
          setMetrics(liveDataMetrics)
        } else {
          // 未连接API，显示初始状态
          const initialMetrics: DashboardMetrics = {
            currentEquity: 0,
            initialEquity: 0,
            totalReturn: 0,
            dailyReturn: 0,
            maxDrawdown: 0,
            totalTrades: 0,
            totalCosts: 0,
            winRate: 0,
            sharpeRatio: 0,
            var95: 0
          }
          setMetrics(initialMetrics)
        }
        
        // 实盘模式的交易记录需要从API获取
        setRecentTrades([])
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [liveMetrics.isConnected])

  // 实时数据轮询
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    
    if (liveMetrics.isConnected) {
      interval = setInterval(updateLiveData, 5000) // 每5秒更新
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [liveMetrics.isConnected])

  // 策略状态监控
  useEffect(() => {
    let strategyInterval: NodeJS.Timeout | null = null
    
    // 每2秒检查策略状态
    strategyInterval = setInterval(updateStrategyStatus, 2000)
    
    return () => {
      if (strategyInterval) clearInterval(strategyInterval)
    }
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">数据加载失败</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">实时监控仪表盘</h1>
          <p className="text-slate-400 mt-1">智能策略终极版 - 实盘交易数据</p>
        </div>
        <div className="flex items-center gap-3">
          <Badge className={liveMetrics.isConnected ? 
            'bg-green-500/20 text-green-400 border-green-500/30' : 
            'bg-red-500/20 text-red-400 border-red-500/30'
          }>
            {liveMetrics.isConnected ? '已连接API' : '未连接API'}
          </Badge>
          <Badge className={strategyStatus?.isRunning ? 
            'bg-blue-500/20 text-blue-400 border-blue-500/30' : 
            'bg-gray-500/20 text-gray-400 border-gray-500/30'
          }>
            {strategyStatus?.isRunning ? '策略运行中' : '策略未运行'}
          </Badge>
        </div>
      </div>

      {/* 策略终极版状态 */}
      <Card className="bg-gradient-to-r from-purple-900/30 to-blue-900/30 border-purple-500/30">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                strategyStatus?.isRunning ? 'bg-blue-500/20' : 'bg-gray-500/20'
              }`}>
                {strategyStatus?.isRunning ? (
                  <Zap className="w-6 h-6 text-blue-400" />
                ) : (
                  <Play className="w-6 h-6 text-gray-400" />
                )}
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">策略终极版状态</h2>
                <p className="text-slate-400">
                  {strategyStatus?.isRunning ? (
                    `多策略融合系统运行中 | 市场状态: ${strategyStatus.marketState} | 风险等级: ${strategyStatus.riskLevel}`
                  ) : (
                    '策略终极版未运行 - 请前往"策略配置"页面启动'
                  )}
                </p>
              </div>
            </div>
            {strategyStatus?.isRunning && (
              <div className="text-right">
                <div className="text-2xl font-bold text-green-400">
                  {strategyStatus.totalTrades}
                </div>
                <div className="text-sm text-slate-400">总交易次数</div>
              </div>
            )}
          </div>
          
          {strategyStatus?.isRunning && (
            <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-lg font-semibold text-white">
                  {strategyStatus.winRate.toFixed(1)}%
                </div>
                <div className="text-xs text-slate-400">胜率</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-white">
                  {strategyStatus.leverage.toFixed(1)}x
                </div>
                <div className="text-xs text-slate-400">当前杠杆</div>
              </div>
              <div className="text-center">
                <div className={`text-lg font-semibold ${
                  strategyStatus.unrealizedPnl >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {strategyStatus.unrealizedPnl >= 0 ? '+' : ''}${strategyStatus.unrealizedPnl.toFixed(2)}
                </div>
                <div className="text-xs text-slate-400">未实现盈亏</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-red-400">
                  {(strategyStatus.maxDrawdown * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-slate-400">最大回撤</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 未连接API提示 */}
      {!liveMetrics.isConnected && (
        <Card className="bg-yellow-500/10 border border-yellow-500/30">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <WifiOff className="w-8 h-8 text-yellow-400" />
              <div>
                <h3 className="text-lg font-semibold text-yellow-400">未连接币安API</h3>
                <p className="text-yellow-300 mt-1">
                  当前显示的是初始状态（全部为0）。请前往 "风险管理" 页面连接您的币安账户后查看实时数据。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="账户余额"
          value={!liveMetrics.isConnected ? "$0" : 
                 (typeof metrics.currentEquity === 'number' ? `$${metrics.currentEquity.toLocaleString()}` : metrics.currentEquity)}
          change={!liveMetrics.isConnected ? undefined : (metrics.currentEquity - metrics.initialEquity)}
          changeType="currency"
          icon={DollarSign}
          description={!liveMetrics.isConnected ? "未连接API" : undefined}
        />
        <MetricCard
          title="今日收益率"
          value={!liveMetrics.isConnected ? "0.00%" : `${(metrics.totalReturn * 100).toFixed(2)}%`}
          change={!liveMetrics.isConnected ? undefined : metrics.dailyReturn * 100}
          icon={TrendingUp}
          description={!liveMetrics.isConnected ? "未连接API" : undefined}
        />
        <MetricCard
          title="当前回撤"
          value={!liveMetrics.isConnected ? "0.00%" : `${(metrics.maxDrawdown * 100).toFixed(2)}%`}
          change={!liveMetrics.isConnected ? undefined : metrics.maxDrawdown * 100}
          icon={TrendingDown}
          className="border-danger-500/20"
          description={!liveMetrics.isConnected ? "未连接API" : undefined}
        />
        <MetricCard
          title="今日交易"
          value={liveMetrics.isConnected ? metrics.totalTrades.toString() : "0"}
          description={liveMetrics.isConnected ? "执行次数" : "未连接API"}
          icon={Activity}
        />
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 每日盈亏图 */}
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              每日盈亏分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-64">
              <div className="text-center space-y-3">
                <WifiOff className="w-12 h-12 text-slate-400 mx-auto" />
                <div>
                  <p className="text-slate-400 font-medium">未连接币安API</p>
                  <p className="text-slate-500 text-sm">连接API后查看每日盈亏数据</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* VaR风险监控 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              VaR风险监控
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-40">
              <div className="text-center space-y-3">
                <Shield className="w-10 h-10 text-slate-400 mx-auto" />
                <div>
                  <p className="text-slate-400 font-medium">未连接API</p>
                  <p className="text-slate-500 text-sm">连接后显示风险指标</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 成本分解 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              交易成本分解
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-40">
              <div className="text-center space-y-3">
                <DollarSign className="w-10 h-10 text-slate-400 mx-auto" />
                <div>
                  <p className="text-slate-400 font-medium">未连接API</p>
                  <p className="text-slate-500 text-sm">连接后显示成本分析</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 告警横幅 */}
      {metrics.var95 > 0.02 && (
        <Card className="border-warning-500 bg-warning-50/10">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-5 h-5 text-warning-500" />
              <div>
                <p className="font-medium text-warning-600">风险预警</p>
                <p className="text-sm text-warning-600/80">
                  当前VaR({(metrics.var95 * 100).toFixed(2)}%)超过2%阈值，请关注风险水平
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 最近交易记录 */}
      <Card>
        <CardHeader>
          <CardTitle>最近交易记录</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="data-table">
              <thead>
                <tr>
                  <th>时间</th>
                  <th>交易对</th>
                  <th>方向</th>
                  <th>数量</th>
                  <th>价格</th>
                  <th>手续费</th>
                  <th>PnL</th>
                  <th>状态</th>
                </tr>
              </thead>
              <tbody>
                {recentTrades.map((trade) => (
                  <tr key={trade.id}>
                    <td>{trade.timestamp.toLocaleTimeString()}</td>
                    <td className="font-mono">{trade.symbol}</td>
                    <td>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        trade.side === 'BUY' 
                          ? 'bg-success-100 text-success-700' 
                          : 'bg-danger-100 text-danger-700'
                      }`}>
                        {trade.side}
                      </span>
                    </td>
                    <td>{trade.quantity}</td>
                    <td className="font-mono">${trade.price.toLocaleString()}</td>
                    <td>${trade.commission.toFixed(2)}</td>
                    <td className={getReturnColor(trade.pnl)}>
                      {trade.pnl > 0 ? '+' : ''}${trade.pnl.toFixed(2)}
                    </td>
                    <td>
                      <span className="px-2 py-1 rounded text-xs bg-success-100 text-success-700">
                        {trade.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 