// 币安K线数据服务 - 为策略指标提供标准K线数据

export interface KlineData {
  openTime: number      // 开盘时间
  open: number         // 开盘价
  high: number         // 最高价  
  low: number          // 最低价
  close: number        // 收盘价
  volume: number       // 成交量
  closeTime: number    // 收盘时间
  quoteVolume: number  // 成交额
  trades: number       // 成交笔数
  buyBaseVolume: number // 主动买入成交量
  buyQuoteVolume: number // 主动买入成交额
}

export interface KlineSubscription {
  symbol: string
  interval: string
  callback: (kline: KlineData) => void
}

// 支持的K线间隔
export type KlineInterval = '1m' | '3m' | '5m' | '15m' | '30m' | '1h' | '2h' | '4h' | '6h' | '8h' | '12h' | '1d' | '3d' | '1w' | '1M'

// 预计算的技术指标接口
export interface PrecomputedIndicators {
  // 移动平均线
  ma_3: number
  ma_5: number
  ma_8: number
  ma_13: number
  ma_21: number
  ma_34: number
  ma_55: number
  ma_89: number
  ema_8: number
  ema_21: number
  
  // RSI指标
  rsi_5: number
  rsi_7: number
  rsi_14: number
  rsi_21: number
  
  // 动量指标
  momentum_1: number
  momentum_2: number
  momentum_4: number
  momentum_8: number
  momentum_16: number
  
  // 市场状态
  volatility: number
  volume_ratio: number
  trend_strength: number
  
  // 数据质量
  dataComplete: boolean
  klineCount: number
  lastUpdate: number
}

class BinanceKlineService {
  private wsConnections: Map<string, WebSocket> = new Map()
  private klineData: Map<string, KlineData[]> = new Map() // symbol_interval -> KlineData[]
  private subscriptions: Map<string, KlineSubscription[]> = new Map()
  private isInitialized: Map<string, boolean> = new Map()
  private precomputedIndicators: Map<string, PrecomputedIndicators> = new Map() // 缓存预计算指标

  // 快速获取历史K线数据并预计算技术指标 (一步到位)
  async fetchHistoricalKlinesWithIndicators(
    symbol: string, 
    interval: KlineInterval, 
    limit: number = 200
  ): Promise<{ klines: KlineData[], indicators: PrecomputedIndicators }> {
    try {
      console.log(`🚀 快速获取历史K线数据和技术指标: ${symbol} ${interval} (${limit}根)`)
      const startTime = Date.now()
      
      // 1. 获取历史K线数据
      const klines = await this.fetchHistoricalKlines(symbol, interval, limit)
      
      // 2. 立即计算所有技术指标
      const indicators = this.calculateAllIndicators(klines)
      
      // 3. 缓存预计算结果
      const key = `${symbol}_${interval}`
      this.precomputedIndicators.set(key, indicators)
      
      const elapsed = Date.now() - startTime
      console.log(`✅ 历史数据和技术指标获取完成: ${elapsed}ms, K线${klines.length}根, 指标完整度${indicators.dataComplete ? '100%' : '部分'}`)
      
      return { klines, indicators }
    } catch (error) {
      console.error('获取历史K线和指标失败:', error)
      throw error
    }
  }

  // 批量计算所有技术指标 (基于历史K线数据)
  private calculateAllIndicators(klines: KlineData[]): PrecomputedIndicators {
    if (klines.length === 0) {
      return this.getEmptyIndicators()
    }

    console.log(`📊 批量计算技术指标: ${klines.length}根K线`)

    // 提取收盘价数组
    const closes = klines.map(k => k.close)
    const highs = klines.map(k => k.high)
    const lows = klines.map(k => k.low)
    const volumes = klines.map(k => k.volume)

    // 计算移动平均线
    const ma_3 = this.calculateSMA(closes, 3)
    const ma_5 = this.calculateSMA(closes, 5)
    const ma_8 = this.calculateSMA(closes, 8)
    const ma_13 = this.calculateSMA(closes, 13)
    const ma_21 = this.calculateSMA(closes, 21)
    const ma_34 = this.calculateSMA(closes, 34)
    const ma_55 = this.calculateSMA(closes, 55)
    const ma_89 = this.calculateSMA(closes, 89)
    
    // 计算EMA
    const ema_8 = this.calculateEMA(closes, 8)
    const ema_21 = this.calculateEMA(closes, 21)
    
    // 计算RSI
    const rsi_5 = this.calculateRSI(closes, 5)
    const rsi_7 = this.calculateRSI(closes, 7)
    const rsi_14 = this.calculateRSI(closes, 14)
    const rsi_21 = this.calculateRSI(closes, 21)
    
    // 计算动量指标
    const momentum_1 = this.calculateMomentum(closes, 1)
    const momentum_2 = this.calculateMomentum(closes, 2)
    const momentum_4 = this.calculateMomentum(closes, 4)
    const momentum_8 = this.calculateMomentum(closes, 8)
    const momentum_16 = this.calculateMomentum(closes, 16)
    
    // 计算波动率和其他指标
    const volatility = this.calculateVolatility(closes, 20)
    const volume_ratio = this.calculateVolumeRatio(volumes, 20)
    const trend_strength = this.calculateTrendStrength(closes)

    const indicators: PrecomputedIndicators = {
      ma_3, ma_5, ma_8, ma_13, ma_21, ma_34, ma_55, ma_89,
      ema_8, ema_21,
      rsi_5, rsi_7, rsi_14, rsi_21,
      momentum_1, momentum_2, momentum_4, momentum_8, momentum_16,
      volatility, volume_ratio, trend_strength,
      dataComplete: klines.length >= 89, // 是否有足够数据计算MA89
      klineCount: klines.length,
      lastUpdate: Date.now()
    }

    console.log(`✅ 技术指标计算完成:`, {
      dataComplete: indicators.dataComplete,
      rsi_14: indicators.rsi_14.toFixed(2),
      ma_21: indicators.ma_21.toFixed(2),
      volatility: (indicators.volatility * 100).toFixed(3) + '%',
      trend_strength: indicators.trend_strength.toFixed(3)
    })

    return indicators
  }

  // 技术指标计算辅助方法

  // 简单移动平均线
  private calculateSMA(data: number[], period: number): number {
    if (data.length < period) return 0
    const slice = data.slice(-period)
    return slice.reduce((sum, val) => sum + val, 0) / period
  }

  // 指数移动平均线
  private calculateEMA(data: number[], period: number): number {
    if (data.length < period) return 0
    
    const multiplier = 2 / (period + 1)
    
    // 使用前period个数据的SMA作为初始EMA
    let ema = 0
    for (let i = 0; i < period; i++) {
      ema += data[i]
    }
    ema /= period
    
    // 从第period个数据开始计算EMA
    for (let i = period; i < data.length; i++) {
      ema = (data[i] * multiplier) + (ema * (1 - multiplier))
    }
    
    return ema
  }

  // RSI计算 (Wilder平滑算法)
  private calculateRSI(data: number[], period: number): number {
    if (data.length < period + 1) return 50
    
    // 计算价格变化
    const changes = []
    for (let i = 1; i < data.length; i++) {
      changes.push(data[i] - data[i - 1])
    }
    
    if (changes.length < period) return 50
    
    // 初始平均增益和损失
    let avgGain = 0
    let avgLoss = 0
    
    for (let i = 0; i < period; i++) {
      if (changes[i] > 0) {
        avgGain += changes[i]
      } else {
        avgLoss += Math.abs(changes[i])
      }
    }
    
    avgGain /= period
    avgLoss /= period
    
    // Wilder平滑
    for (let i = period; i < changes.length; i++) {
      const gain = changes[i] > 0 ? changes[i] : 0
      const loss = changes[i] < 0 ? Math.abs(changes[i]) : 0
      
      avgGain = ((avgGain * (period - 1)) + gain) / period
      avgLoss = ((avgLoss * (period - 1)) + loss) / period
    }
    
    if (avgLoss === 0) return 100
    if (avgGain === 0) return 0
    
    const rs = avgGain / avgLoss
    return 100 - (100 / (1 + rs))
  }

  // 动量计算
  private calculateMomentum(data: number[], periods: number): number {
    if (data.length < periods + 1) return 0
    
    const current = data[data.length - 1]
    const previous = data[data.length - 1 - periods]
    
    return (current / previous) - 1
  }

  // 波动率计算 (基于收盘价标准差)
  private calculateVolatility(data: number[], period: number): number {
    if (data.length < period) return 0
    
    const slice = data.slice(-period)
    const returns = []
    
    for (let i = 1; i < slice.length; i++) {
      returns.push((slice[i] / slice[i - 1]) - 1)
    }
    
    if (returns.length === 0) return 0
    
    const mean = returns.reduce((a, b) => a + b, 0) / returns.length
    const variance = returns.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / returns.length
    
    return Math.sqrt(variance)
  }

  // 成交量比率
  private calculateVolumeRatio(volumes: number[], period: number): number {
    if (volumes.length < period) return 1
    
    const avgVolume = this.calculateSMA(volumes, period)
    const currentVolume = volumes[volumes.length - 1]
    
    return avgVolume > 0 ? currentVolume / avgVolume : 1
  }

  // 趋势强度
  private calculateTrendStrength(data: number[]): number {
    if (data.length < 21) return 0
    
    const ma_8 = this.calculateSMA(data, 8)
    const ma_21 = this.calculateSMA(data, 21)
    
    if (ma_21 === 0) return 0
    
    return Math.abs(ma_8 - ma_21) / ma_21
  }

  // 获取空指标
  private getEmptyIndicators(): PrecomputedIndicators {
    return {
      ma_3: 0, ma_5: 0, ma_8: 0, ma_13: 0, ma_21: 0, ma_34: 0, ma_55: 0, ma_89: 0,
      ema_8: 0, ema_21: 0,
      rsi_5: 50, rsi_7: 50, rsi_14: 50, rsi_21: 50,
      momentum_1: 0, momentum_2: 0, momentum_4: 0, momentum_8: 0, momentum_16: 0,
      volatility: 0, volume_ratio: 1, trend_strength: 0,
      dataComplete: false, klineCount: 0,
      lastUpdate: Date.now()
    }
  }

  // 获取预计算指标
  getPrecomputedIndicators(symbol: string, interval: KlineInterval): PrecomputedIndicators | null {
    const key = `${symbol}_${interval}`
    return this.precomputedIndicators.get(key) || null
  }

  // 获取历史K线数据 (REST API)
  async fetchHistoricalKlines(
    symbol: string, 
    interval: KlineInterval, 
    limit: number = 200
  ): Promise<KlineData[]> {
    try {
      console.log(`📊 获取历史K线数据: ${symbol} ${interval} (${limit}根)`)
      
      // 币安期货API (策略终极版使用期货数据)
      const url = `https://fapi.binance.com/fapi/v1/klines?symbol=${symbol}&interval=${interval}&limit=${limit}`
      
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const rawData = await response.json()
      
      const klines: KlineData[] = rawData.map((item: any[]) => ({
        openTime: item[0],
        open: parseFloat(item[1]),
        high: parseFloat(item[2]),
        low: parseFloat(item[3]),
        close: parseFloat(item[4]),
        volume: parseFloat(item[5]),
        closeTime: item[6],
        quoteVolume: parseFloat(item[7]),
        trades: item[8],
        buyBaseVolume: parseFloat(item[9]),
        buyQuoteVolume: parseFloat(item[10])
      }))
      
      // 缓存历史数据
      const key = `${symbol}_${interval}`
      this.klineData.set(key, klines)
      this.isInitialized.set(key, true)
      
      console.log(`✅ 历史K线获取成功: ${klines.length}根, 最新收盘价: ${klines[klines.length - 1]?.close}`)
      
      return klines
    } catch (error) {
      console.error('获取历史K线失败:', error)
      throw error
    }
  }

  // 订阅实时K线更新 (WebSocket)
  subscribeKline(
    symbol: string, 
    interval: KlineInterval, 
    callback: (kline: KlineData) => void
  ): void {
    const streamName = `${symbol.toLowerCase()}@kline_${interval}`
    
    // 添加订阅回调
    if (!this.subscriptions.has(streamName)) {
      this.subscriptions.set(streamName, [])
    }
    this.subscriptions.get(streamName)!.push({
      symbol,
      interval,
      callback
    })

    // 如果已有连接，直接返回
    if (this.wsConnections.has(streamName)) {
      console.log(`✅ K线WebSocket已连接: ${streamName}`)
      return
    }

    // 建立WebSocket连接 (期货WebSocket)
    this.connectKlineWebSocket(streamName, symbol, interval)
  }

  // 建立K线WebSocket连接 (期货WebSocket)
  private connectKlineWebSocket(streamName: string, symbol: string, interval: KlineInterval): void {
    const wsUrl = `wss://fstream.binance.com/ws/${streamName}`
    console.log(`🔗 连接期货K线WebSocket: ${wsUrl}`)
    
    const ws = new WebSocket(wsUrl)
    
    ws.onopen = () => {
      console.log(`📡 K线WebSocket连接成功: ${streamName}`)
    }
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        
        if (data.e === 'kline' && data.k) {
          const klineInfo = data.k
          const klineData: KlineData = {
            openTime: klineInfo.t,
            open: parseFloat(klineInfo.o),
            high: parseFloat(klineInfo.h),
            low: parseFloat(klineInfo.l),
            close: parseFloat(klineInfo.c),
            volume: parseFloat(klineInfo.v),
            closeTime: klineInfo.T,
            quoteVolume: parseFloat(klineInfo.q),
            trades: klineInfo.n,
            buyBaseVolume: parseFloat(klineInfo.V),
            buyQuoteVolume: parseFloat(klineInfo.Q)
          }
          
          // 更新缓存数据
          this.updateKlineCache(symbol, interval, klineData, klineInfo.x) // x表示是否闭合
          
          // 如果K线已闭合，重新计算技术指标
          if (klineInfo.x) {
            this.updatePrecomputedIndicators(symbol, interval)
          }
          
          // 通知所有订阅者
          const subscriptions = this.subscriptions.get(streamName) || []
          subscriptions.forEach(sub => {
            try {
              sub.callback(klineData)
            } catch (error) {
              console.error('K线回调执行失败:', error)
            }
          })
        }
      } catch (error) {
        console.error('K线数据解析错误:', error)
      }
    }
    
    ws.onclose = (event) => {
      console.warn(`K线WebSocket连接关闭: ${streamName}, code: ${event.code}`)
      this.wsConnections.delete(streamName)
      
      // 自动重连
      setTimeout(() => {
        if (this.subscriptions.has(streamName)) {
          console.log(`🔄 尝试重连K线WebSocket: ${streamName}`)
          this.connectKlineWebSocket(streamName, symbol, interval)
        }
      }, 3000)
    }
    
    ws.onerror = (error) => {
      console.error(`K线WebSocket错误: ${streamName}`, error)
    }
    
    this.wsConnections.set(streamName, ws)
  }

  // 更新K线缓存数据
  private updateKlineCache(symbol: string, interval: KlineInterval, klineData: KlineData, isClosed: boolean): void {
    const key = `${symbol}_${interval}`
    const klines = this.klineData.get(key) || []
    
    if (klines.length === 0) return
    
    if (isClosed) {
      // K线已闭合，添加新K线
      klines.push(klineData)
      // 保持最多500根K线
      if (klines.length > 500) {
        klines.shift()
      }
    } else {
      // K线未闭合，更新最后一根K线
      klines[klines.length - 1] = klineData
    }
    
    this.klineData.set(key, klines)
  }

  // 更新预计算指标 (当新K线闭合时触发)
  private updatePrecomputedIndicators(symbol: string, interval: KlineInterval): void {
    const key = `${symbol}_${interval}`
    const klines = this.klineData.get(key)
    
    if (klines && klines.length > 0) {
      const indicators = this.calculateAllIndicators(klines)
      this.precomputedIndicators.set(key, indicators)
      console.log(`🔄 更新预计算指标: ${symbol} ${interval}, RSI14=${indicators.rsi_14.toFixed(2)}`)
    }
  }

  // 获取缓存的K线数据
  getKlineData(symbol: string, interval: KlineInterval, count?: number): KlineData[] {
    const key = `${symbol}_${interval}`
    const klines = this.klineData.get(key) || []
    
    if (count && count > 0) {
      return klines.slice(-count) // 返回最近count根K线
    }
    
    return [...klines] // 返回所有K线数据副本
  }

  // 检查是否已初始化
  isDataReady(symbol: string, interval: KlineInterval): boolean {
    const key = `${symbol}_${interval}`
    return this.isInitialized.get(key) === true
  }

  // 获取最新收盘价
  getLatestPrice(symbol: string, interval: KlineInterval): number {
    const klines = this.getKlineData(symbol, interval, 1)
    return klines.length > 0 ? klines[0].close : 0
  }

  // 清理订阅
  unsubscribe(symbol: string, interval: KlineInterval): void {
    const streamName = `${symbol.toLowerCase()}@kline_${interval}`
    
    // 关闭WebSocket连接
    const ws = this.wsConnections.get(streamName)
    if (ws) {
      ws.close()
      this.wsConnections.delete(streamName)
    }
    
    // 清理订阅
    this.subscriptions.delete(streamName)
    
    console.log(`🗑️ 已清理K线订阅: ${streamName}`)
  }

  // 清理所有连接
  cleanup(): void {
    console.log('🧹 清理所有K线WebSocket连接')
    
    // 关闭所有WebSocket连接
    for (const [streamName, ws] of this.wsConnections) {
      try {
        ws.close()
        console.log(`🗑️ 已关闭连接: ${streamName}`)
      } catch (error) {
        console.error(`关闭连接失败: ${streamName}`, error)
      }
    }
    
    // 清理所有缓存
    this.wsConnections.clear()
    this.subscriptions.clear()
    this.isInitialized.clear()
    this.precomputedIndicators.clear()
  }

  // 调试：获取服务状态
  getServiceStatus() {
    return {
      connections: Array.from(this.wsConnections.keys()),
      dataKeys: Array.from(this.klineData.keys()),
      subscriptions: Array.from(this.subscriptions.keys()),
      initialized: Array.from(this.isInitialized.entries()),
      precomputedIndicators: Array.from(this.precomputedIndicators.keys())
    }
  }
}

// 创建全局实例
export const binanceKlineService = new BinanceKlineService() 