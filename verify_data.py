#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""数据验证脚本"""

import pandas as pd
import numpy as np

def verify_data():
    """验证回测数据"""
    print("📊 验证回测数据准确性")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('K线数据/BTCUSDT_15m_189773.csv')
    
    print(f"数据基本信息:")
    print(f"  数据行数: {len(df):,}")
    print(f"  开始时间: {df.iloc[0]['datetime']}")
    print(f"  结束时间: {df.iloc[-1]['datetime']}")
    print(f"  开始价格: ${df.iloc[0]['close']:,.2f}")
    print(f"  结束价格: ${df.iloc[-1]['close']:,.2f}")
    
    # 计算买入持有收益（从第60行开始，与策略一致）
    start_price = df.iloc[60]['close']
    end_price = df.iloc[-1]['close']
    buy_hold_return = (end_price - start_price) / start_price
    
    print(f"\n买入持有策略（从第60行开始）:")
    print(f"  起始价格: ${start_price:,.2f}")
    print(f"  结束价格: ${end_price:,.2f}")
    print(f"  总收益: {buy_hold_return:.4f} ({buy_hold_return*100:.2f}%)")
    
    # 计算时间跨度
    start_date = pd.to_datetime(df.iloc[60]['datetime'])
    end_date = pd.to_datetime(df.iloc[-1]['datetime'])
    days = (end_date - start_date).days
    
    print(f"\n时间跨度:")
    print(f"  总天数: {days}")
    print(f"  总年数: {days/365:.2f}")
    print(f"  总月数: {days/30:.1f}")
    
    # 验证年化和月化收益计算
    if days > 0:
        annual_factor = 365 / days
        monthly_factor = 30 / days
        
        buy_hold_annual = (1 + buy_hold_return) ** annual_factor - 1
        buy_hold_monthly = (1 + buy_hold_return) ** monthly_factor - 1
        
        print(f"\n买入持有年化月化收益:")
        print(f"  年化收益: {buy_hold_annual*100:.2f}%")
        print(f"  月化收益: {buy_hold_monthly*100:.2f}%")
    
    print("\n=" * 50)
    print("✅ 数据验证完成")

if __name__ == "__main__":
    verify_data() 