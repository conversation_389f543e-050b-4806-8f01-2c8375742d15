#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能策略平衡版 - 收益与风控平衡
目标：月化收益5%，回撤≤20%，年化收益80%+
核心理念：震荡网格+精选趋势+严格风控
作者：顶尖量化交易师
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Optional
import time
from dataclasses import dataclass
from enum import Enum


class MarketState(Enum):
    """市场状态"""
    SIDEWAYS = "震荡"
    UPTREND = "上涨趋势" 
    DOWNTREND = "下跌趋势"


@dataclass
class Trade:
    """交易记录"""
    timestamp: pd.Timestamp
    action: str  # 'buy', 'sell'
    price: float
    quantity: float
    strategy: str  # 'grid', 'trend'
    executed: bool = True


class BalancedTradingSystem:
    """平衡版交易系统"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.position = 0.0
        self.portfolio_value = initial_capital
        
        # 交易成本
        self.commission_rate = 0.0005  # 0.05%
        self.slippage_rate = 0.0002    # 0.02%
        
        # 平衡的策略参数
        self.grid_spacing = 0.012      # 网格间距1.2%
        self.trend_profit = 0.006      # 趋势止盈0.6%
        self.trend_stop_loss = 0.004   # 趋势止损0.4%
        self.trend_threshold = 0.018   # 趋势判断阈值1.8%
        self.max_position_ratio = 0.7  # 最大仓位70%
        
        # 增强风控
        self.max_drawdown_limit = 0.20  # 最大回撤限制20%
        self.position_risk_limit = 0.15 # 单次仓位风险15%
        self.daily_trade_limit = 50     # 日交易次数限制
        
        # 状态变量
        self.last_grid_price = None
        self.trend_entry_price = None
        self.trend_position = 0
        self.max_portfolio_value = initial_capital
        self.daily_trades = 0
        self.last_trade_day = None
        
        # 记录
        self.trades = []
        self.portfolio_history = []
    
    def load_and_prepare_data(self, file_path: str) -> pd.DataFrame:
        """加载并预处理数据"""
        print(f"📊 加载数据: {file_path}")
        
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print("📊 预计算技术指标...")
        
        # 精选技术指标
        df['ma_20'] = df['close'].rolling(window=20).mean()
        df['ma_60'] = df['close'].rolling(window=60).mean()
        df['ma_120'] = df['close'].rolling(window=120).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 价格变化率
        df['price_change_1h'] = df['close'].pct_change(4)
        df['price_change_4h'] = df['close'].pct_change(16)
        df['price_change_12h'] = df['close'].pct_change(48)
        
        # 波动率
        df['volatility'] = df['close'].rolling(window=20).std() / df['close'].rolling(window=20).mean()
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        # 成交量指标
        df['volume_ma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        
        print("✅ 技术指标计算完成")
        return df
    
    def detect_market_state(self, row: pd.Series) -> MarketState:
        """精准的市场状态检测"""
        price_change_4h = row.get('price_change_4h', 0)
        price_change_12h = row.get('price_change_12h', 0)
        volatility = row.get('volatility', 0)
        ma_20 = row.get('ma_20', 0)
        ma_60 = row.get('ma_60', 0)
        ma_120 = row.get('ma_120', 0)
        macd = row.get('macd', 0)
        macd_signal = row.get('macd_signal', 0)
        volume_ratio = row.get('volume_ratio', 1)
        
        # 更严格的趋势确认
        trend_signals = 0
        trend_strength = 0
        
        # 价格变化信号 - 需要多周期确认
        if abs(price_change_4h) > self.trend_threshold:
            trend_signals += 1
            trend_strength += price_change_4h
        
        if abs(price_change_12h) > self.trend_threshold * 1.5:
            trend_signals += 1
            trend_strength += price_change_12h * 0.5
        
        # 移动平均线信号 - 需要明确排列
        if ma_20 > ma_60 > ma_120 and price_change_4h > 0:
            trend_signals += 2  # 加权更高
            trend_strength += 0.01
        elif ma_20 < ma_60 < ma_120 and price_change_4h < 0:
            trend_signals += 2
            trend_strength -= 0.01
        
        # MACD信号
        if (macd > macd_signal and price_change_4h > 0) or (macd < macd_signal and price_change_4h < 0):
            trend_signals += 1
            trend_strength += (macd - macd_signal) * 100
        
        # 成交量确认
        if volume_ratio > 1.2:  # 成交量放大
            trend_signals += 1
        
        # 波动率条件 - 不能太高
        if volatility < 0.02:
            trend_signals += 1
        
        # 需要至少4个信号确认强趋势
        if trend_signals >= 4:
            if trend_strength > 0:
                return MarketState.UPTREND
            else:
                return MarketState.DOWNTREND
        
        return MarketState.SIDEWAYS
    
    def check_risk_controls(self, current_price: float, timestamp: pd.Timestamp) -> bool:
        """增强风险控制"""
        # 更新最大组合价值
        current_value = self.cash + self.position * current_price
        if current_value > self.max_portfolio_value:
            self.max_portfolio_value = current_value
        
        # 检查最大回撤
        drawdown = (current_value - self.max_portfolio_value) / self.max_portfolio_value
        if drawdown < -self.max_drawdown_limit:
            return False
        
        # 检查日交易限制
        current_day = timestamp.date()
        if self.last_trade_day != current_day:
            self.daily_trades = 0
            self.last_trade_day = current_day
        
        if self.daily_trades >= self.daily_trade_limit:
            return False
        
        return True
    
    def grid_strategy(self, current_price: float, timestamp: pd.Timestamp) -> Optional[Trade]:
        """优化网格策略"""
        if self.last_grid_price is None:
            self.last_grid_price = current_price
            return None
        
        price_change = abs(current_price - self.last_grid_price) / self.last_grid_price
        
        if price_change >= self.grid_spacing:
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            
            if current_price > self.last_grid_price:
                # 价格上涨，卖出
                if position_ratio > 0.1:
                    action = 'sell'
                    quantity = min(0.04, self.position * 0.4)  # 控制交易量
                else:
                    return None
            else:
                # 价格下跌，买入
                if position_ratio < self.max_position_ratio:
                    action = 'buy'
                    max_buy = (current_value * 0.06) / current_price
                    quantity = min(max_buy, 0.04)
                else:
                    return None
            
            self.last_grid_price = current_price
            
            return Trade(
                timestamp=timestamp,
                action=action,
                price=current_price,
                quantity=quantity,
                strategy='grid'
            )
        
        return None
    
    def trend_strategy(self, current_price: float, state: MarketState, 
                      row: pd.Series, timestamp: pd.Timestamp) -> Optional[Trade]:
        """精选趋势策略"""
        rsi = row.get('rsi', 50)
        volume_ratio = row.get('volume_ratio', 1)
        volatility = row.get('volatility', 0)
        
        # 开仓逻辑 - 更严格的条件
        if state == MarketState.UPTREND and self.trend_position <= 0:
            if (35 < rsi < 65 and volume_ratio > 1.1 and volatility < 0.025):
                self.trend_position = 1
                self.trend_entry_price = current_price
                current_value = self.cash + self.position * current_price
                # 控制单次仓位风险
                position_value = min(current_value * 0.12, current_value * self.position_risk_limit)
                quantity = position_value / current_price
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='trend'
                )
        
        elif state == MarketState.DOWNTREND and self.trend_position >= 0:
            if (35 < rsi < 65 and volume_ratio > 1.1 and volatility < 0.025):
                self.trend_position = -1
                self.trend_entry_price = current_price
                if self.position > 0:
                    quantity = min(0.06, self.position * 0.35)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
        
        # 止盈止损逻辑
        elif self.trend_position != 0 and self.trend_entry_price:
            if self.trend_position > 0:  # 多头仓位
                profit_rate = (current_price - self.trend_entry_price) / self.trend_entry_price
                if profit_rate >= self.trend_profit:  # 止盈
                    self.trend_position = 0
                    self.trend_entry_price = None
                    quantity = min(0.06, self.position * 0.4)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
                elif profit_rate <= -self.trend_stop_loss:  # 止损
                    self.trend_position = 0
                    self.trend_entry_price = None
                    quantity = min(0.06, self.position * 0.4)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
            
            elif self.trend_position < 0:  # 空头仓位
                profit_rate = (self.trend_entry_price - current_price) / self.trend_entry_price
                if profit_rate >= self.trend_profit:  # 止盈
                    self.trend_position = 0
                    self.trend_entry_price = None
                    current_value = self.cash + self.position * current_price
                    buy_value = current_value * 0.06
                    quantity = buy_value / current_price
                    return Trade(
                        timestamp=timestamp,
                        action='buy',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
                elif profit_rate <= -self.trend_stop_loss:  # 止损
                    self.trend_position = 0
                    self.trend_entry_price = None
                    current_value = self.cash + self.position * current_price
                    buy_value = current_value * 0.06
                    quantity = buy_value / current_price
                    return Trade(
                        timestamp=timestamp,
                        action='buy',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
        
        return None
    
    def execute_trade(self, trade: Trade) -> bool:
        """执行交易"""
        trade_value = trade.price * trade.quantity
        cost = trade_value * (self.commission_rate + self.slippage_rate)
        
        if trade.action == 'buy':
            total_cost = trade_value + cost
            if self.cash >= total_cost:
                self.cash -= total_cost
                self.position += trade.quantity
                trade.executed = True
                self.daily_trades += 1
            else:
                trade.executed = False
        
        elif trade.action == 'sell':
            if self.position >= trade.quantity:
                proceeds = trade_value - cost
                self.cash += proceeds
                self.position -= trade.quantity
                trade.executed = True
                self.daily_trades += 1
            else:
                trade.executed = False
        
        self.trades.append(trade)
        return trade.executed
    
    def backtest(self, df: pd.DataFrame) -> Dict:
        """运行平衡回测"""
        print("🚀 开始平衡智能策略回测...")
        
        total_rows = len(df)
        start_time = time.time()
        
        # 初始化
        grid_trades = 0
        trend_trades = 0
        state_counts = {state: 0 for state in MarketState}
        risk_stops = 0
        
        # 主回测循环
        for i in range(120, total_rows):  # 从第120行开始
            current_row = df.iloc[i]
            current_price = current_row['close']
            current_time = df.index[i]
            
            # 显示进度
            if i % 20000 == 0:
                progress = i / total_rows * 100
                elapsed = time.time() - start_time
                eta = elapsed / (i - 119) * (total_rows - i) if i > 120 else 0
                print(f"⏳ 进度: {progress:.1f}% | 用时: {elapsed:.0f}s | 预计剩余: {eta:.0f}s")
            
            # 风险控制检查
            if not self.check_risk_controls(current_price, current_time):
                risk_stops += 1
                continue
            
            # 检测市场状态
            market_state = self.detect_market_state(current_row)
            state_counts[market_state] += 1
            
            # 生成交易信号
            trade = None
            
            if market_state == MarketState.SIDEWAYS:
                # 震荡行情 - 网格策略
                trade = self.grid_strategy(current_price, current_time)
                if trade and self.execute_trade(trade) and trade.executed:
                    grid_trades += 1
                    
            elif market_state in [MarketState.UPTREND, MarketState.DOWNTREND]:
                # 趋势行情 - 趋势跟踪
                trade = self.trend_strategy(current_price, market_state, current_row, current_time)
                if trade and self.execute_trade(trade) and trade.executed:
                    trend_trades += 1
            
            # 更新组合价值
            self.portfolio_value = self.cash + self.position * current_price
            
            # 记录组合历史
            if i % 1000 == 0:
                self.portfolio_history.append({
                    'timestamp': current_time,
                    'value': self.portfolio_value,
                    'cash': self.cash,
                    'position': self.position,
                    'price': current_price,
                    'state': market_state.value
                })
        
        elapsed_time = time.time() - start_time
        print(f"\n✅ 平衡回测完成！用时: {elapsed_time:.1f}秒")
        print(f"📊 网格交易: {grid_trades} | 趋势交易: {trend_trades} | 风控停止: {risk_stops}")
        
        return self._calculate_performance(df, state_counts)
    
    def _calculate_performance(self, df: pd.DataFrame, state_counts: Dict) -> Dict:
        """计算绩效指标"""
        
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        start_date = df.index[120]
        end_date = df.index[-1]
        days = (end_date - start_date).days
        
        if days > 0:
            annual_return = (self.portfolio_value / self.initial_capital) ** (365 / days) - 1
            monthly_return = (self.portfolio_value / self.initial_capital) ** (30 / days) - 1
        else:
            annual_return = 0
            monthly_return = 0
        
        # 计算最大回撤
        if self.portfolio_history:
            portfolio_df = pd.DataFrame(self.portfolio_history)
            values = portfolio_df['value']
            rolling_max = values.expanding().max()
            drawdown = (values - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
        else:
            max_drawdown = 0
        
        # 交易统计
        executed_trades = [t for t in self.trades if t.executed]
        total_trades = len(executed_trades)
        grid_trades = len([t for t in executed_trades if t.strategy == 'grid'])
        trend_trades = len([t for t in executed_trades if t.strategy == 'trend'])
        
        # 计算夏普比率
        if self.portfolio_history:
            portfolio_df = pd.DataFrame(self.portfolio_history)
            returns = portfolio_df['value'].pct_change().dropna()
            if len(returns) > 0 and returns.std() > 0:
                sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 96)  # 15分钟数据年化
            else:
                sharpe_ratio = 0
        else:
            sharpe_ratio = 0
        
        # 市场状态分布
        total_states = sum(state_counts.values())
        state_distribution = {
            state.value: count / total_states if total_states > 0 else 0 
            for state, count in state_counts.items()
        }
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_trades': total_trades,
            'grid_trades': grid_trades,
            'trend_trades': trend_trades,
            'final_value': self.portfolio_value,
            'final_cash': self.cash,
            'final_position': self.position,
            'market_state_distribution': state_distribution
        }


def main():
    """主函数"""
    print("🧠 智能策略平衡版 - 收益与风控平衡")
    print("=" * 70)
    print("💡 核心理念：震荡网格+精选趋势+严格风控")
    print("🎯 目标：月化收益5%，回撤≤20%，夏普比率≥1.5")
    print("=" * 70)
    
    # 初始化平衡系统
    trading_system = BalancedTradingSystem(initial_capital=100000)
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = trading_system.load_and_prepare_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 运行平衡回测
    try:
        results = trading_system.backtest(df)
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return
    
    # 输出结果
    print("\n" + "=" * 70)
    print("📊 平衡智能策略回测结果")
    print("=" * 70)
    
    print(f"\n💰 收益表现:")
    print(f"   总收益: {results['total_return']:.2%}")
    print(f"   年化收益: {results['annual_return']:.2%}")
    print(f"   月化收益: {results['monthly_return']:.2%}")
    print(f"   最终资产: ${results['final_value']:,.2f}")
    
    print(f"\n🛡️ 风险控制:")
    print(f"   最大回撤: {results['max_drawdown']:.2%}")
    print(f"   夏普比率: {results['sharpe_ratio']:.2f}")
    
    print(f"\n📈 交易统计:")
    print(f"   总交易次数: {results['total_trades']}")
    if results['total_trades'] > 0:
        print(f"   网格交易: {results['grid_trades']} ({results['grid_trades']/results['total_trades']:.1%})")
        print(f"   趋势交易: {results['trend_trades']} ({results['trend_trades']/results['total_trades']:.1%})")
    
    print(f"\n🎯 市场状态分布:")
    for state, percentage in results['market_state_distribution'].items():
        print(f"   {state}: {percentage:.1%}")
    
    # 平衡目标评估
    print(f"\n🔍 平衡目标评估:")
    monthly_good = results['monthly_return'] >= 0.05
    drawdown_good = results['max_drawdown'] >= -0.20
    sharpe_good = results['sharpe_ratio'] >= 1.5
    
    score = sum([monthly_good, drawdown_good, sharpe_good])
    
    if score == 3:
        print(f"   🎉 完美平衡！三项指标全部达标！")
    elif score == 2:
        print(f"   ✅ 良好平衡！三项指标中达标两项")
    else:
        print(f"   🔧 需要调优，仅达标{score}项指标")
    
    print(f"   {'✅' if monthly_good else '❌'} 月化收益: {results['monthly_return']:.2%} {'≥' if monthly_good else '<'} 5.0%")
    print(f"   {'✅' if drawdown_good else '❌'} 最大回撤: {results['max_drawdown']:.2%} {'≤' if drawdown_good else '>'} -20.0%")
    print(f"   {'✅' if sharpe_good else '❌'} 夏普比率: {results['sharpe_ratio']:.2f} {'≥' if sharpe_good else '<'} 1.5")
    
    # 与买入持有对比
    buy_hold_return = (df['close'].iloc[-1] - df['close'].iloc[120]) / df['close'].iloc[120]
    print(f"\n📊 策略对比:")
    print(f"   平衡策略收益: {results['total_return']:.2%}")
    print(f"   买入持有收益: {buy_hold_return:.2%}")
    
    if results['total_return'] > buy_hold_return:
        print(f"   ✅ 平衡策略跑赢买入持有 {(results['total_return'] - buy_hold_return):.2%}")
    else:
        print(f"   ❌ 平衡策略跑输买入持有 {(buy_hold_return - results['total_return']):.2%}")
    
    print(f"\n🎊 平衡策略回测完成！")
    
    return results


if __name__ == "__main__":
    results = main() 