
import { Routes, Route } from 'react-router-dom'
import { AppLayout } from './components/layout/AppLayout'
import { DashboardPage } from './pages/dashboard/DashboardPage'
import { SettingsPage } from './pages/settings/SettingsPage'
import { StrategyPage } from './pages/StrategyPage'
import { RiskManagementPage } from './pages/RiskManagementPage'
import { TradingHistoryPage } from './pages/TradingHistoryPage'
import { RealTimeMonitoring } from './components/monitoring/RealTimeMonitoring'
import { StrategyProvider } from './context/StrategyContext'

// 初始化全局价格服务
import './services/globalPriceService'

// 临时占位页面组件
function ComingSoonPage({ title }: { title: string }) {
  return (
    <div className="flex flex-col items-center justify-center h-64 space-y-4">
      <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center">
        <span className="text-2xl">🚧</span>
      </div>
      <h2 className="text-xl font-semibold">{title}</h2>
      <p className="text-muted-foreground">功能开发中，敬请期待...</p>
    </div>
  )
}

function App() {
  return (
    <StrategyProvider>
      <Routes>
        <Route path="/*" element={
          <AppLayout>
            <Routes>
              <Route path="/" element={<DashboardPage />} />
              <Route path="/monitor" element={<RealTimeMonitoring />} />
              <Route path="/strategy" element={<StrategyPage />} />
              <Route path="/api-monitor" element={<ComingSoonPage title="API监控" />} />
              <Route path="/risk" element={<RiskManagementPage />} />
              <Route path="/history" element={<TradingHistoryPage />} />
              <Route path="/analytics" element={<ComingSoonPage title="性能分析" />} />
              <Route path="/reports" element={<ComingSoonPage title="报告中心" />} />
              <Route path="/settings" element={<SettingsPage />} />
              {/* 404 页面 */}
              <Route path="*" element={<ComingSoonPage title="页面未找到" />} />
            </Routes>
          </AppLayout>
        } />
      </Routes>
    </StrategyProvider>
  )
}

export default App 