#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标瘦身与特征选择优化器
解决50+指标过拟合问题，保留核心有效指标
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import (
    SelectKBest, f_classif, RFE, 
    mutual_info_classif
)
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report
import matplotlib.pyplot as plt
import seaborn as sns


class TechnicalIndicatorCalculator:
    """精简技术指标计算器"""
    
    def __init__(self):
        # 核心周期参数 (大幅简化)
        self.core_periods = [5, 14, 20, 50]  # 从原来8个时间框架减至4个
        
    def calculate_core_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算核心技术指标 (从50+减至15个)"""
        
        df = df.copy()
        
        # 1. 价格指标 (4个)
        df['price_change'] = df['close'].pct_change()
        df['high_low_ratio'] = (df['high'] - df['low']) / df['close']
        df['open_close_ratio'] = (df['close'] - df['open']) / df['open']
        df['vwap'] = (df['volume'] * df['close']).cumsum() / df['volume'].cumsum()
        
        # 2. 趋势指标 (5个)
        for period in [5, 20, 50]:
            df[f'ma_{period}'] = df['close'].rolling(window=period).mean()
            
        df['ma_ratio_5_20'] = df['ma_5'] / df['ma_20']
        df['ma_ratio_20_50'] = df['ma_20'] / df['ma_50']
        
        # 3. 动量指标 (3个)
        df['rsi_14'] = self.calculate_rsi(df['close'], 14)
        df['momentum_1h'] = df['close'].pct_change(4)  # 1小时(15分钟*4)
        df['momentum_4h'] = df['close'].pct_change(16) # 4小时
        
        # 4. 波动率指标 (2个)
        df['volatility_20'] = df['close'].rolling(window=20).std() / df['close'].rolling(window=20).mean()
        df['atr_14'] = self.calculate_atr(df, 14)
        
        # 5. 成交量指标 (1个)
        df['volume_ratio'] = df['volume'] / df['volume'].rolling(window=20).mean()
        
        return df
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算ATR"""
        high = df['high']
        low = df['low']
        close = df['close'].shift(1)
        
        tr1 = high - low
        tr2 = abs(high - close)
        tr3 = abs(low - close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return true_range.rolling(window=period).mean()


class FeatureSelector:
    """特征选择器"""
    
    def __init__(self, max_features: int = 10):
        self.max_features = max_features
        self.selected_features = []
        self.feature_scores = {}
        self.selector_models = {}
        
    def prepare_labels(self, df: pd.DataFrame, 
                      profit_threshold: float = 0.005) -> pd.Series:
        """准备分类标签 (未来收益>0.5%为正例)"""
        
        # 计算未来4期(1小时)收益
        future_returns = df['close'].shift(-4) / df['close'] - 1
        
        # 二分类: 1=盈利(>threshold), 0=亏损或微利
        labels = (future_returns > profit_threshold).astype(int)
        
        # 确保至少有两个类别
        if labels.sum() == 0:  # 如果没有正例，降低阈值
            labels = (future_returns > profit_threshold * 0.5).astype(int)
        if labels.sum() == len(labels):  # 如果全是正例，提高阈值
            labels = (future_returns > profit_threshold * 2).astype(int)
        
        # 如果仍然只有一个类别，创建平衡的标签
        if len(labels.unique()) == 1:
            # 使用中位数分割
            median_return = future_returns.median()
            labels = (future_returns > median_return).astype(int)
        
        return labels
    
    def method_1_statistical_selection(self, X: pd.DataFrame, 
                                     y: pd.Series) -> List[str]:
        """方法1: 统计显著性选择"""
        
        print("[CHART] 执行统计显著性特征选择...")
        
        # 使用F检验选择最显著的特征
        selector = SelectKBest(score_func=f_classif, k=min(self.max_features, X.shape[1]))
        X_selected = selector.fit_transform(X.fillna(0), y)
        
        # 获取选中的特征
        selected_mask = selector.get_support()
        selected_features = X.columns[selected_mask].tolist()
        
        # 记录特征得分
        feature_scores = dict(zip(X.columns, selector.scores_))
        
        self.selector_models['statistical'] = selector
        
        return selected_features, feature_scores
    
    def method_2_random_forest_importance(self, X: pd.DataFrame, 
                                        y: pd.Series) -> List[str]:
        """方法2: 随机森林重要性选择"""
        
        print("[TREE] 执行随机森林重要性特征选择...")
        
        # 训练随机森林
        rf = RandomForestClassifier(n_jobs=1,
            n_estimators=100, 
            random_state=42,
            max_depth=10,  # 限制深度防止过拟合
            min_samples_split=100,
            min_samples_leaf=50
        )
        
        X_filled = X.fillna(X.mean())  # 简单填充缺失值
        rf.fit(X_filled, y)
        
        # 获取特征重要性
        importances = rf.feature_importances_
        feature_importance = dict(zip(X.columns, importances))
        
        # 选择重要性最高的特征
        sorted_features = sorted(feature_importance.items(), 
                               key=lambda x: x[1], reverse=True)
        selected_features = [f[0] for f in sorted_features[:self.max_features]]
        
        self.selector_models['random_forest'] = rf
        
        return selected_features, feature_importance
    
    def method_3_mutual_information(self, X: pd.DataFrame, 
                                  y: pd.Series) -> List[str]:
        """方法3: 互信息选择"""
        
        print("[LINK] 执行互信息特征选择...")
        
        X_filled = X.fillna(0)
        
        # 计算互信息
        mi_scores = mutual_info_classif(X_filled, y, random_state=42, n_jobs=1)
        feature_mi = dict(zip(X.columns, mi_scores))
        
        # 选择互信息最高的特征
        sorted_features = sorted(feature_mi.items(), 
                               key=lambda x: x[1], reverse=True)
        selected_features = [f[0] for f in sorted_features[:self.max_features]]
        
        return selected_features, feature_mi
    
    def method_4_rfe_selection(self, X: pd.DataFrame, 
                             y: pd.Series) -> List[str]:
        """方法4: 递归特征消除"""
        
        print("[CYCLE] 执行递归特征消除...")
        
        # 使用逻辑回归作为基础估计器
        from sklearn.linear_model import LogisticRegression
        
        base_estimator = LogisticRegression(random_state=42, max_iter=1000)
        
        # RFE选择
        rfe = RFE(estimator=base_estimator, 
                  n_features_to_select=self.max_features)
        
        X_filled = X.fillna(0)
        rfe.fit(X_filled, y)
        
        # 获取选中的特征
        selected_mask = rfe.get_support()
        selected_features = X.columns[selected_mask].tolist()
        
        # 特征排名 (越小越重要)
        feature_ranking = dict(zip(X.columns, rfe.ranking_))
        
        self.selector_models['rfe'] = rfe
        
        return selected_features, feature_ranking
    
    def ensemble_feature_selection(self, X: pd.DataFrame, 
                                 y: pd.Series) -> Tuple[List[str], Dict]:
        """集成特征选择 (多方法投票)"""
        
        print("[VOTE] 执行集成特征选择...")
        
        # 执行所有选择方法
        stat_features, stat_scores = self.method_1_statistical_selection(X, y)
        rf_features, rf_scores = self.method_2_random_forest_importance(X, y)
        mi_features, mi_scores = self.method_3_mutual_information(X, y)
        rfe_features, rfe_ranking = self.method_4_rfe_selection(X, y)
        
        # 统计每个特征被选中的次数
        feature_votes = {}
        all_features = list(X.columns)
        
        for feature in all_features:
            votes = 0
            if feature in stat_features:
                votes += 1
            if feature in rf_features:
                votes += 1
            if feature in mi_features:
                votes += 1
            if feature in rfe_features:
                votes += 1
            feature_votes[feature] = votes
        
        # 选择得票最多的特征
        sorted_by_votes = sorted(feature_votes.items(), 
                               key=lambda x: x[1], reverse=True)
        
        # 确保至少选择得票≥2的特征，不足则补充得票最多的
        high_vote_features = [f for f, v in sorted_by_votes if v >= 2]
        
        if len(high_vote_features) < self.max_features:
            remaining_slots = self.max_features - len(high_vote_features)
            additional_features = [f for f, v in sorted_by_votes if v == 1][:remaining_slots]
            final_features = high_vote_features + additional_features
        else:
            final_features = high_vote_features[:self.max_features]
        
        # 整合分数信息
        detailed_results = {
            'final_features': final_features,
            'feature_votes': feature_votes,
            'statistical_scores': stat_scores,
            'rf_importance': rf_scores,
            'mutual_info_scores': mi_scores,
            'rfe_ranking': rfe_ranking
        }
        
        self.selected_features = final_features
        
        return final_features, detailed_results
    
    def validate_feature_selection(self, X: pd.DataFrame, y: pd.Series,
                                 selected_features: List[str]) -> Dict:
        """验证特征选择效果"""
        
        print("[OK] 验证特征选择效果...")
        
        # 准备数据
        X_selected = X[selected_features].fillna(0)
        X_full = X.fillna(0)
        
        # 分割数据 (时间序列用后30%作为测试)
        split_point = int(len(X) * 0.7)
        
        X_train_full = X_full.iloc[:split_point]
        X_test_full = X_full.iloc[split_point:]
        X_train_selected = X_selected.iloc[:split_point]
        X_test_selected = X_selected.iloc[split_point:]
        y_train = y.iloc[:split_point]
        y_test = y.iloc[split_point:]
        
        # 模型对比 (简单逻辑回归)
        from sklearn.linear_model import LogisticRegression
        from sklearn.metrics import accuracy_score, precision_score, recall_score
        
        # 全特征模型
        model_full = LogisticRegression(random_state=42, max_iter=1000)
        model_full.fit(X_train_full, y_train)
        pred_full = model_full.predict(X_test_full)
        
        # 选择特征模型
        model_selected = LogisticRegression(random_state=42, max_iter=1000)
        model_selected.fit(X_train_selected, y_train)
        pred_selected = model_selected.predict(X_test_selected)
        
        # 计算指标
        results = {
            'full_features': {
                'count': X_full.shape[1],
                'accuracy': accuracy_score(y_test, pred_full),
                'precision': precision_score(y_test, pred_full),
                'recall': recall_score(y_test, pred_full)
            },
            'selected_features': {
                'count': len(selected_features),
                'accuracy': accuracy_score(y_test, pred_selected),
                'precision': precision_score(y_test, pred_selected),
                'recall': recall_score(y_test, pred_selected)
            }
        }
        
        # 计算改进效果
        results['improvement'] = {
            'feature_reduction': 1 - len(selected_features) / X_full.shape[1],
            'accuracy_change': results['selected_features']['accuracy'] - results['full_features']['accuracy'],
            'overfitting_risk_reduction': True if len(selected_features) < X_full.shape[1] * 0.3 else False
        }
        
        return results


class FeatureOptimizer:
    """特征优化器主类"""
    
    def __init__(self, max_features: int = 10):
        self.calculator = TechnicalIndicatorCalculator()
        self.selector = FeatureSelector(max_features)
        self.optimization_results = {}
        
    def optimize_strategy_features(self, df: pd.DataFrame) -> Dict:
        """完整的特征优化流程"""
        
        print("[ROCKET] 开始特征优化流程...")
        print("=" * 50)
        
        # 1. 计算精简技术指标
        print("📈 计算核心技术指标...")
        df_with_indicators = self.calculator.calculate_core_indicators(df)
        
        # 获取特征列 (排除OHLCV)
        feature_cols = [col for col in df_with_indicators.columns 
                       if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        print(f"   原始特征数量: {len(feature_cols)}")
        
        # 2. 准备标签
        labels = self.selector.prepare_labels(df_with_indicators)
        
        # 3. 特征选择
        X = df_with_indicators[feature_cols]
        y = labels
        
        # 移除缺失值过多的行
        valid_mask = ~(X.isnull().sum(axis=1) > len(feature_cols) * 0.5)
        X = X[valid_mask]
        y = y[valid_mask]
        
        print(f"   有效样本数量: {len(X)}")
        
        # 4. 执行集成特征选择
        selected_features, selection_details = self.selector.ensemble_feature_selection(X, y)
        
        print(f"   最终选择特征: {len(selected_features)}")
        print(f"   特征缩减率: {(1 - len(selected_features)/len(feature_cols)):.1%}")
        
        # 5. 验证选择效果
        validation_results = self.selector.validate_feature_selection(X, y, selected_features)
        
        # 6. 汇总结果
        self.optimization_results = {
            'original_feature_count': len(feature_cols),
            'selected_feature_count': len(selected_features),
            'selected_features': selected_features,
            'selection_details': selection_details,
            'validation_results': validation_results,
            'feature_list': feature_cols
        }
        
        print("[OK] 特征优化完成!")
        
        return self.optimization_results
    
    def generate_optimization_report(self) -> str:
        """生成优化报告"""
        
        if not self.optimization_results:
            return "请先运行 optimize_strategy_features()"
        
        results = self.optimization_results
        val_results = results['validation_results']
        
        report = f"""
# [TARGET] 特征优化报告

## [CHART] 优化概览
- **原始特征数量**: {results['original_feature_count']}
- **优化后特征数量**: {results['selected_feature_count']}
- **特征缩减率**: {(1 - results['selected_feature_count']/results['original_feature_count']):.1%}

## [TROPHY] 最终选择的特征
{chr(10).join([f"- {feature}" for feature in results['selected_features']])}

## 📈 模型性能对比

### 全特征模型 ({val_results['full_features']['count']}个特征)
- **准确率**: {val_results['full_features']['accuracy']:.3f}
- **精确率**: {val_results['full_features']['precision']:.3f}
- **召回率**: {val_results['full_features']['recall']:.3f}

### 优化特征模型 ({val_results['selected_features']['count']}个特征)
- **准确率**: {val_results['selected_features']['accuracy']:.3f}
- **精确率**: {val_results['selected_features']['precision']:.3f}
- **召回率**: {val_results['selected_features']['recall']:.3f}

## [TARGET] 优化效果
- **准确率变化**: {val_results['improvement']['accuracy_change']:+.3f}
- **过拟合风险**: {'显著降低' if val_results['improvement']['overfitting_risk_reduction'] else '略有降低'}
- **模型复杂度**: 大幅降低 ({(1-results['selected_feature_count']/results['original_feature_count']):.0%}减少)

## 💡 优化建议
1. **保留核心特征**: 经多方法验证的高价值指标
2. **定期重新评估**: 每季度重新运行特征选择
3. **监控特征稳定性**: 避免特征漂移影响模型性能
4. **渐进式调整**: 新增特征应谨慎验证

## [WARN] 风险提示
- 特征选择基于历史数据，未来效果需持续验证
- 建议配合Walk-forward验证确保稳健性
- 避免在选择特征上过度优化造成新的过拟合
"""
        
        return report
    
    def save_optimization_results(self, filename: str = "feature_optimization_results"):
        """保存优化结果"""
        
        # 保存选择的特征列表
        selected_df = pd.DataFrame(self.optimization_results['selected_features'], 
                                 columns=['selected_features'])
        selected_df.to_csv(f'{filename}_features.csv', index=False)
        
        # 保存完整报告
        report = self.generate_optimization_report()
        with open(f'{filename}_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 保存详细结果 (用于后续分析)
        import json
        with open(f'{filename}_details.json', 'w') as f:
            # 将numpy类型转换为Python原生类型
            def convert_numpy(obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                return obj
            
            # 递归转换
            def clean_for_json(data):
                if isinstance(data, dict):
                    return {k: clean_for_json(v) for k, v in data.items()}
                elif isinstance(data, list):
                    return [clean_for_json(v) for v in data]
                else:
                    return convert_numpy(data)
            
            clean_results = clean_for_json(self.optimization_results)
            json.dump(clean_results, f, indent=2)
        
        print(f"[OK] 优化结果已保存:")
        print(f"   - 特征列表: {filename}_features.csv")
        print(f"   - 优化报告: {filename}_report.md")
        print(f"   - 详细结果: {filename}_details.json")


# 使用示例
if __name__ == "__main__":
    print("[TARGET] 特征选择优化器测试")
    
    # 创建模拟数据进行测试
    dates = pd.date_range('2023-01-01', periods=1000, freq='15min')
    mock_data = pd.DataFrame({
        'open': np.random.randn(1000).cumsum() + 50000,
        'high': np.random.randn(1000).cumsum() + 50200,
        'low': np.random.randn(1000).cumsum() + 49800,
        'close': np.random.randn(1000).cumsum() + 50000,
        'volume': np.random.exponential(1000, 1000)
    }, index=dates)
    
    # 确保OHLC逻辑正确
    mock_data['high'] = np.maximum(mock_data[['open', 'close']].max(axis=1), mock_data['high'])
    mock_data['low'] = np.minimum(mock_data[['open', 'close']].min(axis=1), mock_data['low'])
    
    # 运行特征优化
    optimizer = FeatureOptimizer(max_features=8)
    results = optimizer.optimize_strategy_features(mock_data)
    
    # 输出结果
    print(f"\n[CHART] 优化结果:")
    print(f"   原始特征: {results['original_feature_count']}")
    print(f"   最终特征: {results['selected_feature_count']}")
    print(f"   缩减率: {(1-results['selected_feature_count']/results['original_feature_count']):.1%}")
    
    print(f"\n[TROPHY] 选择的特征:")
    for feature in results['selected_features']:
        print(f"   - {feature}")
    
    # 保存结果
    optimizer.save_optimization_results("feature_selection_test")
    
    print("\n[OK] 特征选择优化器已就绪!")
    print("[WARN] 下一步: 集成到回测系统，验证实际效果") 