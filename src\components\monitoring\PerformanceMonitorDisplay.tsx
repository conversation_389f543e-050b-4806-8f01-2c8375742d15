// 性能监控显示组件 - 实时展示系统性能指标和告警
import { useState, useEffect } from 'react'
import { performanceMonitor, PerformanceMetrics, PerformanceAlert } from '../../services/performanceMonitor'

interface PerformanceMonitorDisplayProps {
  isExpanded?: boolean
  onToggle?: () => void
}

export function PerformanceMonitorDisplay({ isExpanded = false, onToggle }: PerformanceMonitorDisplayProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([])
  const [performanceSummary, setPerformanceSummary] = useState<any>(null)

  useEffect(() => {
    // 订阅性能指标更新
    const unsubscribeMetrics = performanceMonitor.subscribe((newMetrics) => {
      setMetrics(newMetrics)
      
      // 每次更新时也更新性能摘要
      const summary = performanceMonitor.getPerformanceSummary()
      setPerformanceSummary(summary)
    })

    // 订阅告警
    const unsubscribeAlerts = performanceMonitor.subscribeToAlerts((alert) => {
      setAlerts(prev => {
        // 添加新告警到列表顶部，保持最多10个
        const updated = [alert, ...prev].slice(0, 10)
        return updated
      })
    })

    // 初始化当前数据
    const currentMetrics = performanceMonitor.getCurrentMetrics()
    const currentAlerts = performanceMonitor.getCurrentAlerts()
    const summary = performanceMonitor.getPerformanceSummary()
    
    setMetrics(currentMetrics)
    setAlerts(currentAlerts.slice(0, 10)) // 最多显示10个告警
    setPerformanceSummary(summary)

    return () => {
      unsubscribeMetrics()
      unsubscribeAlerts()
    }
  }, [])

  if (!metrics) {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
          <span className="text-gray-300 text-sm">性能监控初始化中...</span>
        </div>
      </div>
    )
  }

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-green-400'
    if (score >= 75) return 'text-blue-400' 
    if (score >= 60) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getPerformanceIcon = (score: number) => {
    if (score >= 90) return '🚀'
    if (score >= 75) return '✅'
    if (score >= 60) return '⚠️'
    return '🚨'
  }

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  const formatBytes = (bytes: number) => {
    if (bytes < 1024) return `${bytes.toFixed(1)}B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`
    return `${(bytes / 1024 / 1024).toFixed(1)}MB`
  }

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`
  }

  const getAlertIcon = (level: PerformanceAlert['level']) => {
    switch (level) {
      case 'CRITICAL': return '🚨'
      case 'WARNING': return '⚠️'
      case 'INFO': return 'ℹ️'
      default: return '📊'
    }
  }

  const getAlertColor = (level: PerformanceAlert['level']) => {
    switch (level) {
      case 'CRITICAL': return 'text-red-400'
      case 'WARNING': return 'text-yellow-400'
      case 'INFO': return 'text-blue-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <div className="bg-gray-800 rounded-lg overflow-hidden">
      {/* 性能监控头部 */}
      <div 
        className="p-4 border-b border-gray-700 cursor-pointer hover:bg-gray-750 transition-colors"
        onClick={onToggle}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white font-medium">性能监控</span>
            </div>
            
            {performanceSummary && (
              <div className="flex items-center space-x-2">
                <span className="text-2xl">{getPerformanceIcon(performanceSummary.score)}</span>
                <span className={`font-bold ${getPerformanceColor(performanceSummary.score)}`}>
                  {performanceSummary.overall}
                </span>
                <span className="text-gray-400 text-sm">
                  ({performanceSummary.score.toFixed(0)}/100)
                </span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            {/* 关键指标简览 */}
            <div className="flex items-center space-x-3 text-sm">
              <div className="flex items-center space-x-1">
                <span className="text-gray-400">CPU:</span>
                <span className={metrics.system.cpuUsage > 70 ? 'text-red-400' : 'text-green-400'}>
                  {metrics.system.cpuUsage.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <span className="text-gray-400">延迟:</span>
                <span className={metrics.api.responseTime > 1000 ? 'text-red-400' : 'text-green-400'}>
                  {formatDuration(metrics.api.responseTime)}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <span className="text-gray-400">成交率:</span>
                <span className={metrics.trading.fillRate < 0.9 ? 'text-red-400' : 'text-green-400'}>
                  {formatPercentage(metrics.trading.fillRate)}
                </span>
              </div>
            </div>
            
            {/* 展开/折叠图标 */}
            <div className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* 详细性能指标 */}
      {isExpanded && (
        <div className="p-4 space-y-6">
          {/* 系统性能 */}
          <div>
            <h4 className="text-white font-medium mb-3 flex items-center">
              🖥️ 系统性能
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">CPU使用率</div>
                <div className={`text-lg font-bold ${metrics.system.cpuUsage > 70 ? 'text-red-400' : 'text-green-400'}`}>
                  {metrics.system.cpuUsage.toFixed(1)}%
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">内存使用</div>
                <div className={`text-lg font-bold ${metrics.system.memoryUsage > 80 ? 'text-red-400' : 'text-green-400'}`}>
                  {metrics.system.memoryUsage.toFixed(1)}%
                </div>
                <div className="text-xs text-gray-500">
                  {formatBytes(metrics.system.heapUsed * 1024 * 1024)} / {formatBytes(metrics.system.heapTotal * 1024 * 1024)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">运行时间</div>
                <div className="text-lg font-bold text-blue-400">
                  {formatDuration(metrics.system.uptime * 1000)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">负载</div>
                <div className="text-lg font-bold text-purple-400">
                  {metrics.system.loadAverage[0]?.toFixed(2) || '0.00'}
                </div>
              </div>
            </div>
          </div>

          {/* API性能 */}
          <div>
            <h4 className="text-white font-medium mb-3 flex items-center">
              📡 API性能
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">响应时间</div>
                <div className={`text-lg font-bold ${metrics.api.responseTime > 1000 ? 'text-red-400' : 'text-green-400'}`}>
                  {formatDuration(metrics.api.responseTime)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">错误率</div>
                <div className={`text-lg font-bold ${metrics.api.errorRate > 0.05 ? 'text-red-400' : 'text-green-400'}`}>
                  {formatPercentage(metrics.api.errorRate)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">吞吐量</div>
                <div className="text-lg font-bold text-blue-400">
                  {metrics.api.throughput.toFixed(1)}/s
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">币安API延迟</div>
                <div className={`text-lg font-bold ${metrics.api.binanceApiLatency > 500 ? 'text-yellow-400' : 'text-green-400'}`}>
                  {formatDuration(metrics.api.binanceApiLatency)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">WebSocket延迟</div>
                <div className={`text-lg font-bold ${metrics.api.websocketLatency > 200 ? 'text-yellow-400' : 'text-green-400'}`}>
                  {formatDuration(metrics.api.websocketLatency)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">请求总数</div>
                <div className="text-lg font-bold text-purple-400">
                  {metrics.api.requestCount.toLocaleString()}
                </div>
              </div>
            </div>
          </div>

          {/* 交易性能 */}
          <div>
            <h4 className="text-white font-medium mb-3 flex items-center">
              ⚡ 交易性能
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">执行时间</div>
                <div className={`text-lg font-bold ${metrics.trading.executionTime > 3000 ? 'text-red-400' : 'text-green-400'}`}>
                  {formatDuration(metrics.trading.executionTime)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">成交率</div>
                <div className={`text-lg font-bold ${metrics.trading.fillRate < 0.9 ? 'text-red-400' : 'text-green-400'}`}>
                  {formatPercentage(metrics.trading.fillRate)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">平均滑点</div>
                <div className={`text-lg font-bold ${metrics.trading.slippageAvg > 0.001 ? 'text-yellow-400' : 'text-green-400'}`}>
                  {formatPercentage(metrics.trading.slippageAvg)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">信号延迟</div>
                <div className="text-lg font-bold text-blue-400">
                  {formatDuration(metrics.trading.signalLatency)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">订单延迟</div>
                <div className="text-lg font-bold text-purple-400">
                  {formatDuration(metrics.trading.orderLatency)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">策略效率</div>
                <div className={`text-lg font-bold ${metrics.trading.strategyEfficiency < 0.7 ? 'text-yellow-400' : 'text-green-400'}`}>
                  {formatPercentage(metrics.trading.strategyEfficiency)}
                </div>
              </div>
            </div>
          </div>

          {/* 数据流性能 */}
          <div>
            <h4 className="text-white font-medium mb-3 flex items-center">
              🔄 数据流性能
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">K线更新率</div>
                <div className="text-lg font-bold text-blue-400">
                  {metrics.dataFlow.klineUpdateRate.toFixed(1)}/s
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">Ticker更新率</div>
                <div className="text-lg font-bold text-green-400">
                  {metrics.dataFlow.tickerUpdateRate.toFixed(1)}/s
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">处理延迟</div>
                <div className={`text-lg font-bold ${metrics.dataFlow.processingDelay > 100 ? 'text-yellow-400' : 'text-green-400'}`}>
                  {formatDuration(metrics.dataFlow.processingDelay)}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">队列大小</div>
                <div className={`text-lg font-bold ${metrics.dataFlow.dataQueueSize > 50 ? 'text-yellow-400' : 'text-green-400'}`}>
                  {metrics.dataFlow.dataQueueSize}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-xs text-gray-400 mb-1">数据丢失率</div>
                <div className={`text-lg font-bold ${metrics.dataFlow.dataLossRate > 0.01 ? 'text-red-400' : 'text-green-400'}`}>
                  {formatPercentage(metrics.dataFlow.dataLossRate)}
                </div>
              </div>
            </div>
          </div>

          {/* 性能告警 */}
          {alerts.length > 0 && (
            <div>
              <h4 className="text-white font-medium mb-3 flex items-center">
                🚨 性能告警
                <span className="ml-2 bg-red-600 text-white text-xs px-2 py-1 rounded-full">
                  {alerts.filter(a => !a.resolved).length}
                </span>
              </h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {alerts.slice(0, 5).map((alert) => (
                  <div 
                    key={alert.id}
                    className={`p-3 rounded border-l-4 ${
                      alert.level === 'CRITICAL' ? 'bg-red-900/20 border-red-500' :
                      alert.level === 'WARNING' ? 'bg-yellow-900/20 border-yellow-500' :
                      'bg-blue-900/20 border-blue-500'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-2">
                        <span className="text-lg">{getAlertIcon(alert.level)}</span>
                        <div>
                          <div className={`font-medium ${getAlertColor(alert.level)}`}>
                            [{alert.level}] {alert.category.toUpperCase()}
                          </div>
                          <div className="text-gray-300 text-sm mt-1">
                            {alert.message}
                          </div>
                          <div className="text-gray-500 text-xs mt-1">
                            {new Date(alert.timestamp).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                      {!alert.resolved && (
                        <button
                          onClick={() => {
                            performanceMonitor.resolveAlert(alert.id)
                            setAlerts(prev => prev.map(a => 
                              a.id === alert.id ? { ...a, resolved: true } : a
                            ))
                          }}
                          className="text-gray-400 hover:text-white text-xs px-2 py-1 rounded border border-gray-600 hover:border-gray-500 transition-colors"
                        >
                          解决
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 性能建议 */}
          {performanceSummary && performanceSummary.recommendations.length > 0 && (
            <div>
              <h4 className="text-white font-medium mb-3 flex items-center">
                💡 优化建议
              </h4>
              <div className="space-y-2">
                {performanceSummary.recommendations.map((recommendation: string, index: number) => (
                  <div key={index} className="flex items-start space-x-2 text-sm">
                    <span className="text-blue-400 mt-1">•</span>
                    <span className="text-gray-300">{recommendation}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
} 