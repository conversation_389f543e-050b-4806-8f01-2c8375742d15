import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select'
import { Badge } from '../components/ui/badge'
import { tradingDatabase, TradeRecord } from '../services/tradingDatabase'
import { format } from 'date-fns'

// 交易记录表格组件
const TradeRecordTable: React.FC<{ trades: TradeRecord[] }> = ({ trades }) => {
  const [expandedRow, setExpandedRow] = useState<string | null>(null)

  const formatPrice = (price: number) => price.toFixed(4)
  const formatPercent = (value: number) => `${(value * 100).toFixed(2)}%`
  const formatTimestamp = (timestamp: number) => format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss')

  // 翻译动作类型
  const translateAction = (action: string, side: string) => {
    const actionMap: { [key: string]: string } = {
      'open': '开仓',
      'close': '平仓',
      'partial_close': '部分平仓'
    }
    const sideMap: { [key: string]: string } = {
      'buy': '买入',
      'sell': '卖出'
    }
    return `${actionMap[action] || action} ${sideMap[side] || side}`
  }

  // 翻译策略类型
  const translateStrategy = (strategy: string) => {
    const strategyMap: { [key: string]: string } = {
      'scalping': '超短线',
      'trend': '趋势',
      'grid': '网格',
      'super_trend': '超强趋势',
      'breakout': '突破',
      'mean_reversion': '均值回归',
      'momentum': '动量',
      'arbitrage': '套利'
    }
    return strategyMap[strategy] || strategy
  }

  // 翻译执行状态
  const translateStatus = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'success': '成功',
      'failed': '失败',
      'pending': '待处理',
      'cancelled': '已取消',
      'partial': '部分成交',
      'rejected': '已拒绝'
    }
    return statusMap[status] || status
  }

  // 翻译市场状态
  const translateMarketState = (state: string) => {
    const stateMap: { [key: string]: string } = {
      'SIDEWAYS': '震荡',
      'UPTREND': '上涨',
      'DOWNTREND': '下跌',
      'VOLATILE': '高波动',
      'BREAKOUT': '突破',
      'SUPER_TREND': '超强趋势'
    }
    return stateMap[state] || state
  }

  // 翻译风险等级
  const translateRiskLevel = (level: string) => {
    const levelMap: { [key: string]: string } = {
      'LOW': '低风险',
      'MEDIUM': '中风险',
      'HIGH': '高风险',
      'CRITICAL': '极高风险'
    }
    return levelMap[level] || level
  }

  const getActionColor = (action: string, side: string) => {
    if (action === 'open') {
      return side === 'buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
    }
    return 'bg-gray-100 text-gray-800'
  }

  const getPnlColor = (pnl?: number) => {
    if (!pnl) return 'text-gray-600'
    return pnl > 0 ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'
  }

  return (
    <div className="overflow-x-auto bg-gray-900 rounded-lg">
      <table className="w-full text-sm">
        <thead>
          <tr className="border-b border-gray-700 bg-gray-800/50">
            <th className="p-3 text-left text-gray-200">时间</th>
            <th className="p-3 text-left text-gray-200">币种</th>
            <th className="p-3 text-left text-gray-200">动作</th>
            <th className="p-3 text-left text-gray-200">策略</th>
            <th className="p-3 text-left text-gray-200">价格</th>
            <th className="p-3 text-left text-gray-200">数量</th>
            <th className="p-3 text-left text-gray-200">杠杆</th>
            <th className="p-3 text-left text-gray-200">盈亏</th>
            <th className="p-3 text-left text-gray-200">状态</th>
            <th className="p-3 text-left text-gray-200">操作</th>
          </tr>
        </thead>
        <tbody>
          {trades.map((trade) => (
            <React.Fragment key={trade.id}>
              <tr className="border-b border-gray-700 hover:bg-gray-800/50 text-gray-200">
                <td className="p-3 text-xs text-gray-400">
                  {formatTimestamp(trade.timestamp)}
                </td>
                <td className="p-3 font-medium text-gray-200">{trade.symbol}</td>
                <td className="p-3">
                  <Badge className={getActionColor(trade.action, trade.side)}>
                    {translateAction(trade.action, trade.side)}
                  </Badge>
                </td>
                <td className="p-3">
                  <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded border border-blue-500/30">
                    {translateStrategy(trade.strategy)}
                  </span>
                </td>
                <td className="p-3 font-mono text-gray-200">{formatPrice(trade.price)}</td>
                <td className="p-3 font-mono text-gray-200">{trade.quantity.toFixed(4)}</td>
                <td className="p-3 text-gray-200">{trade.leverage.toFixed(1)}x</td>
                <td className={`p-3 font-mono ${getPnlColor(trade.pnl)}`}>
                  {trade.pnl ? `${trade.pnl > 0 ? '+' : ''}${trade.pnl.toFixed(2)}` : '-'}
                </td>
                <td className="p-3">
                  <Badge className={trade.execution.status === 'success' ? 'bg-green-500/20 text-green-400 border border-green-500/30' : 'bg-red-500/20 text-red-400 border border-red-500/30'}>
                    {translateStatus(trade.execution.status)}
                  </Badge>
                </td>
                <td className="p-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setExpandedRow(expandedRow === trade.id ? null : trade.id)}
                  >
                    {expandedRow === trade.id ? '收起' : '详情'}
                  </Button>
                </td>
              </tr>
              {expandedRow === trade.id && (
                <tr>
                  <td colSpan={10} className="p-0">
                    <div className="bg-gray-800/80 p-4 border-t border-gray-600">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* 技术指标 */}
                        <Card className="bg-gray-700/50 border-gray-600">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm text-gray-200">技术指标</CardTitle>
                          </CardHeader>
                          <CardContent className="text-xs space-y-1">
                            <div className="flex justify-between">
                              <span className="text-gray-300">RSI:</span>
                              <span className="font-mono text-gray-200">{trade.indicators.rsi.toFixed(1)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">EMA:</span>
                              <span className="font-mono text-gray-200">{formatPrice(trade.indicators.ema)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">动量:</span>
                              <span className="font-mono text-gray-200">{formatPercent(trade.indicators.momentum)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">波动率:</span>
                              <span className="font-mono text-gray-200">{formatPercent(trade.indicators.volatility)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">市场状态:</span>
                              <Badge className="text-xs bg-purple-500/20 text-purple-400 border border-purple-500/30">{translateMarketState(trade.indicators.marketState)}</Badge>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">趋势强度:</span>
                              <span className="font-mono text-gray-200">{formatPercent(trade.indicators.trendStrength)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">支撑位:</span>
                              <span className="font-mono text-gray-200">{formatPrice(trade.indicators.supportLevel)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">阻力位:</span>
                              <span className="font-mono text-gray-200">{formatPrice(trade.indicators.resistanceLevel)}</span>
                            </div>
                          </CardContent>
                        </Card>

                        {/* 账户快照 */}
                        <Card className="bg-gray-700/50 border-gray-600">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm text-gray-200">账户状态</CardTitle>
                          </CardHeader>
                          <CardContent className="text-xs space-y-1">
                            <div className="flex justify-between">
                              <span className="text-gray-300">总权益:</span>
                              <span className="font-mono text-gray-200">{trade.accountSnapshot.totalEquity.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">可用保证金:</span>
                              <span className="font-mono text-gray-200">{trade.accountSnapshot.availableMargin.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">已用保证金:</span>
                              <span className="font-mono text-gray-200">{trade.accountSnapshot.usedMargin.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>未实现盈亏:</span>
                              <span className={`font-mono ${getPnlColor(trade.accountSnapshot.unrealizedPnl)}`}>
                                {trade.accountSnapshot.unrealizedPnl.toFixed(2)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>已实现盈亏:</span>
                              <span className={`font-mono ${getPnlColor(trade.accountSnapshot.realizedPnl)}`}>
                                {trade.accountSnapshot.realizedPnl.toFixed(2)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">保证金比率:</span>
                              <span className="font-mono text-gray-200">{formatPercent(trade.accountSnapshot.marginRatio)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">持仓数量:</span>
                              <span className="font-mono text-gray-200">{trade.accountSnapshot.positions}</span>
                            </div>
                          </CardContent>
                        </Card>

                        {/* 风险管理 */}
                        <Card className="bg-gray-700/50 border-gray-600">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm text-gray-200">风险管理</CardTitle>
                          </CardHeader>
                          <CardContent className="text-xs space-y-1">
                            {trade.riskMetrics.stopLossPrice && (
                              <div className="flex justify-between">
                                <span className="text-gray-300">止损价:</span>
                                <span className="font-mono text-red-400">{formatPrice(trade.riskMetrics.stopLossPrice)}</span>
                              </div>
                            )}
                            {trade.riskMetrics.takeProfitPrice && (
                              <div className="flex justify-between">
                                <span className="text-gray-300">止盈价:</span>
                                <span className="font-mono text-green-400">{formatPrice(trade.riskMetrics.takeProfitPrice)}</span>
                              </div>
                            )}
                            <div className="flex justify-between">
                              <span className="text-gray-300">风险等级:</span>
                              <Badge className={
                                trade.riskMetrics.riskLevel === 'LOW' ? 'bg-green-500/20 text-green-400 border border-green-500/30' :
                                trade.riskMetrics.riskLevel === 'MEDIUM' ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30' :
                                trade.riskMetrics.riskLevel === 'HIGH' ? 'bg-orange-500/20 text-orange-400 border border-orange-500/30' :
                                'bg-red-500/20 text-red-400 border border-red-500/30'
                              }>
                                {translateRiskLevel(trade.riskMetrics.riskLevel)}
                              </Badge>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">回撤:</span>
                              <span className="font-mono text-gray-200">{formatPercent(trade.riskMetrics.drawdown)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">仓位暴露:</span>
                              <span className="font-mono text-gray-200">{formatPercent(trade.riskMetrics.exposureRatio)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-300">相关性风险:</span>
                              <span className="font-mono text-gray-200">{formatPercent(trade.riskMetrics.correlationRisk)}</span>
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      {/* 执行详情 */}
                      <div className="mt-4 p-3 bg-gray-700/50 rounded border border-gray-600">
                        <h4 className="text-sm font-medium mb-2 text-gray-200">执行详情</h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                          <div>
                            <span className="text-gray-400">订单ID:</span>
                            <div className="font-mono text-xs break-all text-gray-200">{trade.execution.orderId}</div>
                          </div>
                          <div>
                            <span className="text-gray-400">成交价:</span>
                            <div className="font-mono text-gray-200">{formatPrice(trade.execution.fillPrice)}</div>
                          </div>
                          <div>
                            <span className="text-gray-400">成交量:</span>
                            <div className="font-mono text-gray-200">{trade.execution.fillQuantity.toFixed(4)}</div>
                          </div>
                          <div>
                            <span className="text-gray-400">手续费:</span>
                            <div className="font-mono text-gray-200">{trade.execution.commission.toFixed(4)}</div>
                          </div>
                          <div>
                            <span className="text-gray-400">滑点:</span>
                            <div className="font-mono text-gray-200">{trade.execution.slippage.toFixed(4)}</div>
                          </div>
                          <div>
                            <span className="text-gray-400">执行时间:</span>
                            <div className="font-mono text-gray-200">{trade.execution.executionTime}ms</div>
                          </div>
                          <div>
                            <span className="text-gray-400">置信度:</span>
                            <div className="font-mono text-gray-200">{formatPercent(trade.confidence)}</div>
                          </div>
                          <div>
                            <span className="text-gray-400">交易原因:</span>
                            <div className="text-xs text-gray-200">{trade.reason}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// 主页面组件
export const TradingHistoryPage: React.FC = () => {
  const [trades, setTrades] = useState<TradeRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalTrades: 0,
    winRate: 0,
    avgProfit: 0,
    maxDrawdown: 0,
    profitFactor: 0
  })

  // 过滤器状态
  const [filters, setFilters] = useState({
    symbol: 'all',
    strategy: 'all',
    timeRange: '24h'
  })

  // 策略执行状态
  const [strategyStatus, setStrategyStatus] = useState<{
    isRunning: boolean
    totalTrades: number
  }>({ isRunning: false, totalTrades: 0 })

  // 加载交易数据
  const loadTrades = async () => {
    setLoading(true)
    try {
      console.log('🔄 开始加载交易数据...')
      const now = Date.now()
      let startTime: number | undefined
      
      switch (filters.timeRange) {
        case '1h':
          startTime = now - (60 * 60 * 1000)
          break
        case '24h':
          startTime = now - (24 * 60 * 60 * 1000)
          break
        case '7d':
          startTime = now - (7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startTime = now - (30 * 24 * 60 * 60 * 1000)
          break
      }

      console.log('🔍 查询参数:', {
        symbol: filters.symbol === 'all' ? '全部' : filters.symbol,
        strategy: filters.strategy === 'all' ? '全部' : filters.strategy,
        timeRange: filters.timeRange,
        startTime: startTime ? new Date(startTime).toLocaleString() : '无限制',
        limit: 500
      })

      const tradeData = await tradingDatabase.getTrades({
        symbol: filters.symbol === 'all' ? undefined : filters.symbol,
        strategy: filters.strategy === 'all' ? undefined : filters.strategy,
        startTime,
        limit: 500
      })

      console.log(`✅ 查询到 ${tradeData.length} 条交易记录`)
      setTrades(tradeData)

      // 加载统计数据
      const statsData = await tradingDatabase.getTradeStatistics(
        filters.symbol === 'all' ? undefined : filters.symbol,
        filters.strategy === 'all' ? undefined : filters.strategy
      )
      console.log('📊 统计数据:', statsData)
      setStats(statsData)

    } catch (error) {
      console.error('❌ 加载交易数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 导出数据
  const exportData = async () => {
    try {
      const now = Date.now()
      const startTime = filters.timeRange === '30d' ? now - (30 * 24 * 60 * 60 * 1000) : undefined
      
      const data = await tradingDatabase.exportData(startTime, now)
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `trading_data_${format(new Date(), 'yyyy-MM-dd_HH-mm-ss')}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      console.log('✅ 数据导出成功')
    } catch (error) {
      console.error('❌ 数据导出失败:', error)
    }
  }

  // 清理旧数据
  const cleanupData = async () => {
    try {
      await tradingDatabase.cleanupOldData(90)
      console.log('✅ 旧数据清理完成')
      loadTrades() // 重新加载数据
    } catch (error) {
      console.error('❌ 数据清理失败:', error)
    }
  }

  // 🔧 获取策略执行状态
  const getStrategyStatus = () => {
    try {
      // 从全局窗口对象获取策略执行器状态
      const status = (window as any).strategyExecutor?.getStrategyStatus?.()
      if (status) {
        return {
          isRunning: status.isRunning || false,
          totalTrades: status.totalTrades || 0
        }
      }
      return { isRunning: false, totalTrades: 0 }
    } catch (error) {
      console.error('❌ 获取策略状态失败:', error)
      return { isRunning: false, totalTrades: 0 }
    }
  }

  // 🔄 清理所有数据 (准备接收真实交易记录)
  const clearAllData = async () => {
    try {
      console.log('🧹 清理所有交易数据，准备接收真实交易记录...')
      
      // 清理所有历史数据（1天内的数据，实际上会清理所有数据）
      await tradingDatabase.cleanupOldData(0)
      
      console.log('✅ 所有交易数据已清理，准备记录真实交易')
      
      // 重新加载数据
      await loadTrades()
      
    } catch (error) {
      console.error('❌ 清理数据失败:', error)
    }
  }

  useEffect(() => {
    console.log('🔍 交易历史页面初始化，开始加载数据...')
    console.log('📊 当前过滤器:', filters)
    loadTrades()
    
    // 更新策略状态
    const status = getStrategyStatus()
    setStrategyStatus(status)
  }, [filters])

  // 定期更新策略状态
  useEffect(() => {
    const updateStatusInterval = setInterval(() => {
      const status = getStrategyStatus()
      setStrategyStatus(status)
    }, 5000) // 每5秒更新一次

    return () => clearInterval(updateStatusInterval)
  }, [])

  // 添加调试信息
  useEffect(() => {
    console.log('📊 交易历史页面状态:', {
      tradesCount: trades.length,
      loading,
      stats,
      filters
    })
  }, [trades, loading, stats, filters])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">正在加载交易数据...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold">📊 交易历史记录</h1>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${strategyStatus.isRunning ? 'bg-green-500' : 'bg-gray-400'}`}></div>
            <span className="text-sm text-gray-600">
              {strategyStatus.isRunning ? '🟢 策略运行中' : '⚪ 策略未启动'}
            </span>
            <span className="text-xs text-gray-500">
              ({strategyStatus.totalTrades} 笔交易)
            </span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportData}>
            导出数据
          </Button>
          <Button variant="outline" onClick={cleanupData}>
            清理旧数据
          </Button>
          <Button variant="outline" onClick={clearAllData}>
            清空所有数据
          </Button>
          <Button onClick={loadTrades}>
            刷新数据
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats.totalTrades}</div>
            <div className="text-xs text-gray-600">总交易次数</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.winRate.toFixed(1)}%</div>
            <div className="text-xs text-gray-600">胜率</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className={`text-2xl font-bold ${stats.avgProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {stats.avgProfit >= 0 ? '+' : ''}{stats.avgProfit.toFixed(2)}
            </div>
            <div className="text-xs text-gray-600">平均盈亏</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.maxDrawdown.toFixed(1)}%</div>
            <div className="text-xs text-gray-600">最大回撤</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.profitFactor.toFixed(2)}</div>
            <div className="text-xs text-gray-600">盈利因子</div>
          </CardContent>
        </Card>
      </div>

      {/* 过滤器 */}
      <Card>
        <CardHeader>
          <CardTitle>筛选条件</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">币种</label>
              <Select value={filters.symbol} onValueChange={(value) => setFilters({...filters, symbol: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="选择币种" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部币种</SelectItem>
                  <SelectItem value="BTCUSDT">BTCUSDT</SelectItem>
                  <SelectItem value="ETHUSDT">ETHUSDT</SelectItem>
                  <SelectItem value="BNBUSDT">BNBUSDT</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">策略</label>
              <Select value={filters.strategy} onValueChange={(value) => setFilters({...filters, strategy: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="选择策略" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部策略</SelectItem>
                  <SelectItem value="scalping">超短线</SelectItem>
                  <SelectItem value="trend">趋势</SelectItem>
                  <SelectItem value="grid">网格</SelectItem>
                  <SelectItem value="super_trend">超强趋势</SelectItem>
                  <SelectItem value="breakout">突破</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">时间范围</label>
              <Select value={filters.timeRange} onValueChange={(value) => setFilters({...filters, timeRange: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">近1小时</SelectItem>
                  <SelectItem value="24h">近24小时</SelectItem>
                  <SelectItem value="7d">近7天</SelectItem>
                  <SelectItem value="30d">近30天</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 交易记录表格 */}
      <Card>
        <CardHeader>
          <CardTitle>交易记录详情</CardTitle>
          <div className="text-sm text-gray-600">
            共 {trades.length} 条记录 | 点击"详情"查看完整技术指标和风险信息
          </div>
        </CardHeader>
        <CardContent>
          {trades.length > 0 ? (
            <TradeRecordTable trades={trades} />
          ) : (
            <div className="text-center py-12 space-y-4">
              <div className="text-gray-500 text-lg">📊 暂无交易数据</div>
              <div className="text-sm text-gray-400 max-w-md mx-auto">
                当前没有交易记录。开始真实交易的步骤：
              </div>
              <div className="space-y-2">
                <div className="text-sm text-gray-600">
                  1️⃣ 前往实时监控页面 → 点击"🚀 启动交易"
                </div>
                <div className="text-sm text-gray-600">
                  2️⃣ 策略执行器会自动记录真实开仓/平仓数据
                </div>
                <div className="text-sm text-gray-600">
                  3️⃣ 交易记录将实时显示在此页面
                </div>
              </div>
              <div className="pt-4">
                <Button onClick={() => window.location.href = '/'} className="bg-blue-600 hover:bg-blue-700">
                  🚀 前往启动交易
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default TradingHistoryPage 