@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* 科技风格卡片 */
  .tech-card {
    @apply relative overflow-hidden rounded-lg border border-border/50 bg-card/50 backdrop-blur-sm;
  }
  
  .tech-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent pointer-events-none;
  }
  
  /* 发光边框效果 */
  .glow-border {
    @apply relative;
  }
  
  .glow-border::before {
    content: '';
    @apply absolute -inset-0.5 bg-gradient-to-r from-primary via-purple-500 to-primary rounded-lg opacity-75 blur-sm -z-10;
  }
  
  /* 数据指标卡片 */
  .metric-card {
    @apply tech-card p-6 transition-all duration-300 hover:shadow-lg hover:shadow-primary/10;
  }
  
  /* 状态指示器 */
  .status-online {
    @apply inline-flex items-center gap-2 text-sm font-medium text-success-600;
  }
  
  .status-warning {
    @apply inline-flex items-center gap-2 text-sm font-medium text-warning-600;
  }
  
  .status-error {
    @apply inline-flex items-center gap-2 text-sm font-medium text-danger-600;
  }
  
  /* 脉冲动画点 */
  .pulse-dot {
    @apply relative inline-flex h-3 w-3 rounded-full;
  }
  
  .pulse-dot::before {
    content: '';
    @apply absolute inline-flex h-full w-full animate-ping rounded-full opacity-75;
  }
  
  .pulse-dot::after {
    content: '';
    @apply relative inline-flex h-3 w-3 rounded-full;
  }
  
  .pulse-dot.success::before,
  .pulse-dot.success::after {
    @apply bg-success-500;
  }
  
  .pulse-dot.warning::before,
  .pulse-dot.warning::after {
    @apply bg-warning-500;
  }
  
  .pulse-dot.danger::before,
  .pulse-dot.danger::after {
    @apply bg-danger-500;
  }
  
  /* 侧边栏样式 */
  .sidebar-nav {
    @apply space-y-1;
  }
  
  .sidebar-nav-item {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground;
  }
  
  .sidebar-nav-item.active {
    @apply bg-primary text-primary-foreground;
  }
  
  /* 图表容器 */
  .chart-container {
    @apply tech-card p-6 h-full;
  }
  
  /* 数据表格 */
  .data-table {
    @apply w-full border-collapse;
  }
  
  .data-table th {
    @apply border-b border-border px-4 py-3 text-left text-sm font-medium text-muted-foreground;
  }
  
  .data-table td {
    @apply border-b border-border px-4 py-3 text-sm;
  }
  
  .data-table tr:hover {
    @apply bg-muted/50;
  }
}

@layer utilities {
  /* 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted));
    border-radius: 2px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }
  
  /* Stagewise toolbar styles */
  #stagewise-toolbar-root {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    pointer-events: none;
  }
  
  #stagewise-toolbar-root > * {
    pointer-events: auto;
  }
} 