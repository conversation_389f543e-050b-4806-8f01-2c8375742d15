// 🔍 价格服务诊断脚本 - 在浏览器控制台中运行
// 复制此代码到浏览器开发者工具的控制台中执行

console.log('🚀 开始价格服务诊断...')

// 1. 检查localStorage状态
console.group('📋 1. localStorage状态检查')
const tradingStatus = localStorage.getItem('trading_status')
const selectedSymbol = localStorage.getItem('selected_symbol')
console.log('交易状态:', tradingStatus)
console.log('选择币种:', selectedSymbol)
console.log('是否满足连接条件:', tradingStatus === 'active' && selectedSymbol)
console.groupEnd()

// 2. 检查全局价格服务状态
console.group('🌐 2. 全局价格服务状态')
try {
  console.log('是否连接:', globalPriceService.isConnected())
  console.log('当前币种:', globalPriceService.getCurrentSymbol())
  console.log('当前价格:', globalPriceService.getCurrentPrice())
  console.log('价格状态:', globalPriceService.getCurrentState())
} catch (error) {
  console.error('❌ 无法访问globalPriceService:', error)
}
console.groupEnd()

// 3. 检查WebSocket连接
console.group('🔌 3. WebSocket连接检查')
try {
  const wsStatus = binanceWebSocket.getConnectionStatus()
  const activeSymbol = binanceWebSocket.getActiveSymbol()
  const allConnections = binanceWebSocket.getAllConnectionStatus()
  
  console.log('WebSocket连接状态:', wsStatus)
  console.log('活跃币种:', activeSymbol)
  console.log('所有连接状态:', allConnections)
} catch (error) {
  console.error('❌ 无法访问binanceWebSocket:', error)
}
console.groupEnd()

// 4. 提供修复建议
console.group('🔧 4. 修复建议')
if (tradingStatus !== 'active') {
  console.log('💡 修复方案 1: 激活交易状态')
  console.log('执行命令: localStorage.setItem("trading_status", "active")')
}

if (!selectedSymbol || selectedSymbol === 'null') {
  console.log('💡 修复方案 2: 设置默认币种')
  console.log('执行命令: localStorage.setItem("selected_symbol", "BTCUSDT")')
}

console.log('💡 修复方案 3: 强制重连价格服务')
console.log('执行命令: globalPriceService.reconnect()')

console.log('💡 修复方案 4: 完整重置和重连')
console.log(`执行命令: 
localStorage.setItem("trading_status", "active")
localStorage.setItem("selected_symbol", "BTCUSDT")
globalPriceService.reconnect()
location.reload()`)
console.groupEnd()

// 5. 自动修复选项
console.group('🚀 5. 一键修复')
const autoFix = () => {
  console.log('执行自动修复...')
  localStorage.setItem('trading_status', 'active')
  localStorage.setItem('selected_symbol', 'BTCUSDT')
  
  setTimeout(() => {
    try {
      globalPriceService.reconnect()
      console.log('✅ 价格服务重连完成')
    } catch (error) {
      console.error('❌ 重连失败:', error)
      console.log('📝 请刷新页面: location.reload()')
    }
  }, 1000)
}

console.log('💡 执行自动修复: autoFix()')
window.autoFix = autoFix
console.groupEnd()

console.log('🎯 诊断完成！请查看上述输出，或执行 autoFix() 进行一键修复') 