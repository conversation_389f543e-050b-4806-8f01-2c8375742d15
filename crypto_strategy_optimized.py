#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版量化交易策略回测系统 v3.0
目标：筛选回撤≤15%，月化收益≥10%的最优策略
优化手续费设置和策略逻辑，增加买入持有对比
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta


class TechnicalIndicators:
    """技术指标计算类"""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """简单移动平均线"""
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """指数移动平均线"""
        return data.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """相对强弱指标"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD指标"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """布林带"""
        sma = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """随机指标KD"""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        return k_percent, d_percent


class OptimizedBacktester:
    """优化回测引擎"""
    
    def __init__(self, initial_capital: float = 100000, 
                 commission_rate: float = 0.0005,  # 降低到0.05%
                 slippage_rate: float = 0.0002):   # 降低到0.02%
        """
        初始化回测引擎
        
        Args:
            initial_capital: 初始资金
            commission_rate: 手续费率 (0.05%)
            slippage_rate: 滑点率 (0.02%)
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        self.data = None
        self.results = {}
        
    def load_data(self, csv_path: str) -> pd.DataFrame:
        """加载K线数据"""
        print(f"📊 正在加载数据: {csv_path}")
        
        # 读取数据
        df = pd.read_csv(csv_path)
        
        # 数据预处理
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        # 计算技术指标
        df = self._calculate_indicators(df)
        
        self.data = df
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print(f"📅 时间范围: {df.index[0]} 至 {df.index[-1]}")
        
        return df
        
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # 移动平均线
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'ma_{period}'] = TechnicalIndicators.sma(close, period)
        
        # 指数移动平均线
        for period in [12, 26, 50]:
            df[f'ema_{period}'] = TechnicalIndicators.ema(close, period)
        
        # MACD
        df['macd'], df['macd_signal'], df['macd_hist'] = TechnicalIndicators.macd(close)
        
        # RSI
        df['rsi_14'] = TechnicalIndicators.rsi(close, 14)
        df['rsi_21'] = TechnicalIndicators.rsi(close, 21)
        
        # 布林带
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = TechnicalIndicators.bollinger_bands(close, 20)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (close - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # KDJ
        df['k'], df['d'] = TechnicalIndicators.stochastic(high, low, close)
        df['j'] = 3 * df['k'] - 2 * df['d']
        
        # 成交量指标
        df['volume_ma'] = TechnicalIndicators.sma(volume, 20)
        df['volume_ratio'] = volume / df['volume_ma']
        
        # 价格变化率
        df['price_change'] = close.pct_change()
        df['returns_1h'] = close.pct_change(4)  # 1小时收益率 (4个15分钟)
        df['returns_4h'] = close.pct_change(16)  # 4小时收益率
        df['returns_1d'] = close.pct_change(96)  # 1天收益率
        
        return df


class OptimizedStrategies:
    """优化交易策略集合"""
    
    @staticmethod
    def buy_and_hold(df: pd.DataFrame) -> pd.DataFrame:
        """买入并持有策略 - 作为基准"""
        signals = pd.DataFrame(index=df.index)
        signals['position'] = 1  # 始终持有
        signals['signal'] = 0
        signals['signal'].iloc[0] = 1  # 第一天买入
        return signals
    
    @staticmethod
    def enhanced_ma_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """增强版均线策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 多重均线确认
        short_ma = df['ema_12']
        medium_ma = df['ema_26']
        long_ma = df['ma_50']
        
        # 趋势确认
        uptrend = (short_ma > medium_ma) & (medium_ma > long_ma)
        downtrend = (short_ma < medium_ma) & (medium_ma < long_ma)
        
        # RSI过滤 - 避免极端区域
        rsi_neutral = (df['rsi_14'] > 35) & (df['rsi_14'] < 65)
        
        # 成交量确认
        volume_ok = df['volume_ratio'] > 0.8
        
        # 信号生成
        buy_signal = uptrend & rsi_neutral & volume_ok
        sell_signal = downtrend & volume_ok
        
        signals['position'] = 0
        signals.loc[buy_signal, 'position'] = 1
        signals.loc[sell_signal, 'position'] = -1
        
        # 前向填充
        signals['position'] = signals['position'].replace(0, np.nan).fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def trend_following_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """趋势跟踪策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 价格位置相对于均线
        price_above_ma20 = df['close'] > df['ma_20']
        price_above_ma50 = df['close'] > df['ma_50']
        
        # 均线斜率 (趋势强度)
        ma20_slope = df['ma_20'].diff(4) > 0  # 4期斜率向上
        ma50_slope = df['ma_50'].diff(8) > 0  # 8期斜率向上
        
        # 动量确认
        momentum_1h = df['returns_1h'] > 0
        momentum_4h = df['returns_4h'] > 0
        
        # 买入条件：多重确认
        buy_condition = (
            price_above_ma20 & 
            price_above_ma50 & 
            ma20_slope & 
            ma50_slope & 
            momentum_1h & 
            (df['rsi_14'] > 40) & 
            (df['rsi_14'] < 75)
        )
        
        # 卖出条件
        sell_condition = (
            ~price_above_ma20 | 
            ~ma20_slope | 
            (df['rsi_14'] < 30)
        )
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = 0
        
        # 前向填充
        signals['position'] = signals['position'].fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def mean_reversion_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """均值回归策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 布林带位置
        bb_oversold = df['close'] < df['bb_lower'] * 1.005  # 略微放宽条件
        bb_overbought = df['close'] > df['bb_upper'] * 0.995
        
        # RSI确认
        rsi_oversold = df['rsi_14'] < 35
        rsi_overbought = df['rsi_14'] > 65
        
        # 成交量异常 (可能是反转信号)
        volume_spike = df['volume_ratio'] > 1.5
        
        # 买入条件：超卖 + 成交量确认
        buy_condition = bb_oversold & rsi_oversold & volume_spike
        
        # 卖出条件：超买或回到中线
        sell_condition = (bb_overbought & rsi_overbought) | (df['close'] > df['bb_middle'])
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = 0
        
        # 前向填充
        signals['position'] = signals['position'].fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def breakout_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """突破策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 计算动态支撑阻力
        high_20 = df['high'].rolling(20).max()
        low_20 = df['low'].rolling(20).min()
        
        # 突破确认
        upward_breakout = df['close'] > high_20.shift(1) * 1.002  # 向上突破+0.2%
        volume_confirm = df['volume_ratio'] > 1.2
        
        # 趋势过滤
        in_uptrend = df['ma_20'] > df['ma_50']
        rsi_filter = (df['rsi_14'] > 45) & (df['rsi_14'] < 80)
        
        # 买入条件
        buy_condition = upward_breakout & volume_confirm & in_uptrend & rsi_filter
        
        # 卖出条件：跌破支撑或RSI过高
        sell_condition = (df['close'] < low_20.shift(1) * 0.998) | (df['rsi_14'] > 75)
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = 0
        
        # 前向填充
        signals['position'] = signals['position'].fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def adaptive_macd_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """自适应MACD策略"""
        signals = pd.DataFrame(index=df.index)
        
        # MACD信号
        macd_bullish = (df['macd'] > df['macd_signal']) & (df['macd_hist'] > 0)
        macd_bearish = (df['macd'] < df['macd_signal']) & (df['macd_hist'] < 0)
        
        # 趋势过滤
        trend_filter = df['ema_12'] > df['ema_26']
        
        # 价格动量
        momentum_ok = df['returns_1h'] > -0.02  # 1小时内跌幅不超过2%
        
        # RSI过滤
        rsi_ok = (df['rsi_14'] > 30) & (df['rsi_14'] < 70)
        
        # 买入条件
        buy_condition = macd_bullish & trend_filter & momentum_ok & rsi_ok
        
        # 卖出条件
        sell_condition = macd_bearish | ~trend_filter | (df['rsi_14'] > 75)
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = 0
        
        # 前向填充
        signals['position'] = signals['position'].fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def conservative_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """保守策略 - 追求稳定收益"""
        signals = pd.DataFrame(index=df.index)
        
        # 长期趋势确认
        long_trend = (df['ma_50'] > df['ma_100']) & (df['ma_100'] > df['ma_200'])
        
        # 短期回调买入机会
        pullback = (df['close'] < df['ma_20']) & (df['close'] > df['ma_50'])
        
        # RSI不能过低
        rsi_ok = df['rsi_14'] > 40
        
        # 成交量正常
        volume_normal = (df['volume_ratio'] > 0.5) & (df['volume_ratio'] < 2.0)
        
        # 买入条件：在长期上升趋势中的短期回调
        buy_condition = long_trend & pullback & rsi_ok & volume_normal
        
        # 卖出条件：趋势转弱或获利了结
        sell_condition = ~long_trend | (df['rsi_14'] > 70)
        
        signals['position'] = 0
        signals.loc[buy_condition, 'position'] = 1
        signals.loc[sell_condition, 'position'] = 0
        
        # 前向填充
        signals['position'] = signals['position'].fillna(method='ffill').fillna(0)
        signals['signal'] = signals['position'].diff()
        
        return signals


class PerformanceAnalyzer:
    """绩效分析器"""
    
    @staticmethod
    def calculate_returns(df: pd.DataFrame, signals: pd.DataFrame, 
                         initial_capital: float, commission_rate: float, 
                         slippage_rate: float) -> Dict:
        """计算策略收益"""
        
        # 计算持仓收益
        df_copy = df.copy()
        df_copy['position'] = signals['position'].fillna(0)
        df_copy['signal'] = signals['signal'].fillna(0)
        
        # 价格变化
        df_copy['price_change'] = df_copy['close'].pct_change()
        
        # 策略收益（考虑手续费和滑点）
        df_copy['strategy_return'] = df_copy['position'].shift(1) * df_copy['price_change']
        
        # 只对实际交易收取费用
        trade_mask = df_copy['signal'] != 0
        total_cost_rate = commission_rate + slippage_rate
        
        # 计算交易成本
        df_copy['trade_cost'] = 0
        df_copy.loc[trade_mask, 'trade_cost'] = total_cost_rate
        
        # 净收益 = 策略收益 - 交易成本
        df_copy['net_return'] = df_copy['strategy_return'] - df_copy['trade_cost']
        
        # 累计收益
        df_copy['cumulative_return'] = (1 + df_copy['net_return']).cumprod()
        df_copy['portfolio_value'] = initial_capital * df_copy['cumulative_return']
        
        # 计算绩效指标
        total_return = df_copy['cumulative_return'].iloc[-1] - 1
        
        # 年化收益率
        days = (df_copy.index[-1] - df_copy.index[0]).days
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        
        # 月化收益率
        monthly_return = (1 + total_return) ** (30 / days) - 1 if days > 0 else 0
        
        # 最大回撤
        rolling_max = df_copy['portfolio_value'].expanding().max()
        drawdown = (df_copy['portfolio_value'] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 夏普比率
        risk_free_rate = 0.02  # 假设无风险利率2%
        excess_return = df_copy['net_return'] - risk_free_rate / (365 * 24 * 4)  # 15分钟数据
        sharpe_ratio = excess_return.mean() / excess_return.std() * np.sqrt(365 * 24 * 4) if excess_return.std() > 0 else 0
        
        # 胜率
        winning_trades = (df_copy['net_return'] > 0).sum()
        total_trades = (df_copy['net_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 交易次数
        trade_count = (df_copy['signal'].abs() > 0).sum()
        
        # 最大连续亏损
        losses = df_copy['net_return'] < 0
        max_consecutive_losses = 0
        current_losses = 0
        for loss in losses:
            if loss:
                current_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, current_losses)
            else:
                current_losses = 0
        
        # 计算收益波动率
        returns_std = df_copy['net_return'].std()
        annual_volatility = returns_std * np.sqrt(365 * 24 * 4)
        
        # 总交易成本
        total_cost = df_copy['trade_cost'].sum()
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'win_rate': win_rate,
            'trade_count': trade_count,
            'max_consecutive_losses': max_consecutive_losses,
            'annual_volatility': annual_volatility,
            'total_cost': total_cost,
            'final_value': df_copy['portfolio_value'].iloc[-1],
            'portfolio_series': df_copy['portfolio_value'],
            'drawdown_series': drawdown
        }


def main():
    """主函数：执行策略回测和筛选"""
    
    print("🚀 启动优化版量化策略回测系统 v3.0")
    print("=" * 50)
    
    # 初始化回测引擎
    backester = OptimizedBacktester(
        initial_capital=100000,
        commission_rate=0.0005,  # 0.05% 手续费
        slippage_rate=0.0002     # 0.02% 滑点
    )
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = backester.load_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 策略列表
    strategies = {
        '买入持有(基准)': OptimizedStrategies.buy_and_hold,
        '增强均线策略': OptimizedStrategies.enhanced_ma_strategy,
        '趋势跟踪策略': OptimizedStrategies.trend_following_strategy,
        '均值回归策略': OptimizedStrategies.mean_reversion_strategy,
        '突破策略': OptimizedStrategies.breakout_strategy,
        '自适应MACD': OptimizedStrategies.adaptive_macd_strategy,
        '保守策略': OptimizedStrategies.conservative_strategy
    }
    
    # 回测所有策略
    results = {}
    print("\n📈 开始策略回测...")
    
    for strategy_name, strategy_func in strategies.items():
        print(f"\n🔍 测试策略: {strategy_name}")
        
        try:
            # 生成交易信号
            signals = strategy_func(df)
            
            # 计算绩效
            performance = PerformanceAnalyzer.calculate_returns(
                df, signals, backester.initial_capital,
                backester.commission_rate, backester.slippage_rate
            )
            
            results[strategy_name] = performance
            
            print(f"   ✅ 总收益: {performance['total_return']:.2%}")
            print(f"   📊 年化收益: {performance['annual_return']:.2%}")
            print(f"   📊 月化收益: {performance['monthly_return']:.2%}")
            print(f"   📉 最大回撤: {performance['max_drawdown']:.2%}")
            print(f"   🎯 夏普比率: {performance['sharpe_ratio']:.2f}")
            print(f"   🎲 胜率: {performance['win_rate']:.2%}")
            print(f"   🔄 交易次数: {performance['trade_count']}")
            print(f"   💰 交易成本: ${performance['total_cost']:.2f}")
            
        except Exception as e:
            print(f"   ❌ 策略 {strategy_name} 回测失败: {e}")
    
    # 筛选符合条件的策略
    print("\n" + "=" * 60)
    print("🎯 策略筛选结果 (回撤≤15%, 月化收益≥10%)")
    print("=" * 60)
    
    qualified_strategies = {}
    for name, perf in results.items():
        if perf['max_drawdown'] >= -0.15 and perf['monthly_return'] >= 0.10:
            qualified_strategies[name] = perf
    
    # 按夏普比率排序
    sorted_results = sorted(results.items(), key=lambda x: x[1]['sharpe_ratio'], reverse=True)
    
    # 创建结果表格
    print("\n📊 所有策略表现概览 (按夏普比率排序):")
    summary_data = []
    for name, perf in sorted_results:
        summary_data.append({
            '策略名称': name,
            '总收益': f"{perf['total_return']:.2%}",
            '年化收益': f"{perf['annual_return']:.2%}",
            '月化收益': f"{perf['monthly_return']:.2%}",
            '最大回撤': f"{perf['max_drawdown']:.2%}",
            '夏普比率': f"{perf['sharpe_ratio']:.2f}",
            '胜率': f"{perf['win_rate']:.2%}",
            '交易次数': perf['trade_count'],
            '交易成本': f"${perf['total_cost']:.0f}"
        })
    
    summary_df = pd.DataFrame(summary_data)
    print(summary_df.to_string(index=False))
    
    if qualified_strategies:
        print(f"\n✅ 找到 {len(qualified_strategies)} 个符合条件的策略:")
        
        # 按夏普比率排序找出最优策略
        best_strategy = max(qualified_strategies.items(), 
                          key=lambda x: x[1]['sharpe_ratio'])
        
        print(f"\n🏆 最优策略: {best_strategy[0]}")
        print(f"   💰 总收益: {best_strategy[1]['total_return']:.2%}")
        print(f"   📈 年化收益: {best_strategy[1]['annual_return']:.2%}")
        print(f"   📊 月化收益: {best_strategy[1]['monthly_return']:.2%}")
        print(f"   📉 最大回撤: {best_strategy[1]['max_drawdown']:.2%}")
        print(f"   ⚡ 夏普比率: {best_strategy[1]['sharpe_ratio']:.2f}")
        print(f"   🎯 胜率: {best_strategy[1]['win_rate']:.2%}")
        print(f"   🔄 交易次数: {best_strategy[1]['trade_count']}")
        print(f"   💎 最终资产: ${best_strategy[1]['final_value']:,.2f}")
        
    else:
        print("❌ 没有策略完全符合严格筛选条件")
        
        # 寻找最优策略 (综合评分)
        print(f"\n🏆 综合评分最高的策略:")
        best_name, best_perf = sorted_results[0]
        print(f"   策略名称: {best_name}")
        print(f"   💰 总收益: {best_perf['total_return']:.2%}")
        print(f"   📈 年化收益: {best_perf['annual_return']:.2%}")
        print(f"   📊 月化收益: {best_perf['monthly_return']:.2%}")
        print(f"   📉 最大回撤: {best_perf['max_drawdown']:.2%}")
        print(f"   ⚡ 夏普比率: {best_perf['sharpe_ratio']:.2f}")
        print(f"   🎯 胜率: {best_perf['win_rate']:.2%}")
        print(f"   💎 最终资产: ${best_perf['final_value']:,.2f}")
    
    # 与买入持有对比
    if '买入持有(基准)' in results:
        benchmark = results['买入持有(基准)']
        print(f"\n📈 基准对比 (买入持有):")
        print(f"   总收益: {benchmark['total_return']:.2%}")
        print(f"   年化收益: {benchmark['annual_return']:.2%}")
        print(f"   最大回撤: {benchmark['max_drawdown']:.2%}")
        print(f"   夏普比率: {benchmark['sharpe_ratio']:.2f}")
        
        # 寻找跑赢基准的策略
        outperforming = []
        for name, perf in results.items():
            if name != '买入持有(基准)' and perf['total_return'] > benchmark['total_return']:
                outperforming.append((name, perf))
        
        if outperforming:
            print(f"\n🎉 跑赢基准的策略 ({len(outperforming)}个):")
            for name, perf in sorted(outperforming, key=lambda x: x[1]['total_return'], reverse=True):
                print(f"   {name}: 总收益 {perf['total_return']:.2%} (超越 {perf['total_return'] - benchmark['total_return']:.2%})")
    
    # 数据统计信息
    print(f"\n📊 数据统计信息:")
    print(f"   📅 回测时间跨度: {df.index[0]} 至 {df.index[-1]}")
    print(f"   📈 数据点数量: {len(df)} 个15分钟K线")
    print(f"   🕒 总回测天数: {(df.index[-1] - df.index[0]).days} 天")
    print(f"   📊 BTC价格范围: ${df['close'].min():,.2f} - ${df['close'].max():,.2f}")
    print(f"   📈 期间涨幅: {(df['close'].iloc[-1] / df['close'].iloc[0] - 1):.2%}")
    
    print(f"\n🎊 回测完成！")
    
    return results, qualified_strategies if qualified_strategies else None


if __name__ == "__main__":
    main() 