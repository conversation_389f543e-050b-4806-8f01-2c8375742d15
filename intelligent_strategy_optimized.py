#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能策略优化版 - 改进回撤和收益
核心改进：更严格的风控，更精准的信号，更合理的仓位管理
作者：顶尖量化交易师
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Optional
import time
from dataclasses import dataclass
from enum import Enum


class MarketState(Enum):
    """市场状态"""
    SIDEWAYS = "震荡"
    UPTREND = "上涨趋势" 
    DOWNTREND = "下跌趋势"


@dataclass
class Trade:
    """交易记录"""
    timestamp: pd.Timestamp
    action: str  # 'buy', 'sell'
    price: float
    quantity: float
    strategy: str  # 'grid', 'trend'
    executed: bool = True


class ImprovedTradingSystem:
    """改进的交易系统"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.position = 0.0
        self.portfolio_value = initial_capital
        
        # 交易成本
        self.commission_rate = 0.0005  # 0.05%
        self.slippage_rate = 0.0002    # 0.02%
        
        # 优化的策略参数
        self.grid_spacing = 0.015      # 提高网格间距到1.5%
        self.trend_profit = 0.003      # 提高趋势止盈到0.3%
        self.trend_stop_loss = 0.008   # 添加趋势止损0.8%
        self.trend_threshold = 0.02    # 提高趋势判断阈值到2%
        self.max_position_ratio = 0.6  # 最大仓位比例60%
        
        # 风控参数
        self.max_drawdown_limit = 0.15  # 最大回撤限制15%
        self.daily_loss_limit = 0.02    # 日损失限制2%
        
        # 状态变量
        self.last_grid_price = None
        self.trend_entry_price = None
        self.trend_position = 0  # 1=多头, -1=空头, 0=无仓位
        self.max_portfolio_value = initial_capital
        self.daily_start_value = initial_capital
        self.last_trade_day = None
        
        # 记录
        self.trades = []
        self.portfolio_history = []
    
    def load_and_prepare_data(self, file_path: str) -> pd.DataFrame:
        """加载并预处理数据"""
        print(f"📊 加载数据: {file_path}")
        
        # 加载数据
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print("📊 预计算技术指标...")
        
        # 预计算技术指标
        df['ma_20'] = df['close'].rolling(window=20).mean()
        df['ma_60'] = df['close'].rolling(window=60).mean()
        df['ma_120'] = df['close'].rolling(window=120).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 价格变化率和波动率
        df['price_change_1h'] = df['close'].pct_change(4)   # 1小时变化率
        df['price_change_4h'] = df['close'].pct_change(16)  # 4小时变化率
        df['volatility'] = df['close'].rolling(window=20).std() / df['close'].rolling(window=20).mean()
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        print("✅ 技术指标计算完成")
        return df
    
    def detect_market_state(self, row: pd.Series) -> MarketState:
        """改进的市场状态检测"""
        price_change_4h = row.get('price_change_4h', 0)
        volatility = row.get('volatility', 0)
        ma_20 = row.get('ma_20', 0)
        ma_60 = row.get('ma_60', 0)
        ma_120 = row.get('ma_120', 0)
        macd = row.get('macd', 0)
        macd_signal = row.get('macd_signal', 0)
        
        # 更严格的趋势判断
        trend_signals = 0
        
        # 价格变化率信号
        if abs(price_change_4h) > self.trend_threshold:
            trend_signals += 1
        
        # 移动平均线信号
        if price_change_4h > 0 and ma_20 > ma_60 > ma_120:
            trend_signals += 1
        elif price_change_4h < 0 and ma_20 < ma_60 < ma_120:
            trend_signals += 1
        
        # MACD信号
        if price_change_4h > 0 and macd > macd_signal:
            trend_signals += 1
        elif price_change_4h < 0 and macd < macd_signal:
            trend_signals += 1
        
        # 波动率条件
        if volatility < 0.025:  # 波动率不能太高
            trend_signals += 1
        
        # 需要至少3个信号确认趋势
        if trend_signals >= 3:
            if price_change_4h > 0:
                return MarketState.UPTREND
            else:
                return MarketState.DOWNTREND
        
        return MarketState.SIDEWAYS
    
    def check_risk_controls(self, current_price: float, timestamp: pd.Timestamp) -> bool:
        """风险控制检查"""
        # 更新最大组合价值
        current_value = self.cash + self.position * current_price
        if current_value > self.max_portfolio_value:
            self.max_portfolio_value = current_value
        
        # 检查最大回撤
        drawdown = (current_value - self.max_portfolio_value) / self.max_portfolio_value
        if drawdown < -self.max_drawdown_limit:
            print(f"⛔ 触发最大回撤限制: {drawdown:.2%}")
            return False
        
        # 检查日损失限制
        current_day = timestamp.date()
        if self.last_trade_day != current_day:
            self.daily_start_value = current_value
            self.last_trade_day = current_day
        
        daily_loss = (current_value - self.daily_start_value) / self.daily_start_value
        if daily_loss < -self.daily_loss_limit:
            print(f"⛔ 触发日损失限制: {daily_loss:.2%}")
            return False
        
        return True
    
    def grid_strategy(self, current_price: float, timestamp: pd.Timestamp) -> Optional[Trade]:
        """改进的网格策略"""
        if self.last_grid_price is None:
            self.last_grid_price = current_price
            return None
        
        # 检查是否触发网格
        price_change = abs(current_price - self.last_grid_price) / self.last_grid_price
        
        if price_change >= self.grid_spacing:
            # 计算动态仓位大小
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            
            if current_price > self.last_grid_price:
                # 价格上涨，卖出（但不能卖空过多）
                if position_ratio > 0.1:  # 至少有10%仓位才卖出
                    action = 'sell'
                    quantity = min(0.03, self.position * 0.5)  # 最多卖出一半仓位
                else:
                    return None
            else:
                # 价格下跌，买入（但不能超过最大仓位）
                if position_ratio < self.max_position_ratio:
                    action = 'buy'
                    max_buy = (current_value * 0.05) / current_price  # 5%资金买入
                    quantity = min(max_buy, 0.03)
                else:
                    return None
            
            self.last_grid_price = current_price
            
            return Trade(
                timestamp=timestamp,
                action=action,
                price=current_price,
                quantity=quantity,
                strategy='grid'
            )
        
        return None
    
    def trend_strategy(self, current_price: float, state: MarketState, 
                      rsi: float, timestamp: pd.Timestamp) -> Optional[Trade]:
        """改进的趋势跟踪策略"""
        
        # 开仓逻辑
        if state == MarketState.UPTREND and self.trend_position <= 0:
            if 30 < rsi < 70:  # RSI在合理范围
                self.trend_position = 1
                self.trend_entry_price = current_price
                # 动态仓位大小
                current_value = self.cash + self.position * current_price
                position_value = current_value * 0.1  # 10%资金做趋势
                quantity = position_value / current_price
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='trend'
                )
        
        elif state == MarketState.DOWNTREND and self.trend_position >= 0:
            if 30 < rsi < 70:  # RSI在合理范围
                self.trend_position = -1
                self.trend_entry_price = current_price
                # 只有有仓位时才能做空
                if self.position > 0:
                    quantity = min(0.05, self.position * 0.3)  # 最多卖出30%仓位
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
        
        # 止盈止损逻辑
        elif self.trend_position != 0 and self.trend_entry_price:
            if self.trend_position > 0:  # 多头仓位
                profit_rate = (current_price - self.trend_entry_price) / self.trend_entry_price
                if profit_rate >= self.trend_profit:  # 止盈
                    self.trend_position = 0
                    self.trend_entry_price = None
                    quantity = min(0.05, self.position * 0.5)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
                elif profit_rate <= -self.trend_stop_loss:  # 止损
                    self.trend_position = 0
                    self.trend_entry_price = None
                    quantity = min(0.05, self.position * 0.5)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
            
            elif self.trend_position < 0:  # 空头仓位
                profit_rate = (self.trend_entry_price - current_price) / self.trend_entry_price
                if profit_rate >= self.trend_profit:  # 止盈
                    self.trend_position = 0
                    self.trend_entry_price = None
                    current_value = self.cash + self.position * current_price
                    buy_value = current_value * 0.05
                    quantity = buy_value / current_price
                    return Trade(
                        timestamp=timestamp,
                        action='buy',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
                elif profit_rate <= -self.trend_stop_loss:  # 止损
                    self.trend_position = 0
                    self.trend_entry_price = None
                    current_value = self.cash + self.position * current_price
                    buy_value = current_value * 0.05
                    quantity = buy_value / current_price
                    return Trade(
                        timestamp=timestamp,
                        action='buy',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
        
        return None
    
    def execute_trade(self, trade: Trade) -> bool:
        """执行交易"""
        # 计算交易成本
        trade_value = trade.price * trade.quantity
        cost = trade_value * (self.commission_rate + self.slippage_rate)
        
        if trade.action == 'buy':
            total_cost = trade_value + cost
            if self.cash >= total_cost:
                self.cash -= total_cost
                self.position += trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        elif trade.action == 'sell':
            if self.position >= trade.quantity:
                proceeds = trade_value - cost
                self.cash += proceeds
                self.position -= trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        self.trades.append(trade)
        return trade.executed
    
    def backtest(self, df: pd.DataFrame) -> Dict:
        """运行回测"""
        print("🚀 开始改进智能策略回测...")
        
        total_rows = len(df)
        start_time = time.time()
        
        # 初始化
        grid_trades = 0
        trend_trades = 0
        state_counts = {state: 0 for state in MarketState}
        risk_stops = 0
        
        # 主回测循环
        for i in range(120, total_rows):  # 从第120个数据点开始，确保指标充分
            current_row = df.iloc[i]
            current_price = current_row['close']
            current_time = df.index[i]
            
            # 显示进度
            if i % 20000 == 0:
                progress = i / total_rows * 100
                elapsed = time.time() - start_time
                eta = elapsed / (i - 119) * (total_rows - i) if i > 120 else 0
                print(f"⏳ 进度: {progress:.1f}% | 用时: {elapsed:.0f}s | 预计剩余: {eta:.0f}s")
            
            # 风险控制检查
            if not self.check_risk_controls(current_price, current_time):
                risk_stops += 1
                continue
            
            # 检测市场状态
            market_state = self.detect_market_state(current_row)
            state_counts[market_state] += 1
            
            # 生成交易信号
            trade = None
            
            if market_state == MarketState.SIDEWAYS:
                # 震荡行情 - 网格策略
                trade = self.grid_strategy(current_price, current_time)
                if trade and self.execute_trade(trade) and trade.executed:
                    grid_trades += 1
                    
            elif market_state in [MarketState.UPTREND, MarketState.DOWNTREND]:
                # 趋势行情 - 趋势跟踪
                rsi = current_row.get('rsi', 50)
                trade = self.trend_strategy(current_price, market_state, rsi, current_time)
                if trade and self.execute_trade(trade) and trade.executed:
                    trend_trades += 1
            
            # 更新组合价值
            self.portfolio_value = self.cash + self.position * current_price
            
            # 记录组合历史（降低频率）
            if i % 1000 == 0:
                self.portfolio_history.append({
                    'timestamp': current_time,
                    'value': self.portfolio_value,
                    'cash': self.cash,
                    'position': self.position,
                    'price': current_price,
                    'state': market_state.value
                })
        
        elapsed_time = time.time() - start_time
        print(f"\n✅ 回测完成！用时: {elapsed_time:.1f}秒")
        print(f"📊 网格交易: {grid_trades} | 趋势交易: {trend_trades} | 风控停止: {risk_stops}")
        
        return self._calculate_performance(df, state_counts)
    
    def _calculate_performance(self, df: pd.DataFrame, state_counts: Dict) -> Dict:
        """计算绩效指标"""
        
        # 基础收益指标
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        # 时间相关指标
        start_date = df.index[120]  # 从第120个数据点开始
        end_date = df.index[-1]
        days = (end_date - start_date).days
        
        if days > 0:
            annual_return = (self.portfolio_value / self.initial_capital) ** (365 / days) - 1
            monthly_return = (self.portfolio_value / self.initial_capital) ** (30 / days) - 1
        else:
            annual_return = 0
            monthly_return = 0
        
        # 计算最大回撤
        if self.portfolio_history:
            portfolio_df = pd.DataFrame(self.portfolio_history)
            values = portfolio_df['value']
            rolling_max = values.expanding().max()
            drawdown = (values - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
        else:
            max_drawdown = 0
        
        # 交易统计
        executed_trades = [t for t in self.trades if t.executed]
        total_trades = len(executed_trades)
        grid_trades = len([t for t in executed_trades if t.strategy == 'grid'])
        trend_trades = len([t for t in executed_trades if t.strategy == 'trend'])
        
        # 市场状态分布
        total_states = sum(state_counts.values())
        state_distribution = {
            state.value: count / total_states if total_states > 0 else 0 
            for state, count in state_counts.items()
        }
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'grid_trades': grid_trades,
            'trend_trades': trend_trades,
            'final_value': self.portfolio_value,
            'final_cash': self.cash,
            'final_position': self.position,
            'market_state_distribution': state_distribution
        }


def main():
    """主函数"""
    print("🧠 智能策略优化版 - 改进回撤和收益")
    print("=" * 70)
    print("💡 核心改进：更严格的风控，更精准的信号，更合理的仓位管理")
    print("🎯 目标：回撤≤15%，月化收益≥10%")
    print("=" * 70)
    
    # 初始化系统
    trading_system = ImprovedTradingSystem(initial_capital=100000)
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = trading_system.load_and_prepare_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 运行回测
    try:
        results = trading_system.backtest(df)
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return
    
    # 输出结果
    print("\n" + "=" * 70)
    print("📊 改进智能策略回测结果")
    print("=" * 70)
    
    print(f"\n💰 收益表现:")
    print(f"   总收益: {results['total_return']:.2%}")
    print(f"   年化收益: {results['annual_return']:.2%}")
    print(f"   月化收益: {results['monthly_return']:.2%}")
    print(f"   最终资产: ${results['final_value']:,.2f}")
    print(f"   最终现金: ${results['final_cash']:,.2f}")
    print(f"   最终仓位: {results['final_position']:.3f}")
    
    print(f"\n🛡️ 风险控制:")
    print(f"   最大回撤: {results['max_drawdown']:.2%}")
    
    print(f"\n📈 交易统计:")
    print(f"   总交易次数: {results['total_trades']}")
    if results['total_trades'] > 0:
        print(f"   网格交易: {results['grid_trades']} ({results['grid_trades']/results['total_trades']:.1%})")
        print(f"   趋势交易: {results['trend_trades']} ({results['trend_trades']/results['total_trades']:.1%})")
    
    print(f"\n🎯 市场状态分布:")
    for state, percentage in results['market_state_distribution'].items():
        print(f"   {state}: {percentage:.1%}")
    
    # 策略评估
    print(f"\n🔍 策略评估:")
    target_met = (results['max_drawdown'] >= -0.15 and results['monthly_return'] >= 0.10)
    
    if target_met:
        print(f"   ✅ 恭喜！策略达到目标条件")
        print(f"   🎯 回撤控制: {results['max_drawdown']:.2%} (目标: ≤15%)")
        print(f"   🎯 月化收益: {results['monthly_return']:.2%} (目标: ≥10%)")
    else:
        print(f"   🔧 策略仍需进一步优化")
        if results['max_drawdown'] < -0.15:
            print(f"   ⚠️ 回撤超限: {results['max_drawdown']:.2%} (目标: ≤15%)")
        if results['monthly_return'] < 0.10:
            print(f"   ⚠️ 收益不足: {results['monthly_return']:.2%} (目标: ≥10%)")
    
    # 与买入持有对比
    buy_hold_return = (df['close'].iloc[-1] - df['close'].iloc[120]) / df['close'].iloc[120]
    print(f"\n📊 策略对比:")
    print(f"   改进策略收益: {results['total_return']:.2%}")
    print(f"   买入持有收益: {buy_hold_return:.2%}")
    
    if results['total_return'] > buy_hold_return:
        print(f"   ✅ 改进策略跑赢买入持有 {(results['total_return'] - buy_hold_return):.2%}")
    else:
        print(f"   ❌ 改进策略跑输买入持有 {(buy_hold_return - results['total_return']):.2%}")
    
    print(f"\n💡 改进建议:")
    print(f"   - 在crypto市场中，技术分析策略很难持续跑赢买入持有")
    print(f"   - 考虑降低交易频率，专注高质量信号")
    print(f"   - 结合基本面分析和宏观因素")
    print(f"   - 或考虑DCA(定投)策略用于长期持有")
    
    print(f"\n🎊 改进智能策略回测完成！")
    
    return results


if __name__ == "__main__":
    results = main() 