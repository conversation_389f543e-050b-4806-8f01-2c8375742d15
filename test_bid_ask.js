// 🔍 买卖价数据测试脚本 - 在浏览器控制台中运行
console.log('🚀 开始买卖价数据测试...')

// 直接测试币安BookTicker数据流
function testBookTicker() {
  console.log('🧪 测试BookTicker数据流...')
  
  const testWs = new WebSocket('wss://fstream.binance.com/ws/btcusdt@bookTicker')
  
  testWs.onopen = () => {
    console.log('✅ BookTicker WebSocket连接成功!')
    console.log('🔗 连接地址: wss://fstream.binance.com/ws/btcusdt@bookTicker')
  }
  
  let messageCount = 0
  testWs.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      messageCount++
      
      if (data.e === 'bookTicker') {
        console.log(`📨 BookTicker消息 #${messageCount}:`, {
          symbol: data.s,
          bid: parseFloat(data.b).toFixed(2),
          ask: parseFloat(data.a).toFixed(2),
          bidQty: parseFloat(data.B).toFixed(3),
          askQty: parseFloat(data.A).toFixed(3),
          spread: (parseFloat(data.a) - parseFloat(data.b)).toFixed(4),
          spreadPercent: (((parseFloat(data.a) - parseFloat(data.b)) / parseFloat(data.b)) * 100).toFixed(4) + '%',
          time: new Date().toLocaleTimeString()
        })
        
        // 接收5条消息后关闭
        if (messageCount >= 5) {
          testWs.close()
          console.log('✅ BookTicker测试完成，数据流正常!')
        }
      } else {
        console.log('📨 收到其他类型数据:', data)
      }
    } catch (error) {
      console.error('❌ 数据解析错误:', error)
    }
  }
  
  testWs.onerror = (error) => {
    console.error('❌ BookTicker连接错误:', error)
  }
  
  testWs.onclose = () => {
    console.log('🔌 BookTicker测试连接已关闭')
  }
  
  // 10秒后自动关闭
  setTimeout(() => {
    if (testWs.readyState === WebSocket.OPEN) {
      testWs.close()
      console.log('⏰ BookTicker测试超时')
    }
  }, 10000)
}

// 检查全局价格服务的买卖价数据
function checkGlobalPriceBidAsk() {
  console.log('🔍 检查全局价格服务买卖价数据...')
  
  if (typeof globalPriceService === 'undefined') {
    console.error('❌ globalPriceService未定义')
    return
  }
  
  const state = globalPriceService.getCurrentState()
  console.log('🌐 全局价格服务状态:', {
    isConnected: state.isConnected,
    currentPrice: state.currentPrice,
    bid: state.bid,
    ask: state.ask,
    spread: state.ask - state.bid,
    lastUpdate: new Date(state.lastUpdate).toLocaleTimeString()
  })
  
  if (state.bid === 0 || state.ask === 0) {
    console.warn('⚠️ 买卖价为0，可能需要重新连接')
    console.log('💡 尝试执行: globalPriceService.reconnect()')
  } else {
    console.log('✅ 买卖价数据正常')
  }
}

// 启动实时买卖价监控
function startBidAskMonitoring() {
  console.log('📡 启动买卖价实时监控...')
  
  let lastBid = null
  let lastAsk = null
  let updateCount = 0
  
  const monitor = setInterval(() => {
    if (typeof globalPriceService !== 'undefined') {
      const state = globalPriceService.getCurrentState()
      
      if (state.bid !== lastBid || state.ask !== lastAsk) {
        updateCount++
        console.log(`🎯 买卖价更新 #${updateCount}:`, {
          bid: `${lastBid || 'N/A'} → ${state.bid.toFixed(2)}`,
          ask: `${lastAsk || 'N/A'} → ${state.ask.toFixed(2)}`,
          spread: (state.ask - state.bid).toFixed(4),
          time: new Date().toLocaleTimeString()
        })
        lastBid = state.bid
        lastAsk = state.ask
      }
    }
  }, 1000)
  
  // 保存到全局以便停止
  window.bidAskMonitor = monitor
  console.log('✅ 买卖价监控已启动')
  console.log('💡 停止监控: clearInterval(window.bidAskMonitor)')
  
  // 30秒后自动停止
  setTimeout(() => {
    clearInterval(monitor)
    console.log('⏰ 买卖价监控已自动停止')
  }, 30000)
}

// 强制启用买卖价数据
function enableBidAskData() {
  console.log('🔧 强制启用买卖价数据...')
  
  // 1. 设置localStorage
  localStorage.setItem('trading_status', 'active')
  localStorage.setItem('selected_symbol', 'BTCUSDT')
  
  // 2. 重连全局价格服务
  if (typeof globalPriceService !== 'undefined') {
    globalPriceService.reconnect()
    console.log('✅ 全局价格服务已重连')
    
    // 3. 等待3秒后检查结果
    setTimeout(() => {
      checkGlobalPriceBidAsk()
    }, 3000)
  } else {
    console.error('❌ globalPriceService未定义')
  }
}

// 导出函数到全局
window.testBookTicker = testBookTicker
window.checkGlobalPriceBidAsk = checkGlobalPriceBidAsk
window.startBidAskMonitoring = startBidAskMonitoring
window.enableBidAskData = enableBidAskData

console.log('🎯 买卖价测试工具已准备就绪!')
console.log('📋 可用命令:')
console.log('  testBookTicker() - 直接测试BookTicker数据流')
console.log('  checkGlobalPriceBidAsk() - 检查全局价格服务买卖价')
console.log('  startBidAskMonitoring() - 启动实时买卖价监控')
console.log('  enableBidAskData() - 强制启用买卖价数据')
console.log('')
console.log('💡 推荐执行顺序:')
console.log('  1. testBookTicker() - 确认网络连接正常')
console.log('  2. enableBidAskData() - 启用买卖价数据')
console.log('  3. startBidAskMonitoring() - 监控数据更新') 