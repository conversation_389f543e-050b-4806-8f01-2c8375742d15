
import { Bell, Wifi, Clock, User } from 'lucide-react'

export function Header() {
  const currentTime = new Date().toLocaleTimeString('zh-CN')
  
  return (
    <header className="h-16 border-b border-border bg-card/50 backdrop-blur-sm">
      <div className="flex items-center justify-between h-full px-6">
        {/* 左侧：页面标题 */}
        <div>
          <h2 className="text-lg font-semibold">实时监控仪表盘</h2>
          <p className="text-sm text-muted-foreground">
            最后更新: {currentTime}
          </p>
        </div>

        {/* 右侧：状态指示器 */}
        <div className="flex items-center gap-4">
          {/* API连接状态 */}
          <div className="flex items-center gap-2">
            <Wifi className="w-4 h-4 text-success-500" />
            <span className="text-sm text-success-500">API连接正常</span>
          </div>

          {/* 实时时钟 */}
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm">{currentTime}</span>
          </div>

          {/* 告警铃声 */}
          <button className="relative p-2 rounded-lg hover:bg-accent">
            <Bell className="w-4 h-4" />
            <span className="absolute -top-1 -right-1 w-2 h-2 bg-danger-500 rounded-full"></span>
          </button>

          {/* 用户头像 */}
          <button className="flex items-center gap-2 p-2 rounded-lg hover:bg-accent">
            <User className="w-4 h-4" />
            <span className="text-sm">操盘手</span>
          </button>
        </div>
      </div>
    </header>
  )
} 