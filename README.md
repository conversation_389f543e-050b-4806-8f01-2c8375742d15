# 🎯 量化交易监控系统前端

基于 **React 18 + Vite + Tailwind CSS + shadcn/UI** 的暗色科技风量化交易监控系统前端框架。

## 🚀 技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| **React** | 18.2+ | 前端框架 |
| **Vite** | 5.0+ | 构建工具 |
| **TypeScript** | 5.2+ | 类型支持 |
| **Tailwind CSS** | 3.4+ | 原子化CSS |
| **shadcn/ui** | Latest | 组件库 |
| **Lucide React** | Latest | 图标库 |
| **Plotly.js** | 2.27+ | 图表库 |
| **Zustand** | 4.4+ | 状态管理 |
| **React Router** | 6.21+ | 路由管理 |

## 📊 功能模块

### 🎛️ 核心功能
- **仪表盘** - 权益曲线、核心指标、VaR监控
- **实时监控** - API状态、告警系统、实时数据流
- **策略配置** - 参数管理、回测结果
- **风控管理** - 风险指标、止损设置
- **报告中心** - 数据导出、PDF生成
- **系统设置** - API配置、用户管理

### 🎨 设计特色
- **暗色科技风** - 专业交易终端视觉体验
- **响应式布局** - 支持桌面/平板/移动端
- **实时数据流** - WebSocket连接，毫秒级更新
- **高性能图表** - Plotly.js驱动的交互式图表
- **模块化架构** - 易于扩展和维护

## 🏗️ 项目结构

```
├── public/                 # 静态资源
├── src/
│   ├── components/         # 组件目录
│   │   ├── ui/            # shadcn/ui组件
│   │   ├── charts/        # 图表组件
│   │   ├── layout/        # 布局组件
│   │   └── common/        # 通用组件
│   ├── pages/             # 页面组件
│   │   ├── dashboard/     # 仪表盘页面
│   │   ├── monitor/       # 监控页面
│   │   ├── strategy/      # 策略页面
│   │   ├── reports/       # 报告页面
│   │   └── settings/      # 设置页面
│   ├── hooks/             # 自定义Hooks
│   ├── lib/               # 工具函数
│   ├── stores/            # Zustand状态管理
│   ├── types/             # TypeScript类型
│   ├── styles/            # 样式文件
│   └── test/              # 测试配置
├── Dockerfile             # Docker构建文件
├── nginx.conf             # Nginx配置
├── docker-compose.yml     # 容器编排
└── README.md             # 项目文档
```

## 🚀 快速开始

### 本地开发

```bash
# 1. 克隆项目
git clone <repository-url>
cd quant-trading-monitor

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 访问应用
open http://localhost:3000
```

### Docker部署

```bash
# 1. 构建镜像
docker build -t quant-trading-monitor .

# 2. 运行容器
docker run -p 3000:3000 quant-trading-monitor

# 3. 使用Docker Compose
docker-compose up -d
```

### 生产构建

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 🧪 测试

```bash
# 运行测试
npm run test

# 测试覆盖率
npm run test:coverage

# 测试UI界面
npm run test:ui
```

## 📁 核心组件说明

### 布局组件
- `AppLayout` - 主应用布局(左侧菜单+右侧内容)
- `Sidebar` - 左侧导航菜单
- `Header` - 顶部状态栏

### 业务组件
- `MetricCard` - 指标卡片组件
- `EquityChart` - 权益曲线图表
- `VaRChart` - VaR风险监控图
- `CostBreakdown` - 费用分解图

### UI组件 (shadcn/ui)
- `Card` - 卡片容器
- `Button` - 按钮组件
- `Table` - 数据表格
- `Badge` - 状态徽章

## 🎨 主题定制

### 颜色系统
```css
/* 主色调 */
--primary: #3b82f6          /* 科技蓝 */
--success: #22c55e          /* 成功绿 */
--warning: #f59e0b          /* 警告橙 */
--danger: #ef4444           /* 危险红 */

/* 背景色 */
--background: #0a0a0a       /* 主背景 */
--card: #1a1a1a            /* 卡片背景 */
```

### 自定义样式类
```css
.tech-card                  /* 科技风卡片 */
.metric-card               /* 指标卡片 */
.pulse-dot                 /* 脉冲动画点 */
.glow-border              /* 发光边框 */
```

## 🔌 API集成

### Mock数据结构
```typescript
// 仪表盘指标
interface DashboardMetrics {
  currentEquity: number
  totalReturn: number
  maxDrawdown: number
  sharpeRatio: number
  // ...
}

// 交易记录
interface TradeRecord {
  id: string
  symbol: string
  side: 'BUY' | 'SELL'
  quantity: number
  price: number
  // ...
}
```

### WebSocket连接
```typescript
// 实时数据订阅
const ws = new WebSocket('ws://localhost:8000/ws')
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  // 更新状态...
}
```

## 🐳 容器化部署

### Dockerfile特性
- **多阶段构建** - 优化镜像大小
- **Nginx服务** - 高性能静态文件服务
- **健康检查** - 容器健康状态监控
- **安全配置** - 非root用户运行

### Docker Compose
```yaml
version: '3.8'
services:
  frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
```

## 🔧 开发工具

### 代码质量
```bash
npm run lint              # ESLint检查
npm run format            # Prettier格式化
npm run type-check        # TypeScript类型检查
```

### 调试工具
- **React DevTools** - 组件调试
- **Vite DevTools** - 构建分析
- **Tailwind Inspector** - 样式调试

## 📈 性能优化

### 构建优化
- **代码分割** - 按需加载页面组件
- **Tree Shaking** - 移除未使用代码
- **资源压缩** - Gzip/Brotli压缩
- **缓存策略** - 长期缓存静态资源

### 运行时优化
- **虚拟滚动** - 大量数据表格优化
- **图表懒加载** - 按需加载图表组件
- **状态管理** - Zustand轻量级状态管理

## 🔒 安全配置

### 安全头设置
```nginx
add_header X-Frame-Options "SAMEORIGIN";
add_header X-XSS-Protection "1; mode=block";
add_header X-Content-Type-Options "nosniff";
add_header Content-Security-Policy "default-src 'self'";
```

### API安全
- **CORS配置** - 跨域请求限制
- **API密钥管理** - 环境变量存储
- **请求限制** - 防止API滥用

## 🤝 贡献指南

1. **Fork项目** - 创建个人分支
2. **功能开发** - 创建功能分支
3. **编写测试** - 确保测试覆盖率
4. **提交PR** - 描述变更内容

### 代码规范
- **TypeScript** - 严格类型检查
- **ESLint** - 代码风格检查
- **Prettier** - 自动格式化
- **Conventional Commits** - 提交信息规范

## 📞 支持与反馈

- **Issues** - [GitHub Issues](https://github.com/your-repo/issues)
- **文档** - [在线文档](https://your-docs-site.com)
- **Discord** - [社区讨论](https://discord.gg/your-server)

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

---

**🎯 专为量化交易打造的专业监控界面** 