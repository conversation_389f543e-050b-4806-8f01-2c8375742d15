#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能策略增强版 - 提高月化收益
目标：月化收益6%+，年化收益100%+
核心改进：更激进参数，多策略组合，动态仓位管理
作者：顶尖量化交易师
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Optional
import time
from dataclasses import dataclass
from enum import Enum


class MarketState(Enum):
    """市场状态"""
    SIDEWAYS = "震荡"
    UPTREND = "上涨趋势" 
    DOWNTREND = "下跌趋势"
    VOLATILE = "高波动"


@dataclass
class Trade:
    """交易记录"""
    timestamp: pd.Timestamp
    action: str  # 'buy', 'sell'
    price: float
    quantity: float
    strategy: str  # 'grid', 'trend', 'momentum', 'volatility'
    executed: bool = True


class EnhancedTradingSystem:
    """增强版交易系统"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.position = 0.0
        self.portfolio_value = initial_capital
        
        # 交易成本
        self.commission_rate = 0.0005  # 0.05%
        self.slippage_rate = 0.0002    # 0.02%
        
        # 增强的策略参数 - 更激进
        self.grid_spacing = 0.008      # 降低网格间距到0.8%
        self.trend_profit_fast = 0.004 # 快速止盈0.4%
        self.trend_profit_hold = 0.015 # 持有止盈1.5%
        self.trend_stop_loss = 0.006   # 趋势止损0.6%
        self.trend_threshold = 0.015   # 趋势判断阈值1.5%
        self.max_position_ratio = 0.8  # 提高最大仓位到80%
        
        # 新增策略参数
        self.momentum_threshold = 0.01  # 动量策略阈值1%
        self.volatility_threshold = 0.03 # 高波动阈值3%
        self.scalp_profit = 0.002      # 超短线止盈0.2%
        
        # 风控参数
        self.max_drawdown_limit = 0.15
        self.daily_loss_limit = 0.025   # 提高日损失限制到2.5%
        
        # 状态变量
        self.last_grid_price = None
        self.trend_entry_price = None
        self.trend_position = 0
        self.momentum_position = 0
        self.max_portfolio_value = initial_capital
        self.daily_start_value = initial_capital
        self.last_trade_day = None
        
        # 记录
        self.trades = []
        self.portfolio_history = []
    
    def load_and_prepare_data(self, file_path: str) -> pd.DataFrame:
        """加载并预处理数据"""
        print(f"📊 加载数据: {file_path}")
        
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print("📊 预计算增强技术指标...")
        
        # 基础技术指标
        df['ma_5'] = df['close'].rolling(window=5).mean()
        df['ma_20'] = df['close'].rolling(window=20).mean()
        df['ma_60'] = df['close'].rolling(window=60).mean()
        
        # RSI多周期
        for period in [7, 14, 21]:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # 价格变化率多周期
        df['price_change_15m'] = df['close'].pct_change(1)   # 15分钟变化
        df['price_change_1h'] = df['close'].pct_change(4)    # 1小时变化
        df['price_change_4h'] = df['close'].pct_change(16)   # 4小时变化
        
        # 波动率指标
        df['volatility_5'] = df['close'].rolling(window=5).std() / df['close'].rolling(window=5).mean()
        df['volatility_20'] = df['close'].rolling(window=20).std() / df['close'].rolling(window=20).mean()
        
        # 动量指标
        df['momentum_1h'] = df['close'] / df['close'].shift(4) - 1
        df['momentum_4h'] = df['close'] / df['close'].shift(16) - 1
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # 布林带
        df['bb_mid'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_mid'] + 2 * bb_std
        df['bb_lower'] = df['bb_mid'] - 2 * bb_std
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        print("✅ 增强技术指标计算完成")
        return df
    
    def detect_market_state(self, row: pd.Series) -> MarketState:
        """增强的市场状态检测"""
        price_change_1h = row.get('price_change_1h', 0)
        price_change_4h = row.get('price_change_4h', 0)
        volatility_5 = row.get('volatility_5', 0)
        volatility_20 = row.get('volatility_20', 0)
        ma_5 = row.get('ma_5', 0)
        ma_20 = row.get('ma_20', 0)
        ma_60 = row.get('ma_60', 0)
        macd_hist = row.get('macd_hist', 0)
        
        # 检查高波动环境
        if volatility_5 > self.volatility_threshold:
            return MarketState.VOLATILE
        
        # 趋势信号计数
        trend_signals = 0
        trend_direction = 0
        
        # 价格变化信号
        if abs(price_change_4h) > self.trend_threshold:
            trend_signals += 1
            trend_direction += 1 if price_change_4h > 0 else -1
        
        if abs(price_change_1h) > 0.005:  # 1小时变化超过0.5%
            trend_signals += 1
            trend_direction += 1 if price_change_1h > 0 else -1
        
        # 移动平均线信号
        if ma_5 > ma_20 > ma_60:
            trend_signals += 1
            trend_direction += 1
        elif ma_5 < ma_20 < ma_60:
            trend_signals += 1
            trend_direction -= 1
        
        # MACD信号
        if abs(macd_hist) > 0.001:
            trend_signals += 1
            trend_direction += 1 if macd_hist > 0 else -1
        
        # 需要至少2个信号确认趋势，降低门槛
        if trend_signals >= 2:
            if trend_direction > 0:
                return MarketState.UPTREND
            elif trend_direction < 0:
                return MarketState.DOWNTREND
        
        return MarketState.SIDEWAYS
    
    def grid_strategy(self, current_price: float, timestamp: pd.Timestamp) -> Optional[Trade]:
        """增强网格策略 - 更频繁交易"""
        if self.last_grid_price is None:
            self.last_grid_price = current_price
            return None
        
        price_change = abs(current_price - self.last_grid_price) / self.last_grid_price
        
        if price_change >= self.grid_spacing:
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            
            if current_price > self.last_grid_price:
                # 价格上涨，卖出
                if position_ratio > 0.05:  # 至少5%仓位
                    action = 'sell'
                    quantity = min(0.05, self.position * 0.6)  # 更积极卖出
                else:
                    return None
            else:
                # 价格下跌，买入
                if position_ratio < self.max_position_ratio:
                    action = 'buy'
                    max_buy = (current_value * 0.08) / current_price  # 8%资金买入
                    quantity = min(max_buy, 0.05)
                else:
                    return None
            
            self.last_grid_price = current_price
            
            return Trade(
                timestamp=timestamp,
                action=action,
                price=current_price,
                quantity=quantity,
                strategy='grid'
            )
        
        return None
    
    def trend_strategy(self, current_price: float, state: MarketState, 
                      row: pd.Series, timestamp: pd.Timestamp) -> Optional[Trade]:
        """增强趋势策略 - 双档止盈"""
        rsi_14 = row.get('rsi_14', 50)
        momentum_1h = row.get('momentum_1h', 0)
        
        # 开仓逻辑 - 更激进
        if state == MarketState.UPTREND and self.trend_position <= 0:
            if 25 < rsi_14 < 75 and momentum_1h > -0.01:  # 放宽条件
                self.trend_position = 1
                self.trend_entry_price = current_price
                current_value = self.cash + self.position * current_price
                position_value = current_value * 0.15  # 15%资金做趋势
                quantity = position_value / current_price
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='trend'
                )
        
        elif state == MarketState.DOWNTREND and self.trend_position >= 0:
            if 25 < rsi_14 < 75 and momentum_1h < 0.01:
                self.trend_position = -1
                self.trend_entry_price = current_price
                if self.position > 0:
                    quantity = min(0.08, self.position * 0.4)  # 更积极做空
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
        
        # 止盈止损逻辑 - 双档止盈
        elif self.trend_position != 0 and self.trend_entry_price:
            if self.trend_position > 0:  # 多头仓位
                profit_rate = (current_price - self.trend_entry_price) / self.trend_entry_price
                if profit_rate >= self.trend_profit_fast:  # 快速止盈
                    # 只平一部分仓位，让利润跑
                    quantity = min(0.04, self.position * 0.3)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
                elif profit_rate >= self.trend_profit_hold:  # 完全止盈
                    self.trend_position = 0
                    self.trend_entry_price = None
                    quantity = min(0.08, self.position * 0.5)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
                elif profit_rate <= -self.trend_stop_loss:  # 止损
                    self.trend_position = 0
                    self.trend_entry_price = None
                    quantity = min(0.06, self.position * 0.4)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
        
        return None
    
    def momentum_strategy(self, current_price: float, row: pd.Series, 
                         timestamp: pd.Timestamp) -> Optional[Trade]:
        """新增动量策略"""
        momentum_1h = row.get('momentum_1h', 0)
        rsi_7 = row.get('rsi_7', 50)
        volatility_5 = row.get('volatility_5', 0)
        
        # 动量突破信号
        if abs(momentum_1h) > self.momentum_threshold and volatility_5 < 0.02:
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            
            if momentum_1h > 0 and position_ratio < 0.7 and rsi_7 < 70:
                # 向上动量，买入
                buy_value = current_value * 0.05
                quantity = buy_value / current_price
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='momentum'
                )
            elif momentum_1h < 0 and position_ratio > 0.1 and rsi_7 > 30:
                # 向下动量，卖出
                quantity = min(0.03, self.position * 0.3)
                return Trade(
                    timestamp=timestamp,
                    action='sell',
                    price=current_price,
                    quantity=quantity,
                    strategy='momentum'
                )
        
        return None
    
    def volatility_strategy(self, current_price: float, row: pd.Series, 
                           timestamp: pd.Timestamp) -> Optional[Trade]:
        """新增波动率策略"""
        bb_position = row.get('bb_position', 0.5)
        volatility_20 = row.get('volatility_20', 0)
        
        # 布林带反转信号
        if volatility_20 > 0.01:  # 有一定波动率
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            
            if bb_position < 0.1 and position_ratio < 0.6:  # 接近下轨，买入
                buy_value = current_value * 0.04
                quantity = buy_value / current_price
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='volatility'
                )
            elif bb_position > 0.9 and position_ratio > 0.1:  # 接近上轨，卖出
                quantity = min(0.025, self.position * 0.25)
                return Trade(
                    timestamp=timestamp,
                    action='sell',
                    price=current_price,
                    quantity=quantity,
                    strategy='volatility'
                )
        
        return None
    
    def execute_trade(self, trade: Trade) -> bool:
        """执行交易"""
        trade_value = trade.price * trade.quantity
        cost = trade_value * (self.commission_rate + self.slippage_rate)
        
        if trade.action == 'buy':
            total_cost = trade_value + cost
            if self.cash >= total_cost:
                self.cash -= total_cost
                self.position += trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        elif trade.action == 'sell':
            if self.position >= trade.quantity:
                proceeds = trade_value - cost
                self.cash += proceeds
                self.position -= trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        self.trades.append(trade)
        return trade.executed
    
    def backtest(self, df: pd.DataFrame) -> Dict:
        """运行增强回测"""
        print("🚀 开始增强智能策略回测...")
        
        total_rows = len(df)
        start_time = time.time()
        
        # 初始化计数器
        strategy_trades = {
            'grid': 0, 'trend': 0, 'momentum': 0, 'volatility': 0
        }
        state_counts = {state: 0 for state in MarketState}
        
        # 主回测循环 - 从更早开始
        for i in range(60, total_rows):  # 从第60行开始，使用更多数据
            current_row = df.iloc[i]
            current_price = current_row['close']
            current_time = df.index[i]
            
            # 显示进度
            if i % 15000 == 0:
                progress = i / total_rows * 100
                elapsed = time.time() - start_time
                eta = elapsed / (i - 59) * (total_rows - i) if i > 60 else 0
                print(f"⏳ 进度: {progress:.1f}% | 用时: {elapsed:.0f}s | 预计剩余: {eta:.0f}s")
            
            # 检测市场状态
            market_state = self.detect_market_state(current_row)
            state_counts[market_state] += 1
            
            # 多策略并行运行
            trades = []
            
            # 策略1: 网格策略
            if market_state == MarketState.SIDEWAYS:
                grid_trade = self.grid_strategy(current_price, current_time)
                if grid_trade:
                    trades.append(grid_trade)
            
            # 策略2: 趋势策略
            if market_state in [MarketState.UPTREND, MarketState.DOWNTREND]:
                trend_trade = self.trend_strategy(current_price, market_state, current_row, current_time)
                if trend_trade:
                    trades.append(trend_trade)
            
            # 策略3: 动量策略
            momentum_trade = self.momentum_strategy(current_price, current_row, current_time)
            if momentum_trade:
                trades.append(momentum_trade)
            
            # 策略4: 波动率策略
            if market_state != MarketState.VOLATILE:
                volatility_trade = self.volatility_strategy(current_price, current_row, current_time)
                if volatility_trade:
                    trades.append(volatility_trade)
            
            # 执行所有交易
            for trade in trades:
                if self.execute_trade(trade) and trade.executed:
                    strategy_trades[trade.strategy] += 1
            
            # 更新组合价值
            self.portfolio_value = self.cash + self.position * current_price
            
            # 记录组合历史
            if i % 800 == 0:
                self.portfolio_history.append({
                    'timestamp': current_time,
                    'value': self.portfolio_value,
                    'cash': self.cash,
                    'position': self.position,
                    'price': current_price,
                    'state': market_state.value
                })
        
        elapsed_time = time.time() - start_time
        print(f"\n✅ 增强回测完成！用时: {elapsed_time:.1f}秒")
        print(f"📊 策略交易统计: {strategy_trades}")
        
        return self._calculate_performance(df, state_counts, strategy_trades)
    
    def _calculate_performance(self, df: pd.DataFrame, state_counts: Dict, strategy_trades: Dict) -> Dict:
        """计算绩效指标"""
        
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        start_date = df.index[60]
        end_date = df.index[-1]
        days = (end_date - start_date).days
        
        if days > 0:
            annual_return = (self.portfolio_value / self.initial_capital) ** (365 / days) - 1
            monthly_return = (self.portfolio_value / self.initial_capital) ** (30 / days) - 1
        else:
            annual_return = 0
            monthly_return = 0
        
        # 计算最大回撤
        if self.portfolio_history:
            portfolio_df = pd.DataFrame(self.portfolio_history)
            values = portfolio_df['value']
            rolling_max = values.expanding().max()
            drawdown = (values - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
        else:
            max_drawdown = 0
        
        # 交易统计
        executed_trades = [t for t in self.trades if t.executed]
        total_trades = len(executed_trades)
        
        # 市场状态分布
        total_states = sum(state_counts.values())
        state_distribution = {
            state.value: count / total_states if total_states > 0 else 0 
            for state, count in state_counts.items()
        }
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'strategy_trades': strategy_trades,
            'final_value': self.portfolio_value,
            'final_cash': self.cash,
            'final_position': self.position,
            'market_state_distribution': state_distribution
        }


def main():
    """主函数"""
    print("🚀 智能策略增强版 - 目标月化收益6%+")
    print("=" * 70)
    print("💡 增强改进：更激进参数 + 多策略组合 + 动态仓位管理")
    print("🎯 目标：月化收益≥6%，年化收益≥100%，回撤≤15%")
    print("=" * 70)
    
    # 初始化增强系统
    trading_system = EnhancedTradingSystem(initial_capital=100000)
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = trading_system.load_and_prepare_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 运行增强回测
    try:
        results = trading_system.backtest(df)
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return
    
    # 输出结果
    print("\n" + "=" * 70)
    print("📊 增强智能策略回测结果")
    print("=" * 70)
    
    print(f"\n💰 收益表现:")
    print(f"   总收益: {results['total_return']:.2%}")
    print(f"   年化收益: {results['annual_return']:.2%}")
    print(f"   月化收益: {results['monthly_return']:.2%}")
    print(f"   最终资产: ${results['final_value']:,.2f}")
    
    print(f"\n🛡️ 风险控制:")
    print(f"   最大回撤: {results['max_drawdown']:.2%}")
    
    print(f"\n📈 多策略交易统计:")
    print(f"   总交易次数: {results['total_trades']}")
    for strategy, count in results['strategy_trades'].items():
        if results['total_trades'] > 0:
            print(f"   {strategy}策略: {count} ({count/results['total_trades']:.1%})")
    
    print(f"\n🎯 市场状态分布:")
    for state, percentage in results['market_state_distribution'].items():
        print(f"   {state}: {percentage:.1%}")
    
    # 目标达成评估
    print(f"\n🔍 目标达成评估:")
    monthly_target = results['monthly_return'] >= 0.06
    drawdown_ok = results['max_drawdown'] >= -0.15
    
    if monthly_target and drawdown_ok:
        print(f"   🎉 完美！达成所有目标！")
        print(f"   ✅ 月化收益: {results['monthly_return']:.2%} ≥ 6.0%")
        print(f"   ✅ 回撤控制: {results['max_drawdown']:.2%} ≤ 15%")
    elif monthly_target:
        print(f"   🎯 月化收益达标: {results['monthly_return']:.2%} ≥ 6.0%")
        print(f"   ⚠️ 回撤超限: {results['max_drawdown']:.2%}")
    elif drawdown_ok:
        print(f"   ✅ 回撤良好: {results['max_drawdown']:.2%} ≤ 15%")
        print(f"   ⚠️ 收益待提升: {results['monthly_return']:.2%} < 6.0%")
    else:
        print(f"   🔧 需要进一步优化")
        print(f"   ⚠️ 月化收益: {results['monthly_return']:.2%} < 6.0%")
        print(f"   ⚠️ 最大回撤: {results['max_drawdown']:.2%}")
    
    print(f"\n🚀 增强策略回测完成！")
    
    return results


if __name__ == "__main__":
    results = main() 