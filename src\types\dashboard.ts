// 仪表盘指标数据类型
export interface DashboardMetrics {
  currentEquity: number
  initialEquity: number
  totalReturn: number
  dailyReturn: number
  maxDrawdown: number
  totalTrades: number
  totalCosts: number
  winRate: number
  sharpeRatio: number
  var95: number
}

// 权益曲线数据点
export interface EquityDataPoint {
  timestamp: Date
  equity: number
  benchmark: number
  drawdown: number
}

// 交易记录
export interface TradeRecord {
  id: string
  timestamp: Date
  symbol: string
  side: 'BUY' | 'SELL'
  quantity: number
  price: number
  commission: number
  slippage: number
  funding: number
  pnl: number
  status: 'FILLED' | 'PARTIAL' | 'CANCELLED'
}

// VaR数据
export interface VaRData {
  timestamp: Date
  var95: number
  var99: number
  confidence: number
  riskLevel: 'low' | 'medium' | 'high' | 'extreme'
}

// 成本分解数据
export interface CostBreakdown {
  period: string
  commission: number
  slippage: number
  funding: number
  total: number
}

// 策略状态
export interface StrategyStatus {
  isRunning: boolean
  currentPosition: number
  leverage: number
  unrealizedPnl: number
  lastSignalTime: Date
  nextRebalanceTime: Date
}

// 图表数据类型
export interface ChartDataPoint {
  x: Date | string | number
  y: number
  [key: string]: any
}

export interface ChartSeries {
  name: string
  data: ChartDataPoint[]
  color?: string
  type?: 'line' | 'area' | 'bar' | 'scatter'
}

// API响应类型
export interface DashboardResponse {
  success: boolean
  data: {
    metrics: DashboardMetrics
    equityData: EquityDataPoint[]
    recentTrades: TradeRecord[]
    varData: VaRData[]
    costBreakdown: CostBreakdown[]
    strategyStatus: StrategyStatus
  }
  timestamp: Date
} 