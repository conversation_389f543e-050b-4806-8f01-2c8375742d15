// 价格数据统一服务 - 解决数据源一致性问题
// 统一管理来自不同数据源的价格数据，确保策略执行时数据一致性

import { binanceKlineService, KlineData } from './binanceKlineService'
import { binanceWebSocket, TickerData } from './binanceWebSocket'

export interface UnifiedPriceData {
  // 核心价格数据（来源：K线）
  currentPrice: number        // 最新收盘价（主要用于策略计算）
  open: number               // 开盘价
  high: number               // 最高价
  low: number                // 最低价
  
  // 市场深度数据（来源：Ticker）
  bid: number                // 买一价
  ask: number                // 卖一价
  spread: number             // 买卖价差
  
  // 统计数据（来源：Ticker）
  volume24h: number          // 24小时成交量
  change24h: number          // 24小时涨跌幅
  high24h: number            // 24小时最高价
  low24h: number             // 24小时最低价
  
  // 数据源信息
  klineUpdateTime: number    // K线数据更新时间
  tickerUpdateTime: number   // Ticker数据更新时间
  isKlineConnected: boolean  // K线数据连接状态
  isTickerConnected: boolean // Ticker数据连接状态
  dataQuality: 'HIGH' | 'MEDIUM' | 'LOW' // 数据质量评估
}

export interface PriceDataQuality {
  klineLatency: number       // K线数据延迟（毫秒）
  tickerLatency: number      // Ticker数据延迟（毫秒）
  priceConsistency: number   // 价格一致性（0-1）
  dataFreshness: number      // 数据新鲜度（0-1）
  overallScore: number       // 整体质量评分（0-100）
}

class PriceDataUnifier {
  private currentData: UnifiedPriceData = {
    currentPrice: 0,
    open: 0,
    high: 0,
    low: 0,
    bid: 0,
    ask: 0,
    spread: 0,
    volume24h: 0,
    change24h: 0,
    high24h: 0,
    low24h: 0,
    klineUpdateTime: 0,
    tickerUpdateTime: 0,
    isKlineConnected: false,
    isTickerConnected: false,
    dataQuality: 'LOW'
  }

  private subscribers: ((data: UnifiedPriceData) => void)[] = []
  private currentSymbol: string | null = null
  private isMonitoring: boolean = false

  // 数据缓存
  private lastKlineData: KlineData | null = null
  private lastTickerData: TickerData | null = null

  // 质量监控
  private qualityCheckInterval: NodeJS.Timeout | null = null
  private priceHistoryForConsistency: number[] = []

  constructor() {
    console.log('🔧 价格数据统一服务初始化')
  }

  // 🎯 启动统一价格监控
  async startUnifiedMonitoring(symbol: string): Promise<boolean> {
    try {
      console.log(`🚀 启动统一价格监控: ${symbol}`)
      
      if (this.isMonitoring && this.currentSymbol === symbol) {
        console.log(`✅ 已在监控 ${symbol}，跳过重复启动`)
        return true
      }

      // 停止之前的监控
      this.stopUnifiedMonitoring()
      
      this.currentSymbol = symbol
      this.isMonitoring = true

      // 1. 初始化K线数据源
      console.log('📊 初始化K线数据源...')
      await this.initializeKlineDataSource(symbol)

      // 2. 初始化Ticker数据源
      console.log('📈 初始化Ticker数据源...')
      await this.initializeTickerDataSource(symbol)

      // 3. 启动数据质量监控
      console.log('🔍 启动数据质量监控...')
      this.startQualityMonitoring()

      console.log(`✅ 统一价格监控启动成功: ${symbol}`)
      return true

    } catch (error) {
      console.error('❌ 统一价格监控启动失败:', error)
      this.stopUnifiedMonitoring()
      return false
    }
  }

  // 🛑 停止统一价格监控
  stopUnifiedMonitoring(): void {
    if (!this.isMonitoring) {
      return
    }

    console.log('🛑 停止统一价格监控')
    
    this.isMonitoring = false
    this.currentSymbol = null
    
    // 停止质量监控
    if (this.qualityCheckInterval) {
      clearInterval(this.qualityCheckInterval)
      this.qualityCheckInterval = null
    }

    // 重置连接状态
    this.currentData.isKlineConnected = false
    this.currentData.isTickerConnected = false
    this.currentData.dataQuality = 'LOW'
    
    this.notifySubscribers()
  }

  // 📊 初始化K线数据源
  private async initializeKlineDataSource(symbol: string): Promise<void> {
    try {
      // 获取历史K线数据
      const historicalKlines = await binanceKlineService.fetchHistoricalKlines(symbol, '1m', 10)
      
      if (historicalKlines.length > 0) {
        const latestKline = historicalKlines[historicalKlines.length - 1]
        this.lastKlineData = latestKline
        
        // 更新K线相关数据
        this.currentData.currentPrice = latestKline.close
        this.currentData.open = latestKline.open
        this.currentData.high = latestKline.high
        this.currentData.low = latestKline.low
        this.currentData.klineUpdateTime = Date.now()
        this.currentData.isKlineConnected = true
        
        console.log(`📊 K线数据初始化成功: 当前价格 ${latestKline.close}`)
      }

      // 订阅实时K线更新
      binanceKlineService.subscribeKline(symbol, '1m', (klineData: KlineData) => {
        this.handleKlineUpdate(klineData)
      })

    } catch (error) {
      console.error('K线数据源初始化失败:', error)
      this.currentData.isKlineConnected = false
      throw error
    }
  }

  // 📈 初始化Ticker数据源
  private async initializeTickerDataSource(symbol: string): Promise<void> {
    try {
      // 启动WebSocket连接
      binanceWebSocket.startMonitoring(symbol)
      
      // 订阅Ticker数据
      binanceWebSocket.subscribeTicker(symbol, (tickerData: TickerData) => {
        this.handleTickerUpdate(tickerData)
      })

      // 等待首次Ticker数据
      let waitTime = 0
      const maxWaitTime = 5000
      
      while (!this.currentData.isTickerConnected && waitTime < maxWaitTime) {
        await new Promise(resolve => setTimeout(resolve, 100))
        waitTime += 100
      }

      if (this.currentData.isTickerConnected) {
        console.log('📈 Ticker数据初始化成功')
      } else {
        console.warn('⚠️ Ticker数据初始化超时，但继续运行')
      }

    } catch (error) {
      console.error('Ticker数据源初始化失败:', error)
      this.currentData.isTickerConnected = false
      // 不抛出异常，因为K线数据可以独立工作
    }
  }

  // 🔄 处理K线数据更新
  private handleKlineUpdate(klineData: KlineData): void {
    this.lastKlineData = klineData
    
    // 更新K线相关数据
    this.currentData.currentPrice = klineData.close
    this.currentData.open = klineData.open
    this.currentData.high = klineData.high
    this.currentData.low = klineData.low
    this.currentData.klineUpdateTime = Date.now()
    this.currentData.isKlineConnected = true

    // 添加到价格历史（用于一致性检查）
    this.priceHistoryForConsistency.push(klineData.close)
    if (this.priceHistoryForConsistency.length > 20) {
      this.priceHistoryForConsistency.shift()
    }

    console.log(`📊 K线更新: ${klineData.close.toFixed(2)}`)
    
    this.updateDataQuality()
    this.notifySubscribers()
  }

  // 🔄 处理Ticker数据更新
  private handleTickerUpdate(tickerData: TickerData): void {
    this.lastTickerData = tickerData
    
    // 更新Ticker相关数据
    this.currentData.bid = tickerData.bid
    this.currentData.ask = tickerData.ask
    this.currentData.spread = tickerData.ask - tickerData.bid
    this.currentData.volume24h = tickerData.volume24h
    this.currentData.change24h = tickerData.change24h
    this.currentData.high24h = tickerData.high24h
    this.currentData.low24h = tickerData.low24h
    this.currentData.tickerUpdateTime = Date.now()
    this.currentData.isTickerConnected = true

    console.log(`📈 Ticker更新: bid ${tickerData.bid.toFixed(2)}, ask ${tickerData.ask.toFixed(2)}`)
    
    this.updateDataQuality()
    this.notifySubscribers()
  }

  // 🔍 更新数据质量评估
  private updateDataQuality(): void {
    const now = Date.now()
    
    // 计算延迟
    const klineLatency = this.currentData.isKlineConnected 
      ? Math.max(0, now - this.currentData.klineUpdateTime) 
      : Infinity
    const tickerLatency = this.currentData.isTickerConnected 
      ? Math.max(0, now - this.currentData.tickerUpdateTime) 
      : Infinity

    // 计算价格一致性（K线价格与Ticker价格的差异）
    let priceConsistency = 1.0
    if (this.currentData.isKlineConnected && this.currentData.isTickerConnected) {
      const klinePrice = this.currentData.currentPrice
      const tickerPrice = this.lastTickerData?.price || 0
      
      if (klinePrice > 0 && tickerPrice > 0) {
        const priceDiff = Math.abs(klinePrice - tickerPrice) / klinePrice
        priceConsistency = Math.max(0, 1 - (priceDiff * 10)) // 1%差异对应90%一致性
      }
    }

    // 计算数据新鲜度
    const maxFreshnessDelay = 10000 // 10秒
    const dataFreshness = Math.max(0, 1 - Math.min(klineLatency, tickerLatency) / maxFreshnessDelay)

    // 综合评估数据质量
    if (this.currentData.isKlineConnected && this.currentData.isTickerConnected && 
        klineLatency < 5000 && tickerLatency < 5000 && priceConsistency > 0.95) {
      this.currentData.dataQuality = 'HIGH'
    } else if (this.currentData.isKlineConnected && klineLatency < 10000) {
      this.currentData.dataQuality = 'MEDIUM'
    } else {
      this.currentData.dataQuality = 'LOW'
    }
  }

  // 🔍 启动数据质量监控
  private startQualityMonitoring(): void {
    this.qualityCheckInterval = setInterval(() => {
      this.updateDataQuality()
      
      const quality = this.getDataQuality()
      if (quality.overallScore < 50) {
        console.warn('⚠️ 数据质量较低:', quality)
      }
    }, 5000) // 每5秒检查一次数据质量
  }

  // 📊 获取数据质量报告
  getDataQuality(): PriceDataQuality {
    const now = Date.now()
    
    const klineLatency = this.currentData.isKlineConnected 
      ? Math.max(0, now - this.currentData.klineUpdateTime) 
      : Infinity
    const tickerLatency = this.currentData.isTickerConnected 
      ? Math.max(0, now - this.currentData.tickerUpdateTime) 
      : Infinity

    // 价格一致性
    let priceConsistency = 1.0
    if (this.lastKlineData && this.lastTickerData) {
      const priceDiff = Math.abs(this.lastKlineData.close - this.lastTickerData.price) / this.lastKlineData.close
      priceConsistency = Math.max(0, 1 - (priceDiff * 10))
    }

    // 数据新鲜度
    const dataFreshness = Math.max(0, 1 - Math.min(klineLatency, tickerLatency) / 10000)

    // 整体评分
    let overallScore = 0
    if (this.currentData.isKlineConnected) overallScore += 40
    if (this.currentData.isTickerConnected) overallScore += 20
    overallScore += priceConsistency * 25
    overallScore += dataFreshness * 15

    return {
      klineLatency: isFinite(klineLatency) ? klineLatency : -1,
      tickerLatency: isFinite(tickerLatency) ? tickerLatency : -1,
      priceConsistency,
      dataFreshness,
      overallScore
    }
  }

  // 📡 订阅统一价格数据
  subscribe(callback: (data: UnifiedPriceData) => void): () => void {
    this.subscribers.push(callback)
    
    // 立即发送当前数据
    callback(this.currentData)
    
    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  // 🔔 通知所有订阅者
  private notifySubscribers(): void {
    this.subscribers.forEach(callback => {
      try {
        callback(this.currentData)
      } catch (error) {
        console.error('通知订阅者失败:', error)
      }
    })
  }

  // 🎯 获取当前统一价格数据
  getUnifiedData(): UnifiedPriceData {
    return { ...this.currentData }
  }

  // 💰 获取策略专用价格（K线收盘价，最准确）
  getStrategyPrice(): number {
    return this.currentData.currentPrice
  }

  // 📊 获取市场深度价格（Ticker买卖价）
  getMarketDepth(): { bid: number, ask: number, spread: number } {
    return {
      bid: this.currentData.bid,
      ask: this.currentData.ask,
      spread: this.currentData.spread
    }
  }

  // ✅ 检查数据有效性
  isPriceDataValid(): boolean {
    return this.currentData.currentPrice > 0 && 
           this.currentData.isKlineConnected &&
           this.currentData.dataQuality !== 'LOW'
  }

  // 🔍 检查连接状态
  isConnected(): boolean {
    return this.isMonitoring && this.currentData.isKlineConnected
  }

  // 📈 获取当前监控币种
  getCurrentSymbol(): string | null {
    return this.currentSymbol
  }
}

// 创建全局实例
export const priceDataUnifier = new PriceDataUnifier() 