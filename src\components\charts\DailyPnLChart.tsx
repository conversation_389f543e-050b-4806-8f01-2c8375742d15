// 模拟每日盈亏数据
const mockDailyPnLData = [
  { date: '01-01', pnl: 125.50, trades: 3, volume: 12500 },
  { date: '01-02', pnl: -89.20, trades: 5, volume: 15600 },
  { date: '01-03', pnl: 234.75, trades: 7, volume: 23400 },
  { date: '01-04', pnl: 67.30, trades: 2, volume: 8900 },
  { date: '01-05', pnl: -156.80, trades: 4, volume: 18700 },
  { date: '01-06', pnl: 345.60, trades: 8, volume: 34500 },
  { date: '01-07', pnl: 78.90, trades: 3, volume: 12300 },
  { date: '01-08', pnl: -45.30, trades: 6, volume: 14200 },
  { date: '01-09', pnl: 189.40, trades: 5, volume: 21800 },
  { date: '01-10', pnl: 123.70, trades: 4, volume: 16700 },
  { date: '01-11', pnl: -67.20, trades: 3, volume: 11500 },
  { date: '01-12', pnl: 278.50, trades: 7, volume: 28900 },
  { date: '01-13', pnl: 94.80, trades: 2, volume: 9800 },
  { date: '01-14', pnl: -123.40, trades: 5, volume: 17200 },
  { date: '01-15', pnl: 156.90, trades: 6, volume: 19600 },
  { date: '01-16', pnl: -78.50, trades: 4, volume: 13400 },
  { date: '01-17', pnl: 289.30, trades: 9, volume: 32100 },
  { date: '01-18', pnl: 45.60, trades: 3, volume: 8700 },
  { date: '01-19', pnl: -167.80, trades: 5, volume: 19800 },
  { date: '01-20', pnl: 198.40, trades: 7, volume: 24300 },
  { date: '01-21', pnl: 87.20, trades: 4, volume: 14500 },
  { date: '01-22', pnl: -134.50, trades: 6, volume: 18100 },
  { date: '01-23', pnl: 245.70, trades: 8, volume: 27600 },
  { date: '01-24', pnl: 76.80, trades: 3, volume: 12100 },
  { date: '01-25', pnl: -98.30, trades: 5, volume: 15800 },
  { date: '01-26', pnl: 167.90, trades: 6, volume: 20900 },
  { date: '01-27', pnl: 203.50, trades: 7, volume: 25400 },
  { date: '01-28', pnl: -89.60, trades: 4, volume: 14800 },
  { date: '01-29', pnl: 134.20, trades: 5, volume: 17900 },
  { date: '01-30', pnl: 267.80, trades: 8, volume: 29300 },
  { date: '02-01', pnl: -145.30, trades: 6, volume: 18900 },
  { date: '02-02', pnl: 178.90, trades: 7, volume: 22700 },
  { date: '02-03', pnl: 95.60, trades: 4, volume: 13600 },
  { date: '02-04', pnl: -187.40, trades: 8, volume: 24100 },
  { date: '02-05', pnl: 234.70, trades: 9, volume: 28400 },
  { date: '02-06', pnl: 67.80, trades: 3, volume: 10200 },
  { date: '02-07', pnl: -123.50, trades: 5, volume: 16800 },
  { date: '02-08', pnl: 189.20, trades: 6, volume: 21500 },
  { date: '02-09', pnl: 145.60, trades: 7, volume: 19300 },
  { date: '02-10', pnl: -76.90, trades: 4, volume: 12900 },
]

import { useState, useEffect, useMemo, useRef } from 'react'

interface DailyPnLChartProps {
  isConnected?: boolean;
}

export function DailyPnLChart({ isConnected = false }: DailyPnLChartProps) {
  const [hoveredBar, setHoveredBar] = useState<number | null>(null)
  const [animationProgress, setAnimationProgress] = useState(0)
  const [viewMode, setViewMode] = useState<'curve' | 'bars' | 'heatmap'>('curve')
  
  // 拖拽和缩放状态
  const [dragState, setDragState] = useState({
    isDragging: false,
    startX: 0,
    offsetX: 0
  })
  const [zoom, setZoom] = useState(1)
  const [isHoveringChart, setIsHoveringChart] = useState(false)
  
  // DOM引用
  const chartContainerRef = useRef<HTMLDivElement>(null)

  // 动画效果
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimationProgress(1)
    }, 100)
    return () => clearTimeout(timer)
  }, [])

  // 防止图表区域滚轮事件冒泡到页面
  useEffect(() => {
    const container = chartContainerRef.current
    if (!container || viewMode !== 'curve') return

    const handleWheel = (e: WheelEvent) => {
      // 完全阻止滚轮事件的所有默认行为和冒泡
      e.preventDefault()
      e.stopPropagation()
      e.stopImmediatePropagation()
      
      // 执行图表缩放逻辑
      if (isHoveringChart) {
        const zoomFactor = 0.1
        const newZoom = Math.max(0.5, Math.min(3, zoom + (e.deltaY > 0 ? -zoomFactor : zoomFactor)))
        setZoom(newZoom)
        
        if (newZoom === 1) {
          setDragState(prev => ({ ...prev, offsetX: 0 }))
        }
      }
      
      return false
    }

    const handleMouseEnter = () => setIsHoveringChart(true)
    const handleMouseLeave = () => setIsHoveringChart(false)

    // 直接在DOM元素上添加原生事件监听器
    container.addEventListener('wheel', handleWheel, { passive: false, capture: true })
    container.addEventListener('mouseenter', handleMouseEnter)
    container.addEventListener('mouseleave', handleMouseLeave)
    
    return () => {
      container.removeEventListener('wheel', handleWheel, { capture: true } as any)
      container.removeEventListener('mouseenter', handleMouseEnter)
      container.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [viewMode, isHoveringChart, zoom, dragState.offsetX])

  // 如果未连接，显示空状态
  if (!isConnected) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-3">
          <div className="w-16 h-16 bg-slate-700/50 rounded-xl flex items-center justify-center mx-auto">
            <div className="w-8 h-8 border-2 border-slate-600 border-dashed rounded"></div>
          </div>
          <div>
            <p className="text-slate-400 font-medium">暂无数据</p>
            <p className="text-slate-500 text-sm">连接API后显示实时盈亏数据</p>
          </div>
        </div>
      </div>
    )
  }

  // 计算统计数据
  const stats = useMemo(() => {
    const totalPnL = mockDailyPnLData.reduce((sum, d) => sum + d.pnl, 0)
    const totalTrades = mockDailyPnLData.reduce((sum, d) => sum + d.trades, 0)
    const profitDays = mockDailyPnLData.filter(d => d.pnl > 0).length
    const maxProfit = Math.max(...mockDailyPnLData.map(d => d.pnl))
    const maxLoss = Math.min(...mockDailyPnLData.map(d => d.pnl))
    const winRate = (profitDays / mockDailyPnLData.length * 100)
    const avgDailyPnL = totalPnL / mockDailyPnLData.length

    return {
      totalPnL,
      totalTrades,
      profitDays,
      maxProfit,
      maxLoss,
      winRate,
      avgDailyPnL
    }
  }, [])

  // 获取颜色强度
  const getColorIntensity = (pnl: number) => {
    const maxAbs = Math.max(Math.abs(stats.maxProfit), Math.abs(stats.maxLoss))
    return Math.abs(pnl) / maxAbs
  }

  // 获取柱状图高度
  const getBarHeight = (pnl: number) => {
    const maxAbs = Math.max(Math.abs(stats.maxProfit), Math.abs(stats.maxLoss))
    return (Math.abs(pnl) / maxAbs) * 120 * animationProgress
  }

  // 曲线图交互事件处理
  const handleCurveMouseDown = (e: React.MouseEvent<SVGSVGElement>) => {
    if (viewMode !== 'curve') return
    setDragState({
      isDragging: true,
      startX: e.clientX,
      offsetX: dragState.offsetX
    })
  }

  const handleCurveMouseMove = (e: React.MouseEvent<SVGSVGElement>) => {
    if (viewMode !== 'curve' || !dragState.isDragging) return
    
    const deltaX = e.clientX - dragState.startX
    const chartWidth = 800 * zoom
    const maxOffset = Math.min(0, -(chartWidth - 800))
    const newOffsetX = Math.max(maxOffset, Math.min(0, dragState.offsetX + deltaX))
    
    setDragState(prev => ({
      ...prev,
      offsetX: newOffsetX
    }))
  }

  const handleCurveMouseUp = () => {
    if (viewMode !== 'curve') return
    setDragState(prev => ({ ...prev, isDragging: false }))
  }



  return (
    <div className="space-y-6">
      {/* 标题和控制区域 */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
            📊 每日盈亏分析
          </h2>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>总交易: {stats.totalTrades}笔</span>
            <span>•</span>
            <span>盈利天数: {stats.profitDays}/{mockDailyPnLData.length}天</span>
            <span>•</span>
            <span>胜率: {stats.winRate.toFixed(1)}%</span>
          </div>
        </div>
        
        {/* 视图切换 */}
        <div className="flex items-center gap-2 bg-slate-800/50 rounded-xl p-1">
          <button
            onClick={() => setViewMode('curve')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              viewMode === 'curve' 
                ? 'bg-cyan-500 text-white shadow-lg' 
                : 'text-slate-400 hover:text-white'
            }`}
          >
            📈 曲线图
          </button>
          <button
            onClick={() => setViewMode('bars')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              viewMode === 'bars' 
                ? 'bg-blue-500 text-white shadow-lg' 
                : 'text-slate-400 hover:text-white'
            }`}
          >
            📊 柱状图
          </button>
          <button
            onClick={() => setViewMode('heatmap')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              viewMode === 'heatmap' 
                ? 'bg-purple-500 text-white shadow-lg' 
                : 'text-slate-400 hover:text-white'
            }`}
          >
            🔥 热力图
          </button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-emerald-500/20 to-green-600/10 border border-emerald-500/30 rounded-xl p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-emerald-400 text-2xl">💰</span>
            <span className="text-xs text-emerald-300 font-medium">累计盈亏</span>
          </div>
          <p className={`text-xl font-bold ${stats.totalPnL >= 0 ? 'text-emerald-400' : 'text-red-400'}`}>
            {stats.totalPnL >= 0 ? '+' : ''}${stats.totalPnL.toFixed(2)}
          </p>
        </div>

        <div className="bg-gradient-to-br from-blue-500/20 to-indigo-600/10 border border-blue-500/30 rounded-xl p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-blue-400 text-2xl">📈</span>
            <span className="text-xs text-blue-300 font-medium">日均收益</span>
          </div>
          <p className={`text-xl font-bold ${stats.avgDailyPnL >= 0 ? 'text-blue-400' : 'text-red-400'}`}>
            {stats.avgDailyPnL >= 0 ? '+' : ''}${stats.avgDailyPnL.toFixed(2)}
          </p>
        </div>

        <div className="bg-gradient-to-br from-purple-500/20 to-violet-600/10 border border-purple-500/30 rounded-xl p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-purple-400 text-2xl">🚀</span>
            <span className="text-xs text-purple-300 font-medium">最大盈利</span>
          </div>
          <p className="text-xl font-bold text-purple-400">
            +${stats.maxProfit.toFixed(2)}
          </p>
        </div>

        <div className="bg-gradient-to-br from-orange-500/20 to-red-600/10 border border-orange-500/30 rounded-xl p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-orange-400 text-2xl">⚠️</span>
            <span className="text-xs text-orange-300 font-medium">最大亏损</span>
          </div>
          <p className="text-xl font-bold text-orange-400">
            ${stats.maxLoss.toFixed(2)}
          </p>
        </div>
      </div>

      {/* 主图表区域 */}
      <div className="relative bg-gradient-to-br from-slate-900/60 to-slate-800/40 rounded-2xl p-6 min-h-[400px] overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-purple-500/5"></div>
        <div className="absolute top-4 right-4 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-2xl"></div>

        {viewMode === 'curve' ? (
          /* 曲线图视图 */
          <div 
            ref={chartContainerRef}
            className="relative z-10"
            style={{ touchAction: 'none' }}
          >
            <svg 
              className={`w-full h-64 ${dragState.isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
              viewBox={`${-dragState.offsetX} 0 ${800 / zoom} 240`}
              onMouseDown={handleCurveMouseDown}
              onMouseMove={handleCurveMouseMove}
              onMouseUp={handleCurveMouseUp}
              onMouseLeave={handleCurveMouseUp}
            >
              <defs>
                {/* 渐变定义 */}
                <linearGradient id="curveGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.3" />
                  <stop offset="100%" stopColor="rgb(59, 130, 246)" stopOpacity="0.05" />
                </linearGradient>
                
                {/* 发光效果 */}
                <filter id="curveGlow">
                  <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                  <feMerge> 
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              </defs>

              {/* 背景网格 */}
              {(() => {
                const chartWidth = 800 * zoom
                return (
                  <>
                    {/* 水平网格线 */}
                    {[...Array(6)].map((_, i) => (
                      <line
                        key={`grid-h-${i}`}
                        x1={40}
                        y1={40 + (i * 32)}
                        x2={chartWidth - 40}
                        y2={40 + (i * 32)}
                        stroke="rgb(71, 85, 105)"
                        strokeWidth="0.5"
                        opacity="0.3"
                      />
                    ))}
                    {/* 垂直网格线 */}
                    {mockDailyPnLData.map((_, i) => (
                      i % 4 === 0 && (
                        <line
                          key={`grid-v-${i}`}
                          x1={40 + (i / (mockDailyPnLData.length - 1)) * (chartWidth - 80)}
                          y1={40}
                          x2={40 + (i / (mockDailyPnLData.length - 1)) * (chartWidth - 80)}
                          y2={200}
                          stroke="rgb(71, 85, 105)"
                          strokeWidth="0.5"
                          opacity="0.3"
                        />
                      )
                    ))}
                  </>
                )
              })()}

              {/* 零轴线 */}
              <line
                x1={40}
                y1={120}
                x2={(800 * zoom) - 40}
                y2={120}
                stroke="rgb(156, 163, 175)"
                strokeWidth="1"
                strokeDasharray="4,2"
                opacity="0.8"
              />

              {/* 计算曲线路径 */}
              {(() => {
                const maxAbs = Math.max(Math.abs(stats.maxProfit), Math.abs(stats.maxLoss))
                const chartWidth = 800 * zoom
                const points = mockDailyPnLData.map((data, i) => {
                  const x = 40 + (i / (mockDailyPnLData.length - 1)) * (chartWidth - 80)
                  const y = 120 - (data.pnl / maxAbs) * 70 * animationProgress
                  return { x, y, pnl: data.pnl, date: data.date, trades: data.trades, volume: data.volume }
                })

                const pathD = points.map((point, i) => 
                  `${i === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
                ).join(' ')

                const areaPath = `${pathD} L ${points[points.length - 1].x} 120 L ${points[0].x} 120 Z`

                return (
                  <g>
                    {/* 曲线下方填充区域 */}
                    <path
                      d={areaPath}
                      fill="url(#curveGradient)"
                      opacity="0.6"
                    />

                    {/* 主曲线 */}
                    <path
                      d={pathD}
                      fill="none"
                      stroke="rgb(59, 130, 246)"
                      strokeWidth="3"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      filter="url(#curveGlow)"
                    />

                    {/* 数据点 */}
                    {points.map((point, i) => (
                      <g key={i}>
                        <circle
                          cx={point.x}
                          cy={point.y}
                          r="4"
                          fill={point.pnl >= 0 ? "rgb(34, 197, 94)" : "rgb(239, 68, 68)"}
                          stroke="white"
                          strokeWidth="2"
                          className="cursor-pointer"
                          onMouseEnter={() => setHoveredBar(i)}
                          onMouseLeave={() => setHoveredBar(null)}
                          style={{
                            animation: `fadeIn 0.6s ease-out ${i * 0.05}s both`
                          }}
                        />
                        
                        {/* 悬浮提示 */}
                        {hoveredBar === i && (
                          <foreignObject x={point.x - 60} y={point.y - 80} width="120" height="70">
                            <div className="bg-black/95 backdrop-blur-sm text-white text-xs rounded-lg p-3 animate-fadeIn">
                              <div className="space-y-1">
                                <div className="font-semibold text-center">{point.date}</div>
                                <div className={`text-center ${point.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                  PnL: {point.pnl >= 0 ? '+' : ''}${point.pnl.toFixed(2)}
                                </div>
                                <div className="text-blue-400 text-center">{point.trades} 笔交易</div>
                              </div>
                            </div>
                          </foreignObject>
                        )}
                      </g>
                    ))}

                    {/* X轴标签 */}
                    {points.map((point, i) => (
                      i % 4 === 0 && (
                        <text
                          key={`label-${i}`}
                          x={point.x}
                          y={220}
                          textAnchor="middle"
                          fontSize="11"
                          fill="rgb(156, 163, 175)"
                        >
                          {point.date}
                        </text>
                      )
                    ))}
                  </g>
                )
              })()}
            </svg>
          </div>
        ) : viewMode === 'bars' ? (
          /* 柱状图视图 */
          <div className="relative z-10">
            <div className="flex items-end justify-center gap-1 h-64 px-4">
              {mockDailyPnLData.map((data, index) => {
                const barHeight = getBarHeight(data.pnl)
                const isPositive = data.pnl >= 0
                
                return (
                  <div
                    key={index}
                    className="flex flex-col items-center group cursor-pointer"
                    onMouseEnter={() => setHoveredBar(index)}
                    onMouseLeave={() => setHoveredBar(null)}
                  >
                    {/* 柱子 */}
                    <div
                      className={`w-4 rounded-t-sm transition-all duration-300 ${
                        isPositive 
                          ? 'bg-gradient-to-t from-emerald-500 to-green-400' 
                          : 'bg-gradient-to-t from-red-500 to-orange-400'
                      } ${hoveredBar === index ? 'scale-110 shadow-lg' : ''}`}
                      style={{
                        height: `${barHeight}px`,
                        transformOrigin: 'bottom',
                        animation: `fadeInUp 0.6s ease-out ${index * 0.03}s both`
                      }}
                    />
                    
                    {/* 日期标签 */}
                    <span className={`text-xs mt-2 transition-colors ${
                      hoveredBar === index ? 'text-white font-medium' : 'text-slate-400'
                    }`}>
                      {data.date.split('-')[1]}
                    </span>
                    
                    {/* 悬浮提示 */}
                    {hoveredBar === index && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black/95 backdrop-blur-sm text-white text-xs rounded-lg p-2.5 min-w-[130px] z-20 animate-fadeIn shadow-xl border border-white/10">
                        <div className="space-y-1">
                          <div className="font-semibold text-center text-sm">{data.date}</div>
                          <div className="h-px bg-white/20 my-1"></div>
                          <div className={`text-center font-medium ${data.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {data.pnl >= 0 ? '+' : ''}${data.pnl.toFixed(2)}
                          </div>
                          <div className="text-blue-400 text-center text-xs">{data.trades}笔</div>
                          <div className="text-purple-400 text-center text-xs">${data.volume.toLocaleString()}</div>
                        </div>
                        {/* 箭头指向柱子 */}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-3 border-r-3 border-t-3 border-transparent border-t-black/95"></div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
            
            {/* 零轴线 */}
            <div className="absolute bottom-24 left-4 right-4 h-px bg-gradient-to-r from-transparent via-slate-500 to-transparent"></div>
          </div>
        ) : (
          /* 热力图视图 */
          <div className="relative z-10 grid grid-cols-10 gap-2 p-4">
            {mockDailyPnLData.map((data, index) => {
              const intensity = getColorIntensity(data.pnl)
              const isPositive = data.pnl >= 0
              
              return (
                <div
                  key={index}
                  className={`aspect-square rounded-lg transition-all duration-300 cursor-pointer flex flex-col items-center justify-between p-1 group relative ${
                    hoveredBar === index ? 'scale-110 shadow-xl z-10' : ''
                  }`}
                  style={{
                    backgroundColor: isPositive 
                      ? `rgba(34, 197, 94, ${0.3 + intensity * 0.7})` 
                      : `rgba(239, 68, 68, ${0.3 + intensity * 0.7})`,
                    animation: `fadeIn 0.8s ease-out ${index * 0.05}s both`
                  }}
                  onMouseEnter={() => setHoveredBar(index)}
                  onMouseLeave={() => setHoveredBar(null)}
                >
                  {/* 日期显示在顶部 */}
                  <div className="text-xs text-white/50 font-medium leading-none text-center">
                    {data.date}
                  </div>
                  
                  {/* PnL数值显示在底部 */}
                  <div className={`text-xs font-bold leading-none text-center ${
                    data.pnl >= 0 ? 'text-white/90' : 'text-white/90'
                  } group-hover:text-white transition-colors`}>
                    {data.pnl >= 0 ? '+' : ''}{Math.round(data.pnl)}
                  </div>
                  
                  {/* 悬浮提示 */}
                  {hoveredBar === index && (
                    <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/95 backdrop-blur-sm text-white text-xs rounded-lg p-2.5 min-w-[120px] z-30 animate-fadeIn shadow-xl border border-white/10">
                      <div className="space-y-1">
                        <div className="font-semibold text-center text-sm">{data.date}</div>
                        <div className="h-px bg-white/20 my-1"></div>
                        <div className={`text-center font-medium ${data.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {data.pnl >= 0 ? '+' : ''}${data.pnl.toFixed(2)}
                        </div>
                        <div className="text-blue-400 text-center text-xs">{data.trades} 笔</div>
                      </div>
                      {/* 箭头指向方格 */}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-3 border-r-3 border-t-3 border-transparent border-t-black/95"></div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        )}

        {/* 曲线图专用控制面板 */}
        {viewMode === 'curve' && (
          <div className="absolute top-4 right-4 space-y-2">
            {/* 缩放控制 */}
            <div className="bg-black/40 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-white">
              <div className="flex items-center gap-3">
                <button 
                  onClick={() => {
                    const newZoom = Math.max(0.5, zoom - 0.2)
                    setZoom(newZoom)
                    if (newZoom === 1) setDragState(prev => ({ ...prev, offsetX: 0 }))
                  }}
                  className="hover:text-cyan-400 transition-colors"
                  disabled={zoom <= 0.5}
                >
                  ➖
                </button>
                <span className="min-w-[3rem] text-center">{(zoom * 100).toFixed(0)}%</span>
                <button 
                  onClick={() => setZoom(Math.min(3, zoom + 0.2))}
                  className="hover:text-cyan-400 transition-colors"
                  disabled={zoom >= 3}
                >
                  ➕
                </button>
              </div>
              <button 
                onClick={() => {
                  setZoom(1)
                  setDragState(prev => ({ ...prev, offsetX: 0 }))
                }}
                className="w-full mt-2 px-2 py-1 bg-cyan-500/20 hover:bg-cyan-500/30 rounded text-cyan-300 transition-colors"
              >
                重置视图
              </button>
            </div>
            
            {/* 操作提示 */}
            <div className="bg-black/40 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-white/70">
              <div className="space-y-1">
                <div>🖱️ {dragState.isDragging ? '拖拽中...' : '点击拖拽查看'}</div>
                <div className={isHoveringChart ? 'text-green-400 font-medium' : 'text-slate-400'}>
                  🎡 {isHoveringChart ? '✓ 滚轮缩放已激活' : '悬停后滚轮缩放'}
                </div>
                <div className="text-cyan-400">缩放级别: {(zoom * 100).toFixed(0)}%</div>
                {isHoveringChart && (
                  <div className="text-green-400 animate-pulse text-xs">
                    🛡️ 页面滚动已完全锁定
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 图例 */}
        <div className="absolute bottom-4 left-6 flex items-center gap-6 text-xs">
          {viewMode === 'curve' ? (
            <>
              <div className="flex items-center gap-2">
                <div className="w-4 h-0.5 bg-blue-500 rounded-full"></div>
                <span className="text-white/80">PnL曲线</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-white/80">盈利点</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-white/80">亏损点</span>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center gap-2">
                <div className="w-4 h-2 bg-gradient-to-r from-emerald-500 to-green-400 rounded-full"></div>
                <span className="text-white/80">盈利</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-2 bg-gradient-to-r from-red-500 to-orange-400 rounded-full"></div>
                <span className="text-white/80">亏损</span>
              </div>
            </>
          )}
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-white/30 rounded-full animate-pulse"></div>
            <span className="text-white/60">实时数据</span>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px) scaleY(0);
          }
          to {
            opacity: 1;
            transform: translateY(0) scaleY(1);
          }
        }
        
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: scale(0.8);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>
    </div>
  )
}
