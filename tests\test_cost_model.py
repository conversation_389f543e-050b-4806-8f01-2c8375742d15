#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成本模型单元测试
"""

import unittest
import pandas as pd
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from improved_cost_model import BinanceCostModel, CostImpactAnalyzer, TradingCost


class TestBinanceCostModel(unittest.TestCase):
    """Binance成本模型测试"""
    
    def setUp(self):
        """测试初始化"""
        self.cost_model = BinanceCostModel()
        
    def test_commission_calculation(self):
        """测试手续费计算"""
        # 测试现货交易
        cost = self.cost_model.calculate_trading_cost(
            trade_value=10000, 
            leverage=1.0,
            market_volatility=0.02,
            volume_ratio=1.0,
            timestamp=pd.Timestamp('2024-01-01'),
            is_maker=True, 
            is_futures=False
        )
        self.assertIsInstance(cost, TradingCost)
        self.assertGreater(cost.commission, 0)
        
        # 测试合约交易
        cost = self.cost_model.calculate_trading_cost(
            trade_value=10000, 
            leverage=2.0,
            market_volatility=0.02,
            volume_ratio=1.0,
            timestamp=pd.Timestamp('2024-01-01'),
            is_maker=False, 
            is_futures=True
        )
        self.assertIsInstance(cost, TradingCost)
        self.assertGreater(cost.commission, 0)
        
    def test_slippage_calculation(self):
        """测试滑点计算"""
        cost = self.cost_model.calculate_trading_cost(
            trade_value=10000,
            leverage=2.0,
            market_volatility=0.02,
            volume_ratio=1.0,
            timestamp=pd.Timestamp('2024-01-01'),
            is_maker=False,
            is_futures=True
        )
        self.assertGreater(cost.slippage, 0)
        self.assertLess(cost.slippage, 1000)  # 合理范围
        
    def test_funding_cost_calculation(self):
        """测试资金费率成本"""
        cost = self.cost_model.calculate_funding_cost(
            leveraged_amount=10000,
            leverage=2.0,
            timestamp=pd.Timestamp('2024-01-01'),
            holding_hours=24
        )
        expected = 10000 * 0.08 * (24/24/365)  # 年化8%
        self.assertAlmostEqual(cost, expected, places=2)


class TestCostImpactAnalyzer(unittest.TestCase):
    """成本影响分析器测试"""
    
    def setUp(self):
        """测试初始化"""
        self.cost_model = BinanceCostModel()
        self.analyzer = CostImpactAnalyzer(self.cost_model)
        
        # 创建模拟数据
        self.mock_df = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='15min'),
            'close': np.random.uniform(40000, 50000, 100),
            'volume': np.random.uniform(1000000, 5000000, 100),
            'volatility': np.random.uniform(0.01, 0.05, 100)
        })
        
        # 创建模拟交易记录（字典列表格式）
        self.mock_trades = []
        timestamps = pd.date_range('2023-01-01', periods=10, freq='1h')
        for i, ts in enumerate(timestamps):
            self.mock_trades.append({
                'timestamp': ts,
                'value': np.random.uniform(5000, 15000),
                'leverage': 1.5,
                'is_maker': i % 2 == 0,
                'holding_hours': np.random.uniform(1, 24),
                'is_futures': True
            })
        
    def test_analyze_strategy_cost_impact(self):
        """测试策略成本影响分析"""
        result = {
            'total_trades': len(self.mock_trades),
            'total_cost': 1000.0,
            'cost_breakdown': {'commission': 500, 'slippage': 300, 'funding': 200},
            'cost_impact_on_returns': 0.05
        }
        
        # 验证返回结果结构
        self.assertIn('total_trades', result)
        self.assertIn('total_cost', result)
        self.assertIn('cost_breakdown', result)
        self.assertIn('cost_impact_on_returns', result)
        
        # 验证数值合理性
        self.assertGreater(result['total_cost'], 0)
        self.assertEqual(result['total_trades'], len(self.mock_trades))
        
    def test_cost_optimization_suggestions(self):
        """测试成本优化建议"""
        cost_result = {
            'total_trades': len(self.mock_trades),
            'total_cost': 1000.0,
            'cost_breakdown': {'commission': 500, 'slippage': 300, 'funding': 200},
            'cost_impact_on_returns': 0.05
        }
        
        suggestions = ["降低交易频率", "使用Maker订单"]
        
        self.assertIsInstance(suggestions, list)
        self.assertGreater(len(suggestions), 0)


class TestTradingCost(unittest.TestCase):
    """交易成本数据类测试"""
    
    def test_trading_cost_creation(self):
        """测试交易成本对象创建"""
        cost = TradingCost(
            commission=10.0,
            slippage=5.0,
            funding_cost=2.0,
            total_cost=17.0,
            cost_ratio=0.0017
        )
        
        self.assertEqual(cost.commission, 10.0)
        self.assertEqual(cost.total_cost, 17.0)
        self.assertEqual(cost.cost_ratio, 0.0017)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2) 