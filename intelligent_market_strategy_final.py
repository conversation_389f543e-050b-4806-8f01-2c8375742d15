#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能行情识别与动态策略切换系统 - 最终版
核心理念：震荡做网格，趋势做追踪，0.2%快速止盈
作者：顶尖量化交易师
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Optional
import time
from dataclasses import dataclass
from enum import Enum


class MarketState(Enum):
    """市场状态"""
    SIDEWAYS = "震荡"
    UPTREND = "上涨趋势" 
    DOWNTREND = "下跌趋势"


@dataclass
class Trade:
    """交易记录"""
    timestamp: pd.Timestamp
    action: str  # 'buy', 'sell'
    price: float
    quantity: float
    strategy: str  # 'grid', 'trend'
    executed: bool = True


class OptimizedTradingSystem:
    """优化的交易系统"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.position = 0.0
        self.portfolio_value = initial_capital
        
        # 交易成本
        self.commission_rate = 0.0005  # 0.05%
        self.slippage_rate = 0.0002    # 0.02%
        
        # 策略参数
        self.grid_spacing = 0.01       # 网格间距1%
        self.trend_profit = 0.002      # 趋势止盈0.2%
        self.trend_threshold = 0.015   # 趋势判断阈值1.5%
        
        # 状态变量
        self.last_grid_price = None
        self.trend_entry_price = None
        self.trend_position = 0  # 1=多头, -1=空头, 0=无仓位
        
        # 记录
        self.trades = []
        self.portfolio_history = []
    
    def load_and_prepare_data(self, file_path: str) -> pd.DataFrame:
        """加载并预处理数据"""
        print(f"📊 加载数据: {file_path}")
        
        # 加载数据
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print("📊 预计算技术指标...")
        
        # 预计算技术指标
        df['ma_20'] = df['close'].rolling(window=20).mean()
        df['ma_60'] = df['close'].rolling(window=60).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 价格变化率
        df['price_change_4h'] = df['close'].pct_change(16)  # 4小时变化率
        df['volatility'] = df['close'].rolling(window=20).std() / df['close'].rolling(window=20).mean()
        
        print("✅ 技术指标计算完成")
        return df
    
    def detect_market_state(self, row: pd.Series) -> MarketState:
        """检测市场状态"""
        price_change_4h = row.get('price_change_4h', 0)
        volatility = row.get('volatility', 0)
        ma_20 = row.get('ma_20', 0)
        ma_60 = row.get('ma_60', 0)
        
        # 判断趋势
        if abs(price_change_4h) > self.trend_threshold and volatility < 0.02:
            # 明确趋势且波动率不高
            if price_change_4h > 0 and ma_20 > ma_60:
                return MarketState.UPTREND
            elif price_change_4h < 0 and ma_20 < ma_60:
                return MarketState.DOWNTREND
        
        return MarketState.SIDEWAYS
    
    def grid_strategy(self, current_price: float, timestamp: pd.Timestamp) -> Optional[Trade]:
        """网格策略"""
        if self.last_grid_price is None:
            self.last_grid_price = current_price
            return None
        
        # 检查是否触发网格
        price_change = abs(current_price - self.last_grid_price) / self.last_grid_price
        
        if price_change >= self.grid_spacing:
            if current_price > self.last_grid_price:
                # 价格上涨，卖出
                action = 'sell'
                quantity = 0.05  # 减小交易量
            else:
                # 价格下跌，买入
                action = 'buy'
                quantity = 0.05
            
            self.last_grid_price = current_price
            
            return Trade(
                timestamp=timestamp,
                action=action,
                price=current_price,
                quantity=quantity,
                strategy='grid'
            )
        
        return None
    
    def trend_strategy(self, current_price: float, state: MarketState, 
                      rsi: float, timestamp: pd.Timestamp) -> Optional[Trade]:
        """趋势跟踪策略"""
        
        # 开仓逻辑
        if state == MarketState.UPTREND and self.trend_position <= 0:
            if rsi < 65:  # RSI不过热
                self.trend_position = 1
                self.trend_entry_price = current_price
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=0.1,
                    strategy='trend'
                )
        
        elif state == MarketState.DOWNTREND and self.trend_position >= 0:
            if rsi > 35:  # RSI不超卖
                self.trend_position = -1
                self.trend_entry_price = current_price
                return Trade(
                    timestamp=timestamp,
                    action='sell',
                    price=current_price,
                    quantity=0.1,
                    strategy='trend'
                )
        
        # 止盈逻辑
        elif self.trend_position != 0 and self.trend_entry_price:
            if self.trend_position > 0:  # 多头仓位
                profit_rate = (current_price - self.trend_entry_price) / self.trend_entry_price
                if profit_rate >= self.trend_profit:  # 达到0.2%止盈
                    self.trend_position = 0
                    self.trend_entry_price = None
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=0.1,
                        strategy='trend'
                    )
            
            elif self.trend_position < 0:  # 空头仓位
                profit_rate = (self.trend_entry_price - current_price) / self.trend_entry_price
                if profit_rate >= self.trend_profit:  # 达到0.2%止盈
                    self.trend_position = 0
                    self.trend_entry_price = None
                    return Trade(
                        timestamp=timestamp,
                        action='buy',
                        price=current_price,
                        quantity=0.1,
                        strategy='trend'
                    )
        
        return None
    
    def execute_trade(self, trade: Trade) -> bool:
        """执行交易"""
        # 计算交易成本
        trade_value = trade.price * trade.quantity
        cost = trade_value * (self.commission_rate + self.slippage_rate)
        
        if trade.action == 'buy':
            total_cost = trade_value + cost
            if self.cash >= total_cost:
                self.cash -= total_cost
                self.position += trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        elif trade.action == 'sell':
            if self.position >= trade.quantity:
                proceeds = trade_value - cost
                self.cash += proceeds
                self.position -= trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        self.trades.append(trade)
        return trade.executed
    
    def backtest(self, df: pd.DataFrame) -> Dict:
        """运行回测"""
        print("🚀 开始智能策略回测...")
        
        total_rows = len(df)
        start_time = time.time()
        
        # 初始化
        grid_trades = 0
        trend_trades = 0
        state_counts = {state: 0 for state in MarketState}
        
        # 主回测循环
        for i in range(60, total_rows):
            current_row = df.iloc[i]
            current_price = current_row['close']
            current_time = df.index[i]
            
            # 显示进度
            if i % 20000 == 0:
                progress = i / total_rows * 100
                elapsed = time.time() - start_time
                eta = elapsed / (i - 59) * (total_rows - i) if i > 60 else 0
                print(f"⏳ 进度: {progress:.1f}% | 用时: {elapsed:.0f}s | 预计剩余: {eta:.0f}s")
            
            # 检测市场状态
            market_state = self.detect_market_state(current_row)
            state_counts[market_state] += 1
            
            # 生成交易信号
            trade = None
            
            if market_state == MarketState.SIDEWAYS:
                # 震荡行情 - 网格策略
                trade = self.grid_strategy(current_price, current_time)
                if trade and trade.executed:
                    grid_trades += 1
                    
            elif market_state in [MarketState.UPTREND, MarketState.DOWNTREND]:
                # 趋势行情 - 趋势跟踪
                rsi = current_row.get('rsi', 50)
                trade = self.trend_strategy(current_price, market_state, rsi, current_time)
                if trade and trade.executed:
                    trend_trades += 1
            
            # 执行交易
            if trade:
                self.execute_trade(trade)
            
            # 更新组合价值
            self.portfolio_value = self.cash + self.position * current_price
            
            # 记录组合历史（降低频率）
            if i % 500 == 0:
                self.portfolio_history.append({
                    'timestamp': current_time,
                    'value': self.portfolio_value,
                    'cash': self.cash,
                    'position': self.position,
                    'price': current_price,
                    'state': market_state.value
                })
        
        elapsed_time = time.time() - start_time
        print(f"\n✅ 回测完成！用时: {elapsed_time:.1f}秒")
        print(f"📊 网格交易: {grid_trades} | 趋势交易: {trend_trades}")
        
        return self._calculate_performance(df, state_counts)
    
    def _calculate_performance(self, df: pd.DataFrame, state_counts: Dict) -> Dict:
        """计算绩效指标"""
        
        # 基础收益指标
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        # 时间相关指标
        start_date = df.index[60]  # 从第60个数据点开始
        end_date = df.index[-1]
        days = (end_date - start_date).days
        
        if days > 0:
            annual_return = (self.portfolio_value / self.initial_capital) ** (365 / days) - 1
            monthly_return = (self.portfolio_value / self.initial_capital) ** (30 / days) - 1
        else:
            annual_return = 0
            monthly_return = 0
        
        # 计算最大回撤
        if self.portfolio_history:
            portfolio_df = pd.DataFrame(self.portfolio_history)
            values = portfolio_df['value']
            rolling_max = values.expanding().max()
            drawdown = (values - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
        else:
            max_drawdown = 0
        
        # 交易统计
        executed_trades = [t for t in self.trades if t.executed]
        total_trades = len(executed_trades)
        grid_trades = len([t for t in executed_trades if t.strategy == 'grid'])
        trend_trades = len([t for t in executed_trades if t.strategy == 'trend'])
        
        # 市场状态分布
        total_states = sum(state_counts.values())
        state_distribution = {
            state.value: count / total_states if total_states > 0 else 0 
            for state, count in state_counts.items()
        }
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'grid_trades': grid_trades,
            'trend_trades': trend_trades,
            'final_value': self.portfolio_value,
            'final_cash': self.cash,
            'final_position': self.position,
            'market_state_distribution': state_distribution
        }


def main():
    """主函数"""
    print("🧠 智能行情识别与动态策略切换系统 - 最终版")
    print("=" * 70)
    print("💡 核心理念：震荡做网格，趋势做追踪，0.2%快速止盈")
    print("🎯 目标：智能识别行情，动态调整策略，严格风控")
    print("=" * 70)
    
    # 初始化系统
    trading_system = OptimizedTradingSystem(initial_capital=100000)
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = trading_system.load_and_prepare_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 运行回测
    try:
        results = trading_system.backtest(df)
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return
    
    # 输出结果
    print("\n" + "=" * 70)
    print("📊 智能策略回测结果")
    print("=" * 70)
    
    print(f"\n💰 收益表现:")
    print(f"   总收益: {results['total_return']:.2%}")
    print(f"   年化收益: {results['annual_return']:.2%}")
    print(f"   月化收益: {results['monthly_return']:.2%}")
    print(f"   最终资产: ${results['final_value']:,.2f}")
    print(f"   最终现金: ${results['final_cash']:,.2f}")
    print(f"   最终仓位: {results['final_position']:.3f}")
    
    print(f"\n🛡️ 风险控制:")
    print(f"   最大回撤: {results['max_drawdown']:.2%}")
    
    print(f"\n📈 交易统计:")
    print(f"   总交易次数: {results['total_trades']}")
    if results['total_trades'] > 0:
        print(f"   网格交易: {results['grid_trades']} ({results['grid_trades']/results['total_trades']:.1%})")
        print(f"   趋势交易: {results['trend_trades']} ({results['trend_trades']/results['total_trades']:.1%})")
    
    print(f"\n🎯 市场状态分布:")
    for state, percentage in results['market_state_distribution'].items():
        print(f"   {state}: {percentage:.1%}")
    
    # 策略评估
    print(f"\n🔍 策略评估:")
    target_met = (results['max_drawdown'] >= -0.15 and results['monthly_return'] >= 0.10)
    
    if target_met:
        print(f"   ✅ 恭喜！策略达到目标条件")
        print(f"   🎯 回撤控制: {results['max_drawdown']:.2%} (目标: ≤15%)")
        print(f"   🎯 月化收益: {results['monthly_return']:.2%} (目标: ≥10%)")
    else:
        print(f"   🔧 策略需要进一步优化")
        if results['max_drawdown'] < -0.15:
            print(f"   ⚠️ 回撤超限: {results['max_drawdown']:.2%} (目标: ≤15%)")
        if results['monthly_return'] < 0.10:
            print(f"   ⚠️ 收益不足: {results['monthly_return']:.2%} (目标: ≥10%)")
    
    # 与买入持有对比
    buy_hold_return = (df['close'].iloc[-1] - df['close'].iloc[60]) / df['close'].iloc[60]
    print(f"\n📊 策略对比:")
    print(f"   智能策略收益: {results['total_return']:.2%}")
    print(f"   买入持有收益: {buy_hold_return:.2%}")
    
    if results['total_return'] > buy_hold_return:
        print(f"   ✅ 智能策略跑赢买入持有 {(results['total_return'] - buy_hold_return):.2%}")
    else:
        print(f"   ❌ 智能策略跑输买入持有 {(buy_hold_return - results['total_return']):.2%}")
    
    print(f"\n🎊 智能策略回测完成！")
    
    return results


if __name__ == "__main__":
    results = main() 