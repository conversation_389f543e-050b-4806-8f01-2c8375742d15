#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gap风险保护 & 杠杆熔断系统
解决跳空损失和强制平仓风险
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import warnings


class RiskLevel(Enum):
    """风险等级"""
    LOW = "低风险"
    MEDIUM = "中等风险"
    HIGH = "高风险"
    CRITICAL = "极高风险"


@dataclass
class GapEvent:
    """Gap事件记录"""
    timestamp: pd.Timestamp
    price_before: float
    price_after: float
    gap_size: float
    gap_percentage: float
    direction: str  # 'up' or 'down'


@dataclass
class LiquidationEvent:
    """强制平仓事件"""
    timestamp: pd.Timestamp
    trigger_reason: str
    position_size: float
    liquidation_price: float
    loss_amount: float
    leverage_used: float


class GapRiskDetector:
    """Gap风险检测器"""
    
    def __init__(self, gap_threshold: float = 0.005):
        self.gap_threshold = gap_threshold  # 0.5% Gap阈值
        self.gap_history = []
        self.atr_period = 14
        
    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算平均真实波幅 (ATR)"""
        high = df['high']
        low = df['low']
        close = df['close'].shift(1)
        
        tr1 = high - low
        tr2 = abs(high - close)
        tr3 = abs(low - close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def detect_gaps(self, df: pd.DataFrame) -> List[GapEvent]:
        """检测价格跳空"""
        gaps = []
        
        # 计算ATR作为动态阈值
        df['atr'] = self.calculate_atr(df, self.atr_period)
        
        for i in range(1, len(df)):
            current_open = df.iloc[i]['open']
            prev_close = df.iloc[i-1]['close']
            atr_value = df.iloc[i]['atr']
            
            # 计算跳空
            gap_size = current_open - prev_close
            gap_percentage = abs(gap_size) / prev_close
            
            # 使用ATR或固定阈值的较大者
            dynamic_threshold = max(self.gap_threshold, atr_value / prev_close * 0.2)
            
            if gap_percentage > dynamic_threshold:
                gap_event = GapEvent(
                    timestamp=df.index[i],
                    price_before=prev_close,
                    price_after=current_open,
                    gap_size=gap_size,
                    gap_percentage=gap_percentage,
                    direction='up' if gap_size > 0 else 'down'
                )
                gaps.append(gap_event)
                self.gap_history.append(gap_event)
        
        return gaps
    
    def calculate_gap_risk_score(self, current_volatility: float,
                               volume_ratio: float,
                               time_of_day: int) -> float:
        """计算Gap风险评分"""
        
        risk_score = 0.0
        
        # 1. 波动率风险 (40%权重)
        volatility_risk = min(current_volatility / 0.05, 1.0) * 0.4
        risk_score += volatility_risk
        
        # 2. 流动性风险 (30%权重)
        if volume_ratio < 0.5:  # 低流动性
            liquidity_risk = 0.3
        elif volume_ratio < 0.8:
            liquidity_risk = 0.15
        else:
            liquidity_risk = 0.0
        risk_score += liquidity_risk
        
        # 3. 时间段风险 (30%权重)
        # 北京时间敏感时段: 21:30(美开), 2:00(美收), 9:30(中开)
        sensitive_hours = [21, 22, 1, 2, 9, 10]
        if time_of_day in sensitive_hours:
            time_risk = 0.2
        else:
            time_risk = 0.05
        risk_score += time_risk
        
        return min(risk_score, 1.0)


class LeverageController:
    """杠杆控制器"""
    
    def __init__(self, max_leverage: float = 1.5):
        self.max_leverage = max_leverage
        self.current_leverage = 1.0
        self.liquidation_history = []
        self.consecutive_losses = 0
        self.profit_high_watermark = 0.0
        
    def calculate_dynamic_leverage(self, 
                                 gap_risk_score: float,
                                 current_drawdown: float,
                                 profit_ratio: float,
                                 consecutive_losses: int) -> float:
        """计算动态杠杆倍数"""
        
        base_leverage = self.max_leverage
        
        # 1. Gap风险调整 (最重要)
        gap_adjustment = 1.0 - gap_risk_score * 0.6
        
        # 2. 回撤保护
        if current_drawdown < -0.10:  # 回撤超过10%
            drawdown_adjustment = 0.5
        elif current_drawdown < -0.05:  # 回撤超过5%
            drawdown_adjustment = 0.7
        else:
            drawdown_adjustment = 1.0
        
        # 3. 连续亏损保护
        if consecutive_losses >= 5:
            loss_adjustment = 0.3
        elif consecutive_losses >= 3:
            loss_adjustment = 0.6
        else:
            loss_adjustment = 1.0
        
        # 4. 利润保护 (利润超过15%时降低杠杆)
        if profit_ratio > 0.15:
            profit_adjustment = 0.8
        elif profit_ratio > 0.10:
            profit_adjustment = 0.9
        else:
            profit_adjustment = 1.0
        
        # 综合调整
        final_leverage = (base_leverage * 
                         gap_adjustment * 
                         drawdown_adjustment * 
                         loss_adjustment * 
                         profit_adjustment)
        
        # 最小杠杆1.0，最大不超过设定值
        return max(min(final_leverage, self.max_leverage), 1.0)
    
    def check_liquidation_risk(self, 
                             current_price: float,
                             entry_price: float,
                             leverage: float,
                             position_size: float,
                             account_equity: float) -> Tuple[bool, float]:
        """检查强制平仓风险"""
        
        if leverage <= 1.0:
            return False, 0.0
        
        # 计算当前盈亏
        pnl = (current_price - entry_price) / entry_price * position_size
        
        # 计算保证金
        margin_required = position_size / leverage
        
        # 计算净值
        net_equity = account_equity + pnl
        
        # 强制平仓线: 保证金不足 (保证金维持率 < 1.2)
        liquidation_threshold = margin_required * 1.2
        
        if net_equity < liquidation_threshold:
            # 计算强制平仓损失
            liquidation_loss = account_equity - liquidation_threshold
            return True, liquidation_loss
        
        return False, 0.0
    
    def simulate_gap_impact(self, 
                          gap_size: float,
                          leverage: float,
                          position_size: float) -> float:
        """模拟Gap对仓位的影响"""
        
        # Gap导致的直接损失
        gap_loss = gap_size * position_size * leverage
        
        return gap_loss


class RiskProtectionSystem:
    """综合风险保护系统"""
    
    def __init__(self, max_leverage: float = 1.5, 
                 max_drawdown: float = 0.25):
        self.gap_detector = GapRiskDetector()
        self.leverage_controller = LeverageController(max_leverage)
        self.max_drawdown = max_drawdown
        self.risk_events = []
        
        # 熔断参数
        self.daily_loss_limit = 0.05      # 单日亏损5%限制
        self.consecutive_loss_limit = 5    # 连续亏损5次限制
        self.gap_loss_limit = 0.03        # Gap损失3%限制
        
    def assess_trading_risk(self, 
                          current_data: pd.Series,
                          portfolio_state: Dict) -> Dict:
        """综合风险评估"""
        
        # 1. Gap风险评估
        volatility = current_data.get('volatility_20', 0.02)
        volume_ratio = current_data.get('volume_ratio_20', 1.0)
        time_hour = current_data.name.hour
        
        gap_risk_score = self.gap_detector.calculate_gap_risk_score(
            volatility, volume_ratio, time_hour
        )
        
        # 2. 动态杠杆计算
        current_drawdown = portfolio_state.get('current_drawdown', 0.0)
        profit_ratio = portfolio_state.get('profit_ratio', 0.0)
        consecutive_losses = portfolio_state.get('consecutive_losses', 0)
        
        recommended_leverage = self.leverage_controller.calculate_dynamic_leverage(
            gap_risk_score, current_drawdown, profit_ratio, consecutive_losses
        )
        
        # 3. 风险等级判定
        if gap_risk_score >= 0.8 or current_drawdown < -0.20:
            risk_level = RiskLevel.CRITICAL
        elif gap_risk_score >= 0.6 or current_drawdown < -0.15:
            risk_level = RiskLevel.HIGH
        elif gap_risk_score >= 0.4 or current_drawdown < -0.10:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW
        
        return {
            'gap_risk_score': gap_risk_score,
            'recommended_leverage': recommended_leverage,
            'risk_level': risk_level,
            'allow_trading': self._check_trading_permission(portfolio_state, risk_level),
            'risk_warnings': self._generate_risk_warnings(gap_risk_score, current_drawdown)
        }
    
    def _check_trading_permission(self, portfolio_state: Dict, 
                                risk_level: RiskLevel) -> bool:
        """检查是否允许交易"""
        
        # 1. 严重回撤限制
        if portfolio_state.get('current_drawdown', 0) < -self.max_drawdown:
            return False
        
        # 2. 单日亏损限制
        if portfolio_state.get('daily_loss', 0) < -self.daily_loss_limit:
            return False
        
        # 3. 连续亏损限制
        if portfolio_state.get('consecutive_losses', 0) >= self.consecutive_loss_limit:
            return False
        
        # 4. 极高风险暂停
        if risk_level == RiskLevel.CRITICAL:
            return False
        
        return True
    
    def _generate_risk_warnings(self, gap_risk_score: float, 
                              current_drawdown: float) -> List[str]:
        """生成风险预警"""
        warnings = []
        
        if gap_risk_score > 0.7:
            warnings.append(f"🚨 Gap风险极高 ({gap_risk_score:.1%})")
        elif gap_risk_score > 0.5:
            warnings.append(f"[WARN] Gap风险较高 ({gap_risk_score:.1%})")
        
        if current_drawdown < -0.15:
            warnings.append(f"🚨 回撤严重 ({current_drawdown:.1%})")
        elif current_drawdown < -0.10:
            warnings.append(f"[WARN] 回撤偏高 ({current_drawdown:.1%})")
        
        return warnings
    
    def handle_gap_event(self, gap_event: GapEvent, 
                        portfolio_state: Dict) -> Dict:
        """处理Gap事件"""
        
        actions_taken = []
        
        # 1. 评估Gap影响
        current_leverage = portfolio_state.get('leverage', 1.0)
        position_size = portfolio_state.get('position_size', 0.0)
        
        gap_impact = self.leverage_controller.simulate_gap_impact(
            gap_event.gap_size, current_leverage, position_size
        )
        
        # 2. 强制平仓检查
        should_liquidate, liquidation_loss = self.leverage_controller.check_liquidation_risk(
            gap_event.price_after,
            portfolio_state.get('entry_price', gap_event.price_before),
            current_leverage,
            position_size,
            portfolio_state.get('equity', 100000)
        )
        
        # 3. 执行保护措施
        if should_liquidate:
            actions_taken.append("强制平仓")
            liquidation_event = LiquidationEvent(
                timestamp=gap_event.timestamp,
                trigger_reason="Gap导致保证金不足",
                position_size=position_size,
                liquidation_price=gap_event.price_after,
                loss_amount=liquidation_loss,
                leverage_used=current_leverage
            )
            self.leverage_controller.liquidation_history.append(liquidation_event)
        
        elif abs(gap_impact) > portfolio_state.get('equity', 100000) * self.gap_loss_limit:
            actions_taken.append("紧急减仓")
        
        return {
            'gap_impact': gap_impact,
            'should_liquidate': should_liquidate,
            'liquidation_loss': liquidation_loss if should_liquidate else 0,
            'actions_taken': actions_taken
        }


# 测试和示例代码
if __name__ == "__main__":
    print("[SHIELD] Gap风险保护系统测试")
    
    # 创建保护系统
    protection_system = RiskProtectionSystem(max_leverage=1.5)
    
    # 模拟测试数据
    test_data = pd.Series({
        'volatility_20': 0.03,
        'volume_ratio_20': 0.8,
    }, name=pd.Timestamp('2024-01-01 21:30:00'))  # 敏感时段
    
    portfolio_state = {
        'current_drawdown': -0.08,
        'profit_ratio': 0.02,
        'consecutive_losses': 2,
        'leverage': 1.5,
        'position_size': 50000,
        'equity': 100000,
        'entry_price': 50000
    }
    
    # 风险评估
    risk_assessment = protection_system.assess_trading_risk(test_data, portfolio_state)
    
    print(f"[CHART] 风险评估结果:")
    print(f"   Gap风险评分: {risk_assessment['gap_risk_score']:.1%}")
    print(f"   推荐杠杆: {risk_assessment['recommended_leverage']:.2f}x")
    print(f"   风险等级: {risk_assessment['risk_level'].value}")
    print(f"   允许交易: {'[OK]' if risk_assessment['allow_trading'] else '🚫'}")
    
    if risk_assessment['risk_warnings']:
        print(f"   风险预警: {', '.join(risk_assessment['risk_warnings'])}")
    
    # 模拟Gap事件
    gap_event = GapEvent(
        timestamp=pd.Timestamp('2024-01-01 21:35:00'),
        price_before=50000,
        price_after=49500,
        gap_size=-500,
        gap_percentage=0.01,
        direction='down'
    )
    
    gap_response = protection_system.handle_gap_event(gap_event, portfolio_state)
    print(f"\n[FIRE] Gap事件处理:")
    print(f"   Gap影响: ${gap_response['gap_impact']:,.2f}")
    print(f"   强制平仓: {'是' if gap_response['should_liquidate'] else '否'}")
    if gap_response['actions_taken']:
        print(f"   采取措施: {', '.join(gap_response['actions_taken'])}")
    
    print("\n[OK] Gap风险保护系统已就绪!")
    print("[WARN] 下一步: 集成到主交易系统") 