const getGridDiagnostics = () => {
  if (!(window as any).strategyExecutor) {
    return {
      status: '策略执行器未初始化',
      details: [] as string[],
      suggestions: ['请先启动交易']
    }
  }

  const executor = (window as any).strategyExecutor
  const status = executor.getStrategyStatus()
  
  // 模拟获取内部状态（实际需要从执行器暴露这些信息）
  const diagnostics = {
    status: status.isRunning ? '策略运行中' : '策略未运行',
    details: [
      `当前交易次数: ${status.totalTrades}`,
      `市场状态: ${status.marketState}`,
      `风险等级: ${status.riskLevel}`,
      `最后信号时间: ${status.lastSignalTime ? new Date(status.lastSignalTime).toLocaleTimeString() : '无'}`,
    ] as string[],
    suggestions: [] as string[]
  }

  // 基于状态添加建议
  if (!status.isRunning) {
    diagnostics.suggestions.push('策略未启动，请点击"🚀 启动交易"')
  } else if (status.totalTrades === 0) {
    diagnostics.suggestions.push('策略已启动但无交易记录，可能：')
    diagnostics.suggestions.push('• 价格波动不够大，未达到网格触发条件')
    diagnostics.suggestions.push('• 风险控制阻止了交易执行')
    diagnostics.suggestions.push('• 账户余额不足或仓位已满')
  }

  if (status.riskLevel === 'CRITICAL') {
    diagnostics.suggestions.push('⚠️ 风险等级为CRITICAL，可能触发了熔断机制')
  }

  return diagnostics
} 