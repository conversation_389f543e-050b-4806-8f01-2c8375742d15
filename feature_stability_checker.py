#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征稳定性验证器 - 解决市场regime变换下的指标失效问题
针对20条指标→市场环境变化导致的稳定性问题
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import mutual_info_classif, RFE
from sklearn.model_selection import cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

class FeatureStabilityValidator:
    """特征稳定性验证器"""
    
    def __init__(self, lookback_months=3, min_samples_per_regime=100):
        self.lookback_months = lookback_months
        self.min_samples_per_regime = min_samples_per_regime
        self.regime_models = {}
        self.feature_importance_history = {}
        
    def identify_market_regimes(self, df, price_col='close'):
        """识别市场状态 - 基于趋势和波动率"""
        
        print("🔍 识别市场regime...")
        
        # 计算趋势指标
        ma20 = df[price_col].rolling(20).mean()
        ma20_slope = ma20.pct_change(5)  # 5期斜率
        
        # 计算波动率指标
        returns = df[price_col].pct_change()
        volatility = returns.rolling(20).std()
        vol_percentile = volatility.rolling(100).rank(pct=True)
        
        # 计算价格相对位置
        high_52w = df[price_col].rolling(252*24).max()  # 52周最高(15分钟数据)
        low_52w = df[price_col].rolling(252*24).min()   # 52周最低
        price_position = (df[price_col] - low_52w) / (high_52w - low_52w)
        
        # Regime分类逻辑
        regimes = pd.Series('undefined', index=df.index)
        
        # 牛市: 强上升趋势 + 低/中波动率 + 高价格位置
        bull_mask = (
            (ma20_slope > 0.02) & 
            (vol_percentile < 0.7) & 
            (price_position > 0.6)
        )
        
        # 熊市: 强下降趋势 + 低/中波动率 + 低价格位置  
        bear_mask = (
            (ma20_slope < -0.02) & 
            (vol_percentile < 0.7) & 
            (price_position < 0.4)
        )
        
        # 震荡市: 弱趋势 + 低波动率
        sideways_mask = (
            (abs(ma20_slope) <= 0.02) & 
            (vol_percentile < 0.5)
        )
        
        # 极端市: 高波动率 (不论趋势)
        volatile_mask = (vol_percentile >= 0.8)
        
        # 恐慌市: 快速下跌 + 极高波动率
        panic_mask = (
            (ma20_slope < -0.05) & 
            (vol_percentile >= 0.9)
        )
        
        # 按优先级分配regime
        regimes[sideways_mask] = 'sideways'
        regimes[bull_mask] = 'bull'
        regimes[bear_mask] = 'bear'
        regimes[volatile_mask] = 'volatile'
        regimes[panic_mask] = 'panic'
        
        # 统计各regime样本数
        regime_counts = regimes.value_counts()
        print("📊 Market Regime分布:")
        for regime, count in regime_counts.items():
            percentage = count / len(regimes) * 100
            print(f"   {regime}: {count:,}样本 ({percentage:.1f}%)")
        
        return regimes
    
    def calculate_feature_importance(self, X, y, method='random_forest'):
        """计算特征重要性"""
        
        if len(X) < self.min_samples_per_regime:
            return {}
        
        # 处理缺失值
        X_filled = X.fillna(X.median())
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_filled)
        X_scaled = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)
        
        importance_dict = {}
        
        try:
            if method == 'random_forest':
                # Windows兼容性: n_jobs=1
                rf = RandomForestClassifier(
                    n_estimators=100, 
                    random_state=42, 
                    n_jobs=1,
                    max_depth=10
                )
                rf.fit(X_scaled, y)
                
                for feature, importance in zip(X.columns, rf.feature_importances_):
                    importance_dict[feature] = importance
                    
            elif method == 'mutual_info':
                # Windows兼容性修复
                mi_scores = mutual_info_classif(
                    X_scaled, y, 
                    random_state=42, 
                    n_jobs=1
                )
                
                for feature, score in zip(X.columns, mi_scores):
                    importance_dict[feature] = score
        
        except Exception as e:
            print(f"⚠️ 特征重要性计算失败 ({method}): {e}")
            # Fallback: 使用相关性
            for feature in X.columns:
                try:
                    corr = abs(np.corrcoef(X_filled[feature], y)[0, 1])
                    importance_dict[feature] = corr if not np.isnan(corr) else 0
                except:
                    importance_dict[feature] = 0
        
        return importance_dict
    
    def validate_regime_stability(self, df, features, target_col='target'):
        """验证不同市场环境下的特征稳定性"""
        
        print("🎯 开始特征稳定性验证...")
        
        # 1. 识别市场regime
        regimes = self.identify_market_regimes(df)
        
        # 2. 为每个regime计算特征重要性
        regime_importance = {}
        valid_regimes = []
        
        for regime_name in ['bull', 'bear', 'sideways', 'volatile', 'panic']:
            regime_mask = (regimes == regime_name)
            regime_data = df[regime_mask]
            
            if len(regime_data) >= self.min_samples_per_regime:
                print(f"📈 分析{regime_name}市场特征重要性...")
                
                X_regime = regime_data[features]
                y_regime = regime_data[target_col] if target_col in regime_data.columns else (regime_data['close'].pct_change().shift(-1) > 0).astype(int)
                
                # 计算RF重要性
                rf_importance = self.calculate_feature_importance(
                    X_regime, y_regime, 'random_forest'
                )
                
                # 计算互信息重要性
                mi_importance = self.calculate_feature_importance(
                    X_regime, y_regime, 'mutual_info'
                )
                
                # 综合重要性 (RF权重60%, MI权重40%)
                combined_importance = {}
                for feature in features:
                    rf_score = rf_importance.get(feature, 0)
                    mi_score = mi_importance.get(feature, 0)
                    combined_importance[feature] = 0.6 * rf_score + 0.4 * mi_score
                
                regime_importance[regime_name] = combined_importance
                valid_regimes.append(regime_name)
                
                print(f"   ✅ {regime_name}: {len(regime_data):,}样本, Top3特征:")
                top_features = sorted(combined_importance.items(), key=lambda x: x[1], reverse=True)[:3]
                for feat, score in top_features:
                    print(f"      {feat}: {score:.3f}")
            else:
                print(f"   ⚠️ {regime_name}: 样本不足({len(regime_data)})")
        
        # 3. 计算特征稳定性得分
        print("📊 计算特征稳定性得分...")
        stability_scores = {}
        
        for feature in features:
            regime_scores = []
            for regime in valid_regimes:
                score = regime_importance[regime].get(feature, 0)
                regime_scores.append(score)
            
            if len(regime_scores) >= 2:
                # 稳定性 = 1 / (1 + 方差) * 平均重要性
                mean_importance = np.mean(regime_scores)
                variance = np.var(regime_scores)
                stability_scores[feature] = mean_importance / (1 + variance)
            else:
                stability_scores[feature] = 0
        
        # 4. 选择最稳定的85%特征
        sorted_features = sorted(stability_scores.items(), key=lambda x: x[1], reverse=True)
        keep_count = max(1, int(len(features) * 0.85))  # 至少保留1个
        stable_features = [f[0] for f in sorted_features[:keep_count]]
        
        print(f"🎯 特征筛选结果: {len(features)} → {len(stable_features)} (保留{len(stable_features)/len(features)*100:.1f}%)")
        print("🏆 最稳定的Top10特征:")
        for i, (feature, score) in enumerate(sorted_features[:10]):
            status = "✅保留" if feature in stable_features else "❌删除"
            print(f"   {i+1:2d}. {feature:20s}: {score:.4f} {status}")
        
        # 5. 生成稳定性报告
        stability_report = self.generate_stability_report(
            regime_importance, stability_scores, stable_features, valid_regimes
        )
        
        return stable_features, stability_scores, stability_report
    
    def generate_stability_report(self, regime_importance, stability_scores, stable_features, valid_regimes):
        """生成详细稳定性报告"""
        
        report = {
            'summary': {
                'total_features': len(stability_scores),
                'stable_features': len(stable_features),
                'reduction_rate': 1 - len(stable_features) / len(stability_scores),
                'valid_regimes': valid_regimes,
                'avg_stability_score': np.mean(list(stability_scores.values()))
            },
            'regime_analysis': regime_importance,
            'feature_rankings': sorted(stability_scores.items(), key=lambda x: x[1], reverse=True),
            'dropped_features': [f for f in stability_scores.keys() if f not in stable_features],
            'recommendations': self.generate_recommendations(stability_scores, regime_importance)
        }
        
        return report
    
    def generate_recommendations(self, stability_scores, regime_importance):
        """生成改进建议"""
        
        recommendations = []
        
        # 1. 分析整体稳定性
        avg_stability = np.mean(list(stability_scores.values()))
        if avg_stability < 0.1:
            recommendations.append({
                'level': 'HIGH',
                'message': f'整体特征稳定性偏低({avg_stability:.3f})，建议增加更多历史数据或重新设计特征'
            })
        
        # 2. 分析特征分化程度
        stability_std = np.std(list(stability_scores.values()))
        if stability_std > 0.05:
            recommendations.append({
                'level': 'MEDIUM',
                'message': f'特征稳定性差异较大(std={stability_std:.3f})，建议重点关注高稳定性特征'
            })
        
        # 3. 分析regime覆盖度
        if len(regime_importance) < 3:
            recommendations.append({
                'level': 'HIGH',
                'message': '市场regime覆盖不足，建议扩展历史数据包含更多市场环境'
            })
        
        # 4. 识别问题特征
        low_stability_features = [f for f, s in stability_scores.items() if s < 0.01]
        if len(low_stability_features) > len(stability_scores) * 0.3:
            recommendations.append({
                'level': 'MEDIUM',
                'message': f'发现{len(low_stability_features)}个极低稳定性特征，建议检查计算逻辑'
            })
        
        return recommendations
    
    def cross_regime_validation(self, df, stable_features, target_col='target'):
        """跨regime交叉验证"""
        
        print("🔬 开始跨regime交叉验证...")
        
        regimes = self.identify_market_regimes(df)
        validation_results = {}
        
        for train_regime in ['bull', 'bear', 'sideways']:
            for test_regime in ['bull', 'bear', 'sideways']:
                if train_regime == test_regime:
                    continue
                
                # 训练数据
                train_mask = (regimes == train_regime)
                train_data = df[train_mask]
                
                # 测试数据
                test_mask = (regimes == test_regime)
                test_data = df[test_mask]
                
                if len(train_data) >= 100 and len(test_data) >= 50:
                    # 准备特征和目标
                    X_train = train_data[stable_features].fillna(train_data[stable_features].median())
                    y_train = train_data[target_col] if target_col in train_data.columns else (train_data['close'].pct_change().shift(-1) > 0).astype(int)
                    
                    X_test = test_data[stable_features].fillna(test_data[stable_features].median())
                    y_test = test_data[target_col] if target_col in test_data.columns else (test_data['close'].pct_change().shift(-1) > 0).astype(int)
                    
                    # 训练模型
                    try:
                        scaler = StandardScaler()
                        X_train_scaled = scaler.fit_transform(X_train)
                        X_test_scaled = scaler.transform(X_test)
                        
                        rf = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=1)
                        rf.fit(X_train_scaled, y_train)
                        
                        # 预测和评估
                        y_pred = rf.predict(X_test_scaled)
                        accuracy = accuracy_score(y_test, y_pred)
                        
                        validation_results[f"{train_regime}→{test_regime}"] = {
                            'accuracy': accuracy,
                            'train_samples': len(train_data),
                            'test_samples': len(test_data)
                        }
                        
                        print(f"   {train_regime}→{test_regime}: {accuracy:.3f} (train:{len(train_data)}, test:{len(test_data)})")
                        
                    except Exception as e:
                        print(f"   ❌ {train_regime}→{test_regime}: 验证失败 - {e}")
        
        # 计算平均跨regime性能
        if validation_results:
            avg_accuracy = np.mean([r['accuracy'] for r in validation_results.values()])
            print(f"📊 平均跨regime准确率: {avg_accuracy:.3f}")
            
            if avg_accuracy > 0.55:
                print("✅ 特征具有良好的跨regime泛化能力")
            elif avg_accuracy > 0.52:
                print("⚠️ 特征跨regime性能一般，建议进一步优化")
            else:
                print("❌ 特征跨regime性能较差，需要重新设计")
        
        return validation_results

def main():
    """主函数 - 演示特征稳定性验证"""
    
    print("🎯 特征稳定性验证器 - Demo")
    print("=" * 50)
    
    # 加载数据 (这里使用模拟数据)
    print("📊 加载历史数据...")
    
    # 模拟数据生成
    np.random.seed(42)
    periods = 10000
    
    # 生成基础价格数据
    price_changes = np.random.normal(0, 0.02, periods)
    prices = [45000]
    for change in price_changes:
        prices.append(prices[-1] * (1 + change))
    
    # 创建DataFrame
    df = pd.DataFrame({
        'close': prices[:-1],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices[:-1]],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices[:-1]],
        'volume': np.random.uniform(1000000, 5000000, periods)
    })
    
    # 添加技术指标作为特征
    print("🔧 计算技术指标...")
    
    # 移动平均
    for period in [5, 10, 20, 50]:
        df[f'ma_{period}'] = df['close'].rolling(period).mean()
        df[f'ma_ratio_{period}'] = df['close'] / df[f'ma_{period}']
    
    # RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # MACD
    ema12 = df['close'].ewm(span=12).mean()
    ema26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema12 - ema26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    
    # 波动率指标
    df['volatility'] = df['close'].pct_change().rolling(20).std()
    df['atr'] = (df['high'] - df['low']).rolling(14).mean()
    
    # 成交量指标
    df['volume_ma'] = df['volume'].rolling(20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_ma']
    
    # 价格位置
    df['price_position'] = (df['close'] - df['close'].rolling(100).min()) / (df['close'].rolling(100).max() - df['close'].rolling(100).min())
    
    # 布林带
    bb_period = 20
    bb_std = 2
    bb_ma = df['close'].rolling(bb_period).mean()
    bb_std_val = df['close'].rolling(bb_period).std()
    df['bb_upper'] = bb_ma + (bb_std_val * bb_std)
    df['bb_lower'] = bb_ma - (bb_std_val * bb_std)
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # 动量指标
    df['momentum'] = df['close'] / df['close'].shift(10)
    df['roc'] = df['close'].pct_change(5)
    
    # 特征列表
    features = [col for col in df.columns if col not in ['close', 'high', 'low', 'volume', 'ma_5', 'ma_10', 'ma_20', 'ma_50']]
    
    print(f"📈 原始特征数量: {len(features)}")
    print("特征列表:", features[:5], "...")
    
    # 移除前200行(指标计算需要)
    df_clean = df.iloc[200:].copy()
    
    # 创建目标变量 (未来收益方向)
    df_clean['target'] = (df_clean['close'].shift(-1) > df_clean['close']).astype(int)
    
    # 移除最后一行
    df_clean = df_clean.iloc[:-1]
    
    print(f"📊 清洗后数据: {len(df_clean):,}行")
    
    # 初始化验证器
    validator = FeatureStabilityValidator(lookback_months=3, min_samples_per_regime=200)
    
    # 执行稳定性验证
    stable_features, stability_scores, report = validator.validate_regime_stability(
        df_clean, features, 'target'
    )
    
    print("\n" + "=" * 50)
    print("📋 稳定性验证结果:")
    print("=" * 50)
    
    print(f"✅ 保留特征: {len(stable_features)}/{len(features)} ({len(stable_features)/len(features)*100:.1f}%)")
    print(f"📊 平均稳定性得分: {report['summary']['avg_stability_score']:.4f}")
    print(f"📈 有效市场regime: {len(report['summary']['valid_regimes'])}")
    
    # 显示建议
    print("\n🎯 改进建议:")
    for rec in report['recommendations']:
        level_icon = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢"}
        print(f"   {level_icon[rec['level']]} {rec['message']}")
    
    # 跨regime验证
    if len(stable_features) > 0:
        print("\n🔬 执行跨regime验证...")
        cross_results = validator.cross_regime_validation(df_clean, stable_features, 'target')
        
        if cross_results:
            print(f"📊 跨regime验证完成，共{len(cross_results)}个测试组合")
    
    print("\n✨ 特征稳定性验证完成!")
    print(f"💡 建议使用的{len(stable_features)}个稳定特征进行后续策略开发")

if __name__ == "__main__":
    main() 