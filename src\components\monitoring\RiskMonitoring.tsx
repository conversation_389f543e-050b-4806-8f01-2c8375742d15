import { useState } from 'react'

interface RiskMetrics {
  leverage: number
  margin: {
    total: number
    used: number
    free: number
    ratio: number
  }
  exposure: {
    totalLong: number
    totalShort: number
    netExposure: number
  }
  drawdown: {
    current: number
    max: number
    duration: number
  }
  var: {
    daily: number
    weekly: number
  }
  marginCall: {
    isWarning: boolean
    threshold: number
    current: number
  }
}

interface RiskAlert {
  level: 'info' | 'warning' | 'danger'
  message: string
  timestamp: number
}

export function RiskMonitoring() {
  const [riskMetrics] = useState<RiskMetrics>({
    leverage: 0,
    margin: {
      total: 0,
      used: 0,
      free: 0,
      ratio: 0
    },
    exposure: {
      totalLong: 0,
      totalShort: 0,
      netExposure: 0
    },
    drawdown: {
      current: 0,
      max: 0,
      duration: 0
    },
    var: {
      daily: 0,
      weekly: 0
    },
    marginCall: {
      isWarning: false,
      threshold: 80,
      current: 0
    }
  })

  const [alerts] = useState<RiskAlert[]>([])

  // 已移除模拟数据更新逻辑，数据现在为初始静态状态

  const getMarginColor = (ratio: number) => {
    if (ratio < 50) return 'text-green-400 bg-green-400/10'
    if (ratio < 70) return 'text-yellow-400 bg-yellow-400/10'
    return 'text-red-400 bg-red-400/10'
  }

  const getLeverageColor = (leverage: number) => {
    if (leverage < 2) return 'text-green-400'
    if (leverage < 3) return 'text-yellow-400'
    return 'text-red-400'
  }

  return (
    <div className="space-y-6">
      {/* 风险警报面板 */}
      {alerts.length > 0 && (
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            🚨 风险警报
          </h3>
          
          <div className="space-y-3">
            {alerts.map((alert, index) => (
              <div 
                key={index}
                className={`p-3 rounded-lg border-l-4 ${
                  alert.level === 'danger' ? 'bg-red-500/10 border-red-500 text-red-400' :
                  alert.level === 'warning' ? 'bg-yellow-500/10 border-yellow-500 text-yellow-400' :
                  'bg-blue-500/10 border-blue-500 text-blue-400'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm">{alert.message}</span>
                  <span className="text-xs opacity-70">
                    {new Date(alert.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 核心风险指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 杠杆率 */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-slate-400">杠杆率</h3>
            <div className={`px-2 py-1 rounded text-xs font-bold ${getLeverageColor(riskMetrics.leverage)}`}>
              {riskMetrics.leverage > 3 ? '高风险' : riskMetrics.leverage > 2 ? '中风险' : '低风险'}
            </div>
          </div>
          
          <div className={`text-3xl font-bold mb-2 ${getLeverageColor(riskMetrics.leverage)}`}>
            {riskMetrics.leverage.toFixed(2)}x
          </div>
          
          <div className="text-xs text-slate-400">
            最大杠杆: 5x
          </div>
        </div>

        {/* 保证金使用率 */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-slate-400">保证金使用率</h3>
            <div className={`px-2 py-1 rounded text-xs font-bold ${getMarginColor(riskMetrics.margin.ratio)}`}>
              {riskMetrics.margin.ratio > 70 ? '警戒' : riskMetrics.margin.ratio > 50 ? '注意' : '安全'}
            </div>
          </div>
          
          <div className="text-3xl font-bold text-white mb-2">
            {riskMetrics.margin.ratio.toFixed(1)}%
          </div>
          
          {/* 保证金状态条 */}
          <div className="w-full bg-slate-600 rounded-full h-2 mb-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                riskMetrics.margin.ratio < 50 ? 'bg-green-400' : 
                riskMetrics.margin.ratio < 70 ? 'bg-yellow-400' : 'bg-red-400'
              }`}
              style={{ width: `${Math.min(riskMetrics.margin.ratio, 100)}%` }}
            ></div>
          </div>
          
          <div className="text-xs text-slate-400">
            已用: ${riskMetrics.margin.used.toFixed(0)} / 总计: ${riskMetrics.margin.total.toFixed(0)}
          </div>
        </div>

        {/* 净敞口 */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <h3 className="text-sm font-medium text-slate-400 mb-4">净敞口</h3>
          
          <div className={`text-3xl font-bold mb-2 ${
            riskMetrics.exposure.netExposure >= 0 ? 'text-green-400' : 'text-red-400'
          }`}>
            ${Math.abs(riskMetrics.exposure.netExposure).toFixed(0)}
          </div>
          
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span className="text-slate-400">多头:</span>
              <span className="text-green-400">${riskMetrics.exposure.totalLong.toFixed(0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">空头:</span>
              <span className="text-red-400">${riskMetrics.exposure.totalShort.toFixed(0)}</span>
            </div>
          </div>
        </div>

        {/* 当前回撤 */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <h3 className="text-sm font-medium text-slate-400 mb-4">当前回撤</h3>
          
          <div className="text-3xl font-bold text-red-400 mb-2">
            {riskMetrics.drawdown.current.toFixed(2)}%
          </div>
          
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span className="text-slate-400">最大回撤:</span>
              <span className="text-red-400">{riskMetrics.drawdown.max.toFixed(2)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">持续天数:</span>
              <span className="text-white">{riskMetrics.drawdown.duration}</span>
            </div>
          </div>
        </div>
      </div>

      {/* VaR风险值 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            📊 风险价值 (VaR)
          </h3>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-slate-400 text-sm mb-1">日VaR (95%)</div>
              <div className="text-xl font-bold text-red-400">
                ${riskMetrics.var.daily.toFixed(0)}
              </div>
            </div>
            
            <div className="bg-slate-700/50 rounded-lg p-4">
              <div className="text-slate-400 text-sm mb-1">周VaR (95%)</div>
              <div className="text-xl font-bold text-red-400">
                ${riskMetrics.var.weekly.toFixed(0)}
              </div>
            </div>
          </div>
          
          <div className="mt-4 text-xs text-slate-400">
            95%置信度下，预期最大损失金额
          </div>
        </div>

        {/* 风险控制措施 */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            🛡️ 风险控制
          </h3>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
              <span className="text-sm text-slate-300">止损订单</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-xs text-green-400">启用</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
              <span className="text-sm text-slate-300">仓位限制</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-xs text-green-400">启用</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
              <span className="text-sm text-slate-300">熔断机制</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <span className="text-xs text-yellow-400">监控中</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 