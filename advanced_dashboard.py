#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级监控仪表盘 v2.0 - 实时数据监控
支持WebSocket实时数据流、Redis缓存、多维度图表、Telegram告警
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import redis
import json
import requests
import asyncio
import websockets
import threading
import time
from datetime import datetime, timedelta
import logging

# 页面配置
st.set_page_config(
    page_title="🎯 高级策略监控仪表盘",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

class TelegramAlerter:
    """Telegram告警系统"""
    
    def __init__(self, bot_token="", chat_id=""):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.enabled = bool(bot_token and chat_id)
    
    def send_alert(self, message, level="INFO"):
        """发送告警消息"""
        if not self.enabled:
            return False
        
        icons = {"INFO": "ℹ️", "WARNING": "⚠️", "ERROR": "🚨", "SUCCESS": "✅"}
        icon = icons.get(level, "📢")
        
        formatted_message = f"{icon} <b>策略监控告警</b>\n\n{message}\n\n🕐 {datetime.now().strftime('%H:%M:%S')}"
        
        url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
        data = {
            'chat_id': self.chat_id,
            'text': formatted_message,
            'parse_mode': 'HTML'
        }
        
        try:
            response = requests.post(url, data=data, timeout=5)
            return response.status_code == 200
        except Exception as e:
            logging.error(f"Telegram发送失败: {e}")
            return False

class RealTimeDataManager:
    """实时数据管理器"""
    
    def __init__(self):
        self.redis_client = None
        self.connect_redis()
        
    def connect_redis(self):
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(
                host='localhost', 
                port=6379, 
                db=0,
                decode_responses=True
            )
            # 测试连接
            self.redis_client.ping()
            return True
        except Exception as e:
            st.warning(f"Redis连接失败: {e}")
            return False
    
    def push_trading_data(self, data):
        """推送交易数据"""
        if self.redis_client:
            try:
                self.redis_client.lpush('trading_stream', json.dumps(data))
                # 保持最新1000条记录
                self.redis_client.ltrim('trading_stream', 0, 999)
                return True
            except Exception as e:
                logging.error(f"数据推送失败: {e}")
        return False
    
    def get_latest_data(self, count=100):
        """获取最新数据"""
        if self.redis_client:
            try:
                data_list = self.redis_client.lrange('trading_stream', 0, count-1)
                return [json.loads(item) for item in data_list]
            except Exception as e:
                logging.error(f"数据获取失败: {e}")
        return []

class AdvancedDashboard:
    """高级监控仪表盘"""
    
    def __init__(self):
        self.data_manager = RealTimeDataManager()
        self.alerter = TelegramAlerter()
        self.last_alert_time = {}
        
    def generate_mock_data(self, periods=200):
        """生成模拟实时数据"""
        base_time = datetime.now() - timedelta(hours=periods/12)  # 5分钟一个点
        
        # 模拟价格走势
        btc_prices = []
        equity_values = []
        initial_price = 45000
        initial_equity = 10000
        
        for i in range(periods):
            # BTC价格随机游走
            if i == 0:
                btc_price = initial_price
                equity = initial_equity
            else:
                price_change = np.random.normal(0, 0.02)
                btc_price = btc_prices[-1] * (1 + price_change)
                
                # 策略收益 (略优于BTC)
                strategy_change = price_change * 1.1 + np.random.normal(0, 0.005)
                equity = equity_values[-1] * (1 + strategy_change)
            
            btc_prices.append(btc_price)
            equity_values.append(equity)
        
        # 创建时间序列
        timestamps = [base_time + timedelta(minutes=5*i) for i in range(periods)]
        
        # 生成交易记录
        trade_count = max(1, periods // 20)  # 平均每小时1笔交易
        trade_times = np.random.choice(range(periods), trade_count, replace=False)
        
        trades = []
        total_commission = 0
        total_slippage = 0
        total_funding = 0
        
        for i in trade_times:
            commission = np.random.uniform(2, 15)
            slippage = np.random.uniform(1, 8)
            funding = np.random.uniform(0.5, 3)
            
            total_commission += commission
            total_slippage += slippage
            total_funding += funding
            
            trades.append({
                'timestamp': timestamps[i],
                'symbol': 'BTCUSDT',
                'side': np.random.choice(['BUY', 'SELL']),
                'quantity': round(np.random.uniform(0.05, 0.3), 3),
                'price': btc_prices[i],
                'commission': commission,
                'slippage': slippage,
                'funding': funding
            })
        
        return {
            'timestamps': timestamps,
            'btc_prices': btc_prices,
            'equity_values': equity_values,
            'trades': trades,
            'cost_breakdown': {
                'commission': total_commission,
                'slippage': total_slippage, 
                'funding': total_funding
            }
        }
    
    def create_equity_benchmark_chart(self, timestamps, equity_values, btc_prices):
        """创建权益曲线与基准对比图"""
        
        # 标准化处理 (都从100开始)
        equity_normalized = 100 * np.array(equity_values) / equity_values[0]
        btc_normalized = 100 * np.array(btc_prices) / btc_prices[0]
        
        fig = make_subplots(specs=[[{"secondary_y": True}]])
        
        # 策略权益曲线
        fig.add_trace(
            go.Scatter(
                x=timestamps, 
                y=equity_normalized,
                mode='lines',
                name='策略权益',
                line=dict(color='#1f77b4', width=3),
                hovertemplate='策略: %{y:.2f}<br>时间: %{x}<extra></extra>'
            ),
            secondary_y=False,
        )
        
        # BTC基准
        fig.add_trace(
            go.Scatter(
                x=timestamps, 
                y=btc_normalized,
                mode='lines',
                name='BTC基准',
                line=dict(color='#ff7f0e', width=2, dash='dash'),
                hovertemplate='BTC: %{y:.2f}<br>时间: %{x}<extra></extra>'
            ),
            secondary_y=True,
        )
        
        # 设置Y轴标题
        fig.update_yaxes(title_text="策略权益 (标准化)", secondary_y=False)
        fig.update_yaxes(title_text="BTC价格 (标准化)", secondary_y=True)
        
        fig.update_layout(
            title="📈 策略表现 vs BTC基准",
            xaxis_title="时间",
            height=400,
            hovermode='x unified'
        )
        
        return fig
    
    def create_cost_breakdown_chart(self, cost_data):
        """创建费用分解堆叠图"""
        
        periods = ['今日', '本周', '本月']
        
        # 模拟不同时期的费用
        daily_costs = {
            'commission': cost_data['commission'] * 0.1,
            'slippage': cost_data['slippage'] * 0.1,
            'funding': cost_data['funding'] * 0.1
        }
        
        weekly_costs = {k: v * 7 for k, v in daily_costs.items()}
        monthly_costs = {k: v * 30 for k, v in daily_costs.items()}
        
        fig = go.Figure(data=[
            go.Bar(
                name='手续费',
                x=periods,
                y=[daily_costs['commission'], weekly_costs['commission'], monthly_costs['commission']],
                marker_color='#1f77b4'
            ),
            go.Bar(
                name='滑点成本',
                x=periods,
                y=[daily_costs['slippage'], weekly_costs['slippage'], monthly_costs['slippage']],
                marker_color='#ff7f0e'
            ),
            go.Bar(
                name='资金费率',
                x=periods,
                y=[daily_costs['funding'], weekly_costs['funding'], monthly_costs['funding']],
                marker_color='#2ca02c'
            )
        ])
        
        fig.update_layout(
            title="💰 交易成本分解",
            barmode='stack',
            xaxis_title="时间周期",
            yaxis_title="成本 ($)",
            height=350
        )
        
        return fig
    
    def create_var_chart(self, timestamps, equity_values):
        """创建VaR风险监控图"""
        
        # 计算收益率
        returns = pd.Series(equity_values).pct_change().dropna()
        
        # 滚动VaR计算
        window = 50
        var_values = []
        var_upper = []
        var_lower = []
        
        for i in range(len(returns)):
            if i < window:
                var_values.append(0)
                var_upper.append(0)
                var_lower.append(0)
            else:
                window_returns = returns.iloc[i-window:i]
                var_95 = np.percentile(window_returns, 5)
                var_99 = np.percentile(window_returns, 1)
                
                var_values.append(abs(var_95) * 100)  # 转为百分比
                var_upper.append(abs(var_99) * 100)
                var_lower.append(abs(np.percentile(window_returns, 10)) * 100)
        
        # 对齐时间戳
        var_timestamps = timestamps[1:]  # 去掉第一个(因为pct_change)
        
        fig = go.Figure()
        
        # VaR主线
        fig.add_trace(go.Scatter(
            x=var_timestamps,
            y=var_values,
            mode='lines',
            name='VaR(95%)',
            line=dict(color='red', width=2),
            hovertemplate='VaR: %{y:.2f}%<br>时间: %{x}<extra></extra>'
        ))
        
        # 置信区间
        fig.add_trace(go.Scatter(
            x=var_timestamps,
            y=var_upper,
            fill=None,
            mode='lines',
            line_color='rgba(0,0,0,0)',
            showlegend=False
        ))
        
        fig.add_trace(go.Scatter(
            x=var_timestamps,
            y=var_lower,
            fill='tonexty',
            mode='lines',
            line_color='rgba(0,0,0,0)',
            name='置信区间',
            fillcolor='rgba(255,0,0,0.1)'
        ))
        
        # 风险阈值线
        fig.add_hline(y=2.0, line_dash="dash", line_color="orange", 
                     annotation_text="风险预警线(2%)")
        
        fig.update_layout(
            title="🛡️ VaR风险监控",
            xaxis_title="时间",
            yaxis_title="VaR (%)",
            height=350
        )
        
        return fig
    
    def check_alert_conditions(self, current_data):
        """检查告警条件"""
        alerts = []
        
        if not current_data:
            return alerts
        
        current_time = datetime.now()
        
        # VaR超过2%
        if len(current_data['equity_values']) > 50:
            returns = pd.Series(current_data['equity_values']).pct_change().dropna()
            if len(returns) > 0:
                current_var = abs(np.percentile(returns.tail(50), 5)) * 100
                if current_var > 2.0:
                    alert_key = 'var_high'
                    if self.should_send_alert(alert_key, 300):  # 5分钟间隔
                        alerts.append({
                            'level': 'WARNING',
                            'message': f'VaR风险过高: {current_var:.2f}% (>2%阈值)'
                        })
        
        # 权益大幅下降
        if len(current_data['equity_values']) > 1:
            initial_equity = current_data['equity_values'][0]
            current_equity = current_data['equity_values'][-1]
            equity_ratio = current_equity / initial_equity
            
            if equity_ratio < 0.9:  # 低于90%初始资金
                alert_key = 'equity_low'
                if self.should_send_alert(alert_key, 600):  # 10分钟间隔
                    alerts.append({
                        'level': 'ERROR',
                        'message': f'资金告警: 当前{equity_ratio:.1%}初始资金 (${current_equity:.2f})'
                    })
        
        return alerts
    
    def should_send_alert(self, alert_key, interval_seconds):
        """检查是否应该发送告警(防止重复)"""
        current_time = time.time()
        last_time = self.last_alert_time.get(alert_key, 0)
        
        if current_time - last_time > interval_seconds:
            self.last_alert_time[alert_key] = current_time
            return True
        return False
    
    def run_dashboard(self):
        """运行仪表盘主程序"""
        
        # 侧边栏配置
        st.sidebar.title("⚙️ 监控配置")
        
        # Telegram配置
        with st.sidebar.expander("📱 Telegram告警"):
            bot_token = st.text_input("Bot Token", type="password")
            chat_id = st.text_input("Chat ID")
            
            if bot_token and chat_id:
                self.alerter = TelegramAlerter(bot_token, chat_id)
                st.success("✅ Telegram已配置")
            
            if st.button("测试告警"):
                if self.alerter.enabled:
                    success = self.alerter.send_alert("测试消息", "INFO")
                    if success:
                        st.success("📱 测试消息已发送")
                    else:
                        st.error("❌ 发送失败")
                else:
                    st.error("请先配置Token和Chat ID")
        
        # Redis状态
        with st.sidebar.expander("💾 数据状态"):
            if self.data_manager.redis_client:
                st.success("✅ Redis连接正常")
                data_count = len(self.data_manager.get_latest_data(10))
                st.info(f"📊 缓存数据: {data_count}条")
            else:
                st.error("❌ Redis连接失败")
        
        # 自动刷新
        auto_refresh = st.sidebar.checkbox("🔄 自动刷新", value=True)
        if auto_refresh:
            refresh_interval = st.sidebar.slider("刷新间隔(秒)", 5, 60, 10)
            st.sidebar.info(f"⏱️ {refresh_interval}秒后自动刷新")
        
        # 主标题
        st.title("🎯 高级策略监控仪表盘 v2.0")
        st.markdown("---")
        
        # 生成模拟数据
        current_data = self.generate_mock_data()
        
        # 核心指标卡片
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            current_equity = current_data['equity_values'][-1]
            initial_equity = current_data['equity_values'][0]
            total_return = (current_equity / initial_equity - 1) * 100
            st.metric(
                "💰 当前权益", 
                f"${current_equity:,.2f}", 
                f"{total_return:+.2f}%"
            )
        
        with col2:
            if len(current_data['equity_values']) > 1:
                daily_return = (current_data['equity_values'][-1] / current_data['equity_values'][-2] - 1) * 100
            else:
                daily_return = 0
            st.metric(
                "📈 最新收益", 
                f"{daily_return:+.2f}%"
            )
        
        with col3:
            # 计算最大回撤
            equity_series = pd.Series(current_data['equity_values'])
            rolling_max = equity_series.expanding().max()
            drawdowns = (equity_series / rolling_max - 1) * 100
            max_dd = drawdowns.min()
            st.metric(
                "📉 最大回撤", 
                f"{max_dd:.2f}%"
            )
        
        with col4:
            total_trades = len(current_data['trades'])
            total_costs = sum(current_data['cost_breakdown'].values())
            st.metric(
                "🔄 总交易数", 
                f"{total_trades}笔",
                f"成本${total_costs:.1f}"
            )
        
        st.markdown("---")
        
        # 主要图表
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # 权益曲线图
            equity_fig = self.create_equity_benchmark_chart(
                current_data['timestamps'],
                current_data['equity_values'],
                current_data['btc_prices']
            )
            st.plotly_chart(equity_fig, use_container_width=True)
        
        with col2:
            # 费用分解图
            cost_fig = self.create_cost_breakdown_chart(current_data['cost_breakdown'])
            st.plotly_chart(cost_fig, use_container_width=True)
        
        # VaR风险监控
        var_fig = self.create_var_chart(
            current_data['timestamps'],
            current_data['equity_values']
        )
        st.plotly_chart(var_fig, use_container_width=True)
        
        # 交易记录表
        st.subheader("📋 最近交易记录")
        if current_data['trades']:
            trades_df = pd.DataFrame(current_data['trades'])
            trades_df['timestamp'] = trades_df['timestamp'].dt.strftime('%H:%M:%S')
            trades_df = trades_df.round(3)
            st.dataframe(trades_df.tail(10), use_container_width=True)
        else:
            st.info("暂无交易记录")
        
        # 告警检查
        alerts = self.check_alert_conditions(current_data)
        if alerts:
            st.subheader("🚨 实时告警")
            for alert in alerts:
                if alert['level'] == 'ERROR':
                    st.error(alert['message'])
                elif alert['level'] == 'WARNING':
                    st.warning(alert['message'])
                else:
                    st.info(alert['message'])
                
                # 发送Telegram告警
                if self.alerter.enabled:
                    self.alerter.send_alert(alert['message'], alert['level'])
        
        # 系统状态
        with st.expander("🔍 系统状态"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write("**连接状态**")
                st.write(f"Redis: {'✅' if self.data_manager.redis_client else '❌'}")
                st.write(f"Telegram: {'✅' if self.alerter.enabled else '❌'}")
            
            with col2:
                st.write("**性能指标**")
                st.write(f"数据点数: {len(current_data['timestamps'])}")
                st.write(f"更新时间: {datetime.now().strftime('%H:%M:%S')}")
            
            with col3:
                st.write("**风险状态**")
                current_var = abs(np.percentile(pd.Series(current_data['equity_values']).pct_change().dropna().tail(50), 5)) * 100 if len(current_data['equity_values']) > 50 else 0
                risk_level = "🟢 低" if current_var < 1 else "🟡 中" if current_var < 2 else "🔴 高"
                st.write(f"风险等级: {risk_level}")
                st.write(f"当前VaR: {current_var:.2f}%")
        
        # 自动刷新
        if auto_refresh:
            time.sleep(refresh_interval)
            st.rerun()

# 主程序入口
if __name__ == "__main__":
    dashboard = AdvancedDashboard()
    dashboard.run_dashboard() 