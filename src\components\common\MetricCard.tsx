import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn, formatCurrency, formatPercentage, getReturnColor } from '@/lib/utils'

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  changeType?: 'currency' | 'percentage'
  icon?: React.ComponentType<{ className?: string }>
  className?: string
  description?: string
}

export function MetricCard({
  title,
  value,
  change,
  changeType = 'percentage',
  icon: Icon,
  className,
  description
}: MetricCardProps) {
  const formatChange = (value: number) => {
    if (changeType === 'currency') {
      return formatCurrency(value)
    }
    return formatPercentage(value / 100)
  }

  const changeColor = change ? getReturnColor(change) : 'text-muted-foreground'

  return (
    <Card className={cn('metric-card', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          <div className="text-2xl font-bold">
            {typeof value === 'number' ? formatCurrency(value) : value}
          </div>
          {change !== undefined && (
            <p className={cn('text-xs flex items-center gap-1', changeColor)}>
              <span>
                {change > 0 ? '+' : ''}{formatChange(change)}
              </span>
              <span className="text-muted-foreground">较昨日</span>
            </p>
          )}
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 