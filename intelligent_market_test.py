#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能策略测试版 - 简化调试
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def test_data_loading():
    """测试数据加载"""
    print("🧪 开始数据加载测试...")
    
    try:
        # 加载数据
        data_path = "K线数据/BTCUSDT_15m_189773.csv"
        print(f"📊 加载数据: {data_path}")
        
        df = pd.read_csv(data_path)
        print(f"✅ 数据加载成功，共 {len(df)} 条记录")
        
        # 显示数据基本信息
        print(f"📈 数据范围: {df['datetime'].iloc[0]} 到 {df['datetime'].iloc[-1]}")
        print(f"💰 价格范围: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
        
        # 转换时间格式
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        print("✅ 时间索引设置完成")
        
        return df
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def test_technical_indicators(df, max_rows=1000):
    """测试技术指标计算 - 仅计算前1000行"""
    print("\n🧪 开始技术指标测试...")
    
    try:
        # 只取前1000行进行测试
        test_df = df.head(max_rows).copy()
        print(f"📊 测试数据: {len(test_df)} 条记录")
        
        # 计算简单移动平均
        test_df['ma_20'] = test_df['close'].rolling(window=20).mean()
        print("✅ MA20 计算完成")
        
        # 计算RSI
        delta = test_df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        test_df['rsi'] = 100 - (100 / (1 + rs))
        print("✅ RSI 计算完成")
        
        # 计算价格变化率
        test_df['price_change_4h'] = test_df['close'].pct_change(16)
        print("✅ 价格变化率计算完成")
        
        # 显示统计信息
        print(f"📊 技术指标统计:")
        print(f"   MA20 有效值: {test_df['ma_20'].count()}")
        print(f"   RSI 有效值: {test_df['rsi'].count()}")
        print(f"   RSI 范围: {test_df['rsi'].min():.1f} - {test_df['rsi'].max():.1f}")
        
        return test_df
        
    except Exception as e:
        print(f"❌ 技术指标计算失败: {e}")
        return None

def test_market_state_detection(df, max_rows=500):
    """测试市场状态检测 - 仅检测前500行"""
    print("\n🧪 开始市场状态检测测试...")
    
    try:
        # 只取前500行进行测试
        test_df = df.head(max_rows).copy()
        
        states = []
        trend_count = 0
        sideways_count = 0
        
        for i in range(len(test_df)):
            if i < 60:
                states.append("震荡")
                sideways_count += 1
                continue
            
            # 简化的状态检测
            current_data = test_df.iloc[i]
            price_change_4h = current_data.get('price_change_4h', 0)
            
            if abs(price_change_4h) > 0.02:  # 2%变化阈值
                if price_change_4h > 0:
                    states.append("上涨趋势")
                    trend_count += 1
                else:
                    states.append("下跌趋势")
                    trend_count += 1
            else:
                states.append("震荡")
                sideways_count += 1
        
        print(f"✅ 市场状态检测完成")
        print(f"📊 状态分布:")
        print(f"   震荡行情: {sideways_count} ({sideways_count/len(states):.1%})")
        print(f"   趋势行情: {trend_count} ({trend_count/len(states):.1%})")
        
        return states
        
    except Exception as e:
        print(f"❌ 市场状态检测失败: {e}")
        return None

def test_simple_trading(df, states, max_rows=200):
    """测试简单交易逻辑 - 仅测试前200行"""
    print("\n🧪 开始简单交易测试...")
    
    try:
        # 初始化
        cash = 100000
        position = 0
        trades = []
        
        test_df = df.head(max_rows).copy()
        
        for i in range(60, min(len(test_df), len(states))):
            current_price = test_df['close'].iloc[i]
            current_state = states[i]
            
            # 简单交易逻辑
            if current_state == "上涨趋势" and position <= 0:
                # 买入
                quantity = 0.1
                cost = current_price * quantity * 1.0007  # 包含手续费
                if cash >= cost:
                    cash -= cost
                    position += quantity
                    trades.append({
                        'action': 'buy',
                        'price': current_price,
                        'quantity': quantity,
                        'timestamp': test_df.index[i]
                    })
            
            elif current_state == "下跌趋势" and position > 0:
                # 卖出
                quantity = min(0.1, position)
                proceeds = current_price * quantity * 0.9993  # 扣除手续费
                cash += proceeds
                position -= quantity
                trades.append({
                    'action': 'sell',
                    'price': current_price,
                    'quantity': quantity,
                    'timestamp': test_df.index[i]
                })
        
        # 计算最终价值
        final_price = test_df['close'].iloc[-1]
        total_value = cash + position * final_price
        total_return = (total_value - 100000) / 100000
        
        print(f"✅ 简单交易测试完成")
        print(f"💰 交易结果:")
        print(f"   交易次数: {len(trades)}")
        print(f"   最终现金: ${cash:,.2f}")
        print(f"   最终仓位: {position:.3f}")
        print(f"   总价值: ${total_value:,.2f}")
        print(f"   总收益: {total_return:.2%}")
        
        return trades, total_value
        
    except Exception as e:
        print(f"❌ 简单交易测试失败: {e}")
        return None, None

def main():
    """主测试函数"""
    print("🧠 智能策略调试测试")
    print("=" * 50)
    
    # 测试1: 数据加载
    df = test_data_loading()
    if df is None:
        return
    
    # 测试2: 技术指标计算
    df_with_indicators = test_technical_indicators(df, max_rows=1000)
    if df_with_indicators is None:
        return
    
    # 测试3: 市场状态检测
    states = test_market_state_detection(df_with_indicators, max_rows=500)
    if states is None:
        return
    
    # 测试4: 简单交易
    trades, final_value = test_simple_trading(df_with_indicators, states, max_rows=200)
    if trades is None:
        return
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成！")
    print("✅ 基础功能正常，可以继续开发完整版本")
    print("=" * 50)

if __name__ == "__main__":
    main() 