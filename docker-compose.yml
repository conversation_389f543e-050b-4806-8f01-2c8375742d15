version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - VITE_API_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
    networks:
      - quant-network
    restart: unless-stopped

  # 后端服务 (占位)
  backend:
    image: nginx:alpine
    ports:
      - "8000:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./mock-api:/usr/share/nginx/html:ro
    networks:
      - quant-network
    restart: unless-stopped
    command: >
      sh -c "echo 'Backend service placeholder' > /usr/share/nginx/html/index.html &&
             nginx -g 'daemon off;'"

  # Redis (可选 - 用于缓存和会话)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quant-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  strategy-optimizer:
    build: .
    container_name: ultimate_strategy_optimizer
    volumes:
      - ./data:/app/data
      - ./results:/app/results
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - PYTHONPATH=/app
      - TZ=Asia/Shanghai
    ports:
      - "8888:8888"
    command: >
      bash -c "
        echo '🚀 启动终极版策略优化环境...' &&
        python improved_cost_model.py &&
        python gap_risk_protection.py &&
        python feature_selection_optimizer.py &&
        python robustness_validator.py &&
        echo '✅ 所有模块测试完成!' &&
        jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root
      "
    
  # Redis缓存 (可选)
  redis_cache:
    image: redis:7-alpine
    container_name: strategy_cache
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    
  # MongoDB数据库 (可选)
  mongodb:
    image: mongo:5
    container_name: strategy_db
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
    volumes:
      - mongo_data:/data/db

networks:
  quant-network:
    driver: bridge

volumes:
  redis_data:
  mongo_data: 