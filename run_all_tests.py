#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极版策略改进 - 一键测试脚本
运行所有核心模块并生成综合报告
"""

import os
import sys
import time
import subprocess
from datetime import datetime
import traceback

def print_banner():
    """打印启动横幅"""
    print("=" * 80)
    print("🚀 终极版策略改进项目 - 一键测试启动")
    print("=" * 80)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"🐍 Python版本: {sys.version}")
    print("=" * 80)

def run_module_test(module_name, description):
    """运行单个模块测试"""
    print(f"\n🧪 测试模块: {description}")
    print("-" * 50)
    
    try:
        start_time = time.time()
        result = subprocess.run([sys.executable, module_name], 
                              capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ {description} - 测试通过 ({end_time-start_time:.2f}s)")
            if result.stdout:
                # 只显示关键输出行
                lines = result.stdout.split('\n')
                for line in lines:
                    if any(keyword in line for keyword in ['✅', '⚠️', '🚀', '📊', '🛡️', '🎯']):
                        print(f"   {line}")
            return True
        else:
            print(f"❌ {description} - 测试失败 ({end_time-start_time:.2f}s)")
            if result.stderr:
                print(f"   错误: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 测试超时 (>60s)")
        return False
    except Exception as e:
        print(f"💥 {description} - 运行异常: {str(e)}")
        return False

def run_pytest_tests():
    """运行pytest单元测试"""
    print(f"\n🧪 运行单元测试套件")
    print("-" * 50)
    
    try:
        start_time = time.time()
        result = subprocess.run([sys.executable, '-m', 'pytest', 'tests/', '-v', '--tb=short'], 
                              capture_output=True, text=True, timeout=120)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ 单元测试 - 全部通过 ({end_time-start_time:.2f}s)")
            # 提取测试摘要
            lines = result.stdout.split('\n')
            for line in lines:
                if 'passed' in line and ('failed' in line or 'error' in line or '=' in line):
                    print(f"   {line}")
            return True
        else:
            print(f"❌ 单元测试 - 部分失败 ({end_time-start_time:.2f}s)")
            # 显示失败摘要
            lines = result.stdout.split('\n')
            for line in lines:
                if 'FAILED' in line or 'ERROR' in line or 'failed' in line:
                    print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ 单元测试 - 超时 (>120s)")
        return False
    except Exception as e:
        print(f"💥 单元测试 - 运行异常: {str(e)}")
        return False

def generate_summary_report(results):
    """生成测试摘要报告"""
    print("\n" + "=" * 80)
    print("📊 测试摘要报告")
    print("=" * 80)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results.values() if r)
    failed_tests = total_tests - passed_tests
    
    print(f"📈 总测试数: {total_tests}")
    print(f"✅ 通过数量: {passed_tests}")
    print(f"❌ 失败数量: {failed_tests}")
    print(f"📊 通过率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n📋 详细结果:")
    for module, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {module}")
    
    # 总体评估
    if passed_tests == total_tests:
        print("\n🎉 恭喜! 所有模块测试通过，系统就绪!")
        print("💡 建议: 可以开始集成到主策略系统")
    elif passed_tests >= total_tests * 0.8:
        print("\n⚠️ 大部分模块正常，少数需要修复")
        print("💡 建议: 优先修复失败模块，然后进行集成")
    else:
        print("\n🚨 多个模块存在问题，需要系统性修复")
        print("💡 建议: 逐个调试失败模块，确保基础功能正常")
    
    print("=" * 80)

def main():
    """主函数"""
    print_banner()
    
    # 定义测试模块
    test_modules = {
        "真实费用模型": "improved_cost_model.py",
        "Gap风险保护": "gap_risk_protection.py", 
        "特征选择优化": "feature_selection_optimizer.py",
        "稳健性验证": "robustness_validator.py"
    }
    
    # 运行模块测试
    results = {}
    for description, module_file in test_modules.items():
        if os.path.exists(module_file):
            results[description] = run_module_test(module_file, description)
        else:
            print(f"⚠️ 模块文件不存在: {module_file}")
            results[description] = False
    
    # 运行单元测试
    if os.path.exists("tests/"):
        results["单元测试套件"] = run_pytest_tests()
    else:
        print("⚠️ 测试目录不存在: tests/")
        results["单元测试套件"] = False
    
    # 生成摘要报告
    generate_summary_report(results)
    
    # 保存测试日志
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"test_results_{timestamp}.log"
    
    try:
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"终极版策略改进测试报告\n")
            f.write(f"生成时间: {datetime.now()}\n")
            f.write(f"测试结果: {results}\n")
        print(f"📄 测试日志已保存: {log_file}")
    except Exception as e:
        print(f"⚠️ 日志保存失败: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n\n💥 程序异常: {e}")
        traceback.print_exc()
    finally:
        print("\n👋 测试完成，感谢使用!") 