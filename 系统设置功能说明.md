# 系统设置功能说明

## 📍 访问方式
在左侧导航栏点击 **"系统设置"** 或直接访问 `/settings` 路径

## 🎯 功能定位
**系统设置专注于基础配置和连接管理，所有交易策略相关参数请在"策略配置"模块中设置**

## 🎯 功能模块

### 1. API配置 (核心功能)
**位置**: 默认选中的第一个选项卡

**功能特点**:
- 📊 **交易所选择**: 支持 Binance、OKX、Huobi、Gate.io
- 🔑 **API Key管理**: 支持显示/隐藏密钥输入
- 🔐 **Secret Key管理**: 安全的密钥输入界面
- 🧪 **测试网络开关**: 用于开发和测试环境
- ✅ **连接测试**: 一键验证API配置是否正确
- 💾 **配置保存**: 安全存储API配置信息
- 🛡️ **安全提醒**: 权限设置和安全操作指南

**操作流程**:
1. 选择目标交易所
2. 输入API Key和Secret Key
3. 选择是否使用测试网络
4. 点击"测试连接"验证配置
5. 点击"保存配置"存储设置

**安全提醒**:
- API密钥仅需要现货交易和读取权限
- 建议优先使用测试网络验证配置
- 定期更换API密钥确保账户安全
- 不要在公共场所或设备上操作

### 2. 通知设置
**通知方式**:
- 📧 **邮件通知**: 支持自定义邮箱地址
- 📱 **短信通知**: SMS消息推送
- 🔔 **推送通知**: 桌面/移动端通知

**告警类型**:
- ⚡ **风险告警**: 账户风险和异常情况
- 💹 **交易成交通知**: 订单执行结果通知

### 3. 通用设置
**界面配置**:
- 🎨 **主题模式**: 浅色/深色/跟随系统
- 🌍 **界面语言**: 简体中文/English
- ⏱️ **数据刷新频率**: 1秒~30秒可选

**高级设置**:
- 💾 **自动保存配置**: 自动保存用户设置
- 🔊 **启用声音提示**: 重要事件音频通知
- 📊 **发送匿名使用统计**: 帮助改进产品体验

## 🔧 技术特点

### 用户体验
- ✨ **实时预览**: 配置更改立即反映
- 🔒 **密钥保护**: API密钥默认隐藏显示
- 📱 **响应式设计**: 支持移动端操作
- ⚡ **快速验证**: API连接测试功能

### 安全特性
- 🔐 **密钥加密存储**: 敏感信息安全处理
- 🧪 **测试网络支持**: 避免实盘风险
- ✅ **连接验证**: 保存前验证配置有效性
- 🚫 **输入验证**: 防止无效配置

### 状态管理
- 💾 **本地状态**: React state管理配置数据
- 🔄 **异步操作**: 支持加载状态显示
- ✅ **操作反馈**: 成功/失败提示信息
- 🎯 **表单验证**: 必填项和格式检查

## 🎯 使用建议

### 首次配置流程
1. **API配置** (必需)
   - 优先配置测试网络环境
   - 验证连接成功后再保存
   
2. **通知设置** (推荐)
   - 启用风险告警通知
   - 配置有效的邮箱地址

3. **通用设置** (可选)
   - 根据个人习惯调整界面设置

### 安全提醒
- ⚠️ **API权限**: 只授予必要的交易权限
- 🔒 **密钥管理**: 定期更换API密钥
- 🧪 **测试优先**: 先在测试网络验证策略
- 📱 **多重验证**: 启用交易所的双因子认证

## 🔗 与其他模块的关系

### 策略配置模块
**交易策略参数在策略配置中设置**:
- 💰 最大仓位设置
- ⚠️ 风险阈值控制
- 🛑 止损止盈参数
- 📊 交易频率限制
- 🎯 策略特定参数

### 数据流向
```
系统设置 (API配置) → 策略配置 (策略参数) → 实时监控 (执行结果)
```

## 🔮 后续扩展

计划添加的功能:
- 🔔 **Webhook通知支持**
- 📈 **历史配置版本管理**
- 🛡️ **高级安全设置**
- 🌍 **多语言界面支持**
- 📱 **移动端优化**

---

💡 **提示**: 系统设置专注于基础连接和界面配置，所有交易策略相关参数请在"策略配置"模块中设置，确保系统架构清晰分层。 