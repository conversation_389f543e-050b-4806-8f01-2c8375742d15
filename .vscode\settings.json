{"css.validate": false, "less.validate": false, "scss.validate": false, "tailwindCSS.includeLanguages": {"typescript": "typescript", "javascript": "javascript", "typescriptreact": "typescriptreact", "javascriptreact": "javascriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "editor.quickSuggestions": {"strings": true}, "css.customData": [".vscode/css_custom_data.json"]}