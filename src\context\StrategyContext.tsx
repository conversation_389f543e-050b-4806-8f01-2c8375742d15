import React, { createContext, useContext, useState } from 'react'

// 策略数据接口
export interface StrategyData {
  id: string
  name: string
  description: string
  initialCapital: number
  profit: number
  profitPercent: number
  maxDrawdown: number
  sharpeRatio: number
  trades: number
  winRate: number
  isActive: boolean
  status: 'running' | 'stopped' | 'paused'
  lastUpdate: string
}

// 上下文接口
interface StrategyContextType {
  strategies: StrategyData[]
  activeStrategy: StrategyData | null
  updateStrategy: (id: string, updates: Partial<StrategyData>) => void
  getStrategyById: (id: string) => StrategyData | undefined
}

// 创建上下文
const StrategyContext = createContext<StrategyContextType | undefined>(undefined)

// 策略提供者组件
export function StrategyProvider({ children }: { children: React.ReactNode }) {
  const [strategies, setStrategies] = useState<StrategyData[]>([
    {
      id: '1',
      name: '策略终极版',
      description: '高频智能交易策略，专为比特币设计',
      initialCapital: 100000,
      profit: 45079000, // 450.79% 年化收益
      profitPercent: 450.79,
      maxDrawdown: -33.24,
      sharpeRatio: 2.85,
      trades: 66312,
      winRate: 89.7,
      isActive: true,
      status: 'running',
      lastUpdate: '2023-04-01'
    }
  ])

  const [activeStrategy, setActiveStrategy] = useState<StrategyData | null>(
    strategies.find(s => s.isActive) || null
  )

  const updateStrategy = (id: string, updates: Partial<StrategyData>) => {
    setStrategies(prev => 
      prev.map(strategy => 
        strategy.id === id ? { ...strategy, ...updates } : strategy
      )
    )
    
    // 更新活跃策略
    if (activeStrategy?.id === id) {
      setActiveStrategy(prev => prev ? { ...prev, ...updates } : null)
    }
  }

  const getStrategyById = (id: string) => {
    return strategies.find(strategy => strategy.id === id)
  }

  const value = {
    strategies,
    activeStrategy,
    updateStrategy,
    getStrategyById
  }

  return (
    <StrategyContext.Provider value={value}>
      {children}
    </StrategyContext.Provider>
  )
}

// 自定义Hook
export function useStrategy() {
  const context = useContext(StrategyContext)
  if (context === undefined) {
    throw new Error('useStrategy must be used within a StrategyProvider')
  }
  return context
} 