#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能行情识别与动态策略切换系统 v2.0 - 优化版
核心理念：精准识别行情，降低交易频率，严格风控
作者：顶尖量化交易师
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Tuple, Optional, Literal
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum


class MarketState(Enum):
    """市场状态枚举"""
    SIDEWAYS = "震荡"  # 震荡行情
    UPTREND = "上涨趋势"  # 上涨趋势 
    DOWNTREND = "下跌趋势"  # 下跌趋势
    UNKNOWN = "未知"  # 未知状态


@dataclass
class TradeSignal:
    """交易信号"""
    action: str  # 'buy', 'sell', 'hold'
    price: float
    quantity: float
    strategy_type: str  # 'grid', 'trend_follow'
    timestamp: pd.Timestamp
    target_profit: float = 0.005  # 提高目标利润率到0.5%
    stop_loss: float = 0.02  # 增加2%止损


@dataclass
class Position:
    """持仓信息"""
    entry_price: float
    quantity: float
    entry_time: pd.Timestamp
    strategy_type: str
    target_profit: float = 0.005
    stop_loss: float = 0.02


class TechnicalIndicators:
    """技术指标计算"""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """简单移动平均线"""
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """指数移动平均线"""
        return data.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """布林带"""
        sma = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """相对强弱指标"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """平均真实波动范围"""
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        return tr.rolling(window=period).mean()


class MarketStateDetector:
    """优化的市场状态识别器"""
    
    def __init__(self, trend_period: int = 100, volatility_period: int = 50):
        self.trend_period = trend_period
        self.volatility_period = volatility_period
        self.state_stability_count = 0  # 状态稳定性计数
        self.min_stability_periods = 20  # 最小稳定周期
    
    def detect_market_state(self, df: pd.DataFrame, current_idx: int) -> MarketState:
        """优化的市场状态识别"""
        if current_idx < max(self.trend_period, self.volatility_period):
            return MarketState.UNKNOWN
        
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # 1. 多时间框架趋势分析
        # 短期趋势（20周期）
        sma_20 = close.rolling(20).mean()
        sma_50 = close.rolling(50).mean()
        sma_100 = close.rolling(100).mean()
        
        # 趋势强度计算
        trend_20_50 = (sma_20.iloc[current_idx] - sma_50.iloc[current_idx]) / sma_50.iloc[current_idx]
        trend_50_100 = (sma_50.iloc[current_idx] - sma_100.iloc[current_idx]) / sma_100.iloc[current_idx]
        
        # 价格相对位置
        price_vs_sma20 = (close.iloc[current_idx] - sma_20.iloc[current_idx]) / sma_20.iloc[current_idx]
        price_vs_sma50 = (close.iloc[current_idx] - sma_50.iloc[current_idx]) / sma_50.iloc[current_idx]
        
        # 2. 波动率分析
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(close, 20)
        bb_width = (bb_upper.iloc[current_idx] - bb_lower.iloc[current_idx]) / bb_middle.iloc[current_idx]
        bb_position = (close.iloc[current_idx] - bb_lower.iloc[current_idx]) / (bb_upper.iloc[current_idx] - bb_lower.iloc[current_idx])
        
        # 3. 动量指标
        rsi = TechnicalIndicators.rsi(close, 14)
        
        # 4. 成交量确认
        volume_sma = volume.rolling(20).mean()
        volume_ratio = volume.iloc[current_idx] / volume_sma.iloc[current_idx] if volume_sma.iloc[current_idx] > 0 else 1
        
        # 5. 价格区间分析
        recent_high = high.iloc[current_idx-50:current_idx+1].max()
        recent_low = low.iloc[current_idx-50:current_idx+1].min()
        price_range = (recent_high - recent_low) / recent_low
        
        # 当前价格在区间中的位置
        range_position = (close.iloc[current_idx] - recent_low) / (recent_high - recent_low)
        
        # 6. 综合判断逻辑（更严格的条件）
        # 强上涨趋势
        strong_uptrend = (
            trend_20_50 > 0.02 and  # 短期趋势强劲
            trend_50_100 > 0.01 and  # 中期趋势向上
            price_vs_sma20 > 0.02 and  # 价格明显高于短期均线
            price_vs_sma50 > 0.02 and  # 价格明显高于中期均线
            bb_position > 0.6 and  # 价格在布林带上部
            rsi.iloc[current_idx] > 55 and rsi.iloc[current_idx] < 80 and  # RSI适中偏强
            volume_ratio > 1.2  # 成交量放大
        )
        
        # 强下跌趋势
        strong_downtrend = (
            trend_20_50 < -0.02 and  # 短期趋势下跌
            trend_50_100 < -0.01 and  # 中期趋势向下
            price_vs_sma20 < -0.02 and  # 价格明显低于短期均线
            price_vs_sma50 < -0.02 and  # 价格明显低于中期均线
            bb_position < 0.4 and  # 价格在布林带下部
            rsi.iloc[current_idx] < 45 and rsi.iloc[current_idx] > 20 and  # RSI适中偏弱
            volume_ratio > 1.2  # 成交量放大
        )
        
        # 震荡行情（更严格的条件）
        sideways_conditions = (
            abs(trend_20_50) < 0.01 and  # 短期趋势不明显
            abs(trend_50_100) < 0.005 and  # 中期趋势不明显
            bb_width < 0.08 and  # 波动率较低
            0.3 < bb_position < 0.7 and  # 价格在布林带中部
            30 < rsi.iloc[current_idx] < 70 and  # RSI中性
            price_range < 0.15  # 价格区间相对较小
        )
        
        # 返回判断结果
        if strong_uptrend:
            return MarketState.UPTREND
        elif strong_downtrend:
            return MarketState.DOWNTREND
        elif sideways_conditions:
            return MarketState.SIDEWAYS
        else:
            return MarketState.UNKNOWN


class OptimizedGridStrategy:
    """优化的网格策略"""
    
    def __init__(self, grid_spacing: float = 0.01, max_grids: int = 6):
        """
        Args:
            grid_spacing: 网格间距（提高到1%）
            max_grids: 最大网格数量（减少到6个）
        """
        self.grid_spacing = grid_spacing
        self.max_grids = max_grids
        self.grid_levels = []
        self.open_orders = {}
        self.last_trade_time = None
        self.min_trade_interval = 4  # 最小交易间隔（4个15分钟 = 1小时）
        
    def initialize_grids(self, current_price: float, base_quantity: float = 0.15):
        """初始化网格（更大的网格间距）"""
        self.grid_levels = []
        self.open_orders = {}
        
        # 以当前价格为中心，上下各设置网格
        for i in range(-self.max_grids//2, self.max_grids//2 + 1):
            if i == 0:
                continue
            
            grid_price = current_price * (1 + i * self.grid_spacing)
            self.grid_levels.append(grid_price)
            
            if i < 0:  # 下方网格设置买单
                self.open_orders[grid_price] = ('buy', base_quantity)
            else:  # 上方网格设置卖单
                self.open_orders[grid_price] = ('sell', base_quantity)
    
    def get_grid_signal(self, current_price: float, current_time: pd.Timestamp) -> Optional[TradeSignal]:
        """获取网格交易信号（增加时间间隔限制）"""
        # 检查交易间隔
        if (self.last_trade_time is not None and 
            (current_time - self.last_trade_time).total_seconds() < self.min_trade_interval * 15 * 60):
            return None
        
        for price, (action, quantity) in list(self.open_orders.items()):
            if action == 'buy' and current_price <= price:
                # 触发买单
                del self.open_orders[price]
                # 在上方设置对应的卖单
                sell_price = price * (1 + self.grid_spacing)
                self.open_orders[sell_price] = ('sell', quantity)
                self.last_trade_time = current_time
                
                return TradeSignal(
                    action='buy',
                    price=price,
                    quantity=quantity,
                    strategy_type='grid',
                    timestamp=current_time,
                    target_profit=self.grid_spacing
                )
            
            elif action == 'sell' and current_price >= price:
                # 触发卖单
                del self.open_orders[price]
                # 在下方设置对应的买单
                buy_price = price * (1 - self.grid_spacing)
                self.open_orders[buy_price] = ('buy', quantity)
                self.last_trade_time = current_time
                
                return TradeSignal(
                    action='sell',
                    price=price,
                    quantity=quantity,
                    strategy_type='grid',
                    timestamp=current_time,
                    target_profit=self.grid_spacing
                )
        
        return None


class OptimizedTrendStrategy:
    """优化的趋势跟踪策略"""
    
    def __init__(self, profit_target: float = 0.005, stop_loss: float = 0.02, max_position_size: float = 0.2):
        """
        Args:
            profit_target: 目标利润率（提高到0.5%）
            stop_loss: 止损比例（2%）
            max_position_size: 最大仓位比例（降低到20%）
        """
        self.profit_target = profit_target
        self.stop_loss = stop_loss
        self.max_position_size = max_position_size
        self.current_position = None
        self.last_trade_time = None
        self.min_trade_interval = 8  # 最小交易间隔（2小时）
        
    def get_trend_signal(self, df: pd.DataFrame, current_idx: int, 
                        market_state: MarketState, current_time: pd.Timestamp) -> Optional[TradeSignal]:
        """获取趋势跟踪信号（增加更严格的条件）"""
        if current_idx < 100:
            return None
            
        current_price = df['close'].iloc[current_idx]
        
        # 检查当前持仓的止盈止损
        if self.current_position:
            profit_rate = (current_price - self.current_position.entry_price) / self.current_position.entry_price
            
            # 多头止盈止损
            if self.current_position.quantity > 0:
                if profit_rate >= self.profit_target or profit_rate <= -self.stop_loss:
                    signal = TradeSignal(
                        action='sell',
                        price=current_price,
                        quantity=self.current_position.quantity,
                        strategy_type='trend_follow',
                        timestamp=current_time,
                        target_profit=profit_rate
                    )
                    self.current_position = None
                    self.last_trade_time = current_time
                    return signal
            
            # 空头止盈止损
            elif self.current_position.quantity < 0:
                if profit_rate <= -self.profit_target or profit_rate >= self.stop_loss:
                    signal = TradeSignal(
                        action='buy',
                        price=current_price,
                        quantity=abs(self.current_position.quantity),
                        strategy_type='trend_follow',
                        timestamp=current_time,
                        target_profit=abs(profit_rate)
                    )
                    self.current_position = None
                    self.last_trade_time = current_time
                    return signal
        
        # 检查交易间隔
        if (self.last_trade_time is not None and 
            (current_time - self.last_trade_time).total_seconds() < self.min_trade_interval * 15 * 60):
            return None
        
        # 没有持仓时，寻找新的入场机会（更严格的条件）
        if not self.current_position:
            close = df['close']
            ema_10 = TechnicalIndicators.ema(close, 10)
            ema_20 = TechnicalIndicators.ema(close, 20)
            ema_50 = TechnicalIndicators.ema(close, 50)
            rsi = TechnicalIndicators.rsi(close, 14)
            
            # 计算动量
            momentum_4h = df['returns_4h'].iloc[current_idx]
            momentum_1d = df['returns_1d'].iloc[current_idx]
            
            # 上涨趋势入场条件（更严格）
            if market_state == MarketState.UPTREND:
                if (current_price > ema_10.iloc[current_idx] and 
                    ema_10.iloc[current_idx] > ema_20.iloc[current_idx] and
                    ema_20.iloc[current_idx] > ema_50.iloc[current_idx] and  # 均线多头排列
                    rsi.iloc[current_idx] > 55 and rsi.iloc[current_idx] < 75 and
                    momentum_4h > 0.01 and  # 4小时动量为正
                    momentum_1d > 0.02):  # 日动量为正
                    
                    quantity = self.max_position_size
                    self.current_position = Position(
                        entry_price=current_price,
                        quantity=quantity,
                        entry_time=current_time,
                        strategy_type='trend_follow',
                        target_profit=self.profit_target,
                        stop_loss=self.stop_loss
                    )
                    self.last_trade_time = current_time
                    
                    return TradeSignal(
                        action='buy',
                        price=current_price,
                        quantity=quantity,
                        strategy_type='trend_follow',
                        timestamp=current_time,
                        target_profit=self.profit_target,
                        stop_loss=self.stop_loss
                    )
            
            # 下跌趋势入场条件（更严格）
            elif market_state == MarketState.DOWNTREND:
                if (current_price < ema_10.iloc[current_idx] and 
                    ema_10.iloc[current_idx] < ema_20.iloc[current_idx] and
                    ema_20.iloc[current_idx] < ema_50.iloc[current_idx] and  # 均线空头排列
                    rsi.iloc[current_idx] < 45 and rsi.iloc[current_idx] > 25 and
                    momentum_4h < -0.01 and  # 4小时动量为负
                    momentum_1d < -0.02):  # 日动量为负
                    
                    quantity = -self.max_position_size
                    self.current_position = Position(
                        entry_price=current_price,
                        quantity=quantity,
                        entry_time=current_time,
                        strategy_type='trend_follow',
                        target_profit=self.profit_target,
                        stop_loss=self.stop_loss
                    )
                    self.last_trade_time = current_time
                    
                    return TradeSignal(
                        action='sell',
                        price=current_price,
                        quantity=abs(quantity),
                        strategy_type='trend_follow',
                        timestamp=current_time,
                        target_profit=self.profit_target,
                        stop_loss=self.stop_loss
                    )
        
        return None


class OptimizedTradingSystem:
    """优化的智能交易系统"""
    
    def __init__(self, initial_capital: float = 100000,
                 commission_rate: float = 0.0005,
                 slippage_rate: float = 0.0002):
        """
        初始化优化的智能交易系统
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        
        # 策略组件
        self.market_detector = MarketStateDetector()
        self.grid_strategy = OptimizedGridStrategy(grid_spacing=0.01, max_grids=6)
        self.trend_strategy = OptimizedTrendStrategy(profit_target=0.005, stop_loss=0.02, max_position_size=0.2)
        
        # 状态管理
        self.current_state = MarketState.UNKNOWN
        self.last_state_change = None
        self.state_history = []
        self.state_stability_count = 0
        
        # 回测结果
        self.portfolio_value = initial_capital
        self.cash = initial_capital
        self.position = 0.0
        self.trades = []
        self.portfolio_history = []
        
    def load_data(self, csv_path: str) -> pd.DataFrame:
        """加载数据并计算指标"""
        print(f"📊 加载数据: {csv_path}")
        
        df = pd.read_csv(csv_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        # 计算基础指标
        close = df['close']
        df['returns_1h'] = close.pct_change(4)
        df['returns_4h'] = close.pct_change(16)
        df['returns_1d'] = close.pct_change(96)
        
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        return df
    
    def execute_trade(self, signal: TradeSignal) -> Dict:
        """执行交易"""
        # 计算交易成本
        trade_value = signal.price * signal.quantity
        total_cost_rate = self.commission_rate + self.slippage_rate
        trade_cost = trade_value * total_cost_rate
        
        trade_info = {
            'timestamp': signal.timestamp,
            'action': signal.action,
            'price': signal.price,
            'quantity': signal.quantity,
            'strategy': signal.strategy_type,
            'cost': trade_cost,
            'target_profit': signal.target_profit
        }
        
        if signal.action == 'buy':
            # 买入
            total_cost = trade_value + trade_cost
            if self.cash >= total_cost:
                self.cash -= total_cost
                self.position += signal.quantity
                trade_info['executed'] = True
            else:
                trade_info['executed'] = False
                trade_info['reason'] = '资金不足'
        
        elif signal.action == 'sell':
            # 卖出
            if self.position >= signal.quantity:
                self.cash += trade_value - trade_cost
                self.position -= signal.quantity
                trade_info['executed'] = True
            else:
                trade_info['executed'] = False
                trade_info['reason'] = '持仓不足'
        
        self.trades.append(trade_info)
        return trade_info
    
    def backtest(self, df: pd.DataFrame) -> Dict:
        """运行优化回测"""
        print("\n🚀 开始优化智能策略回测...")
        
        # 初始化
        grid_initialized = False
        state_changes = 0
        
        for i in range(200, len(df)):  # 从200开始确保指标稳定
            current_time = df.index[i]
            current_price = df['close'].iloc[i]
            
            # 1. 识别市场状态（增加稳定性检查）
            new_state = self.market_detector.detect_market_state(df, i)
            
            if new_state == self.current_state:
                self.state_stability_count += 1
            else:
                self.state_stability_count = 0
            
            # 只有状态稳定一定周期后才切换策略
            if (new_state != self.current_state and 
                new_state != MarketState.UNKNOWN and 
                self.state_stability_count >= 10):  # 需要10个周期的稳定性
                
                print(f"📊 {current_time}: 市场状态变化 {self.current_state.value} -> {new_state.value}")
                self.current_state = new_state
                self.last_state_change = current_time
                state_changes += 1
                
                # 状态变化时重新初始化网格
                if new_state == MarketState.SIDEWAYS:
                    self.grid_strategy.initialize_grids(current_price, 0.15)
                    grid_initialized = True
            
            # 记录状态历史
            self.state_history.append({
                'timestamp': current_time,
                'state': self.current_state,
                'price': current_price
            })
            
            # 2. 根据市场状态生成交易信号
            signal = None
            
            if self.current_state == MarketState.SIDEWAYS and grid_initialized:
                # 震荡行情 - 使用网格策略
                signal = self.grid_strategy.get_grid_signal(current_price, current_time)
                
            elif self.current_state in [MarketState.UPTREND, MarketState.DOWNTREND]:
                # 趋势行情 - 使用趋势跟踪
                signal = self.trend_strategy.get_trend_signal(df, i, self.current_state, current_time)
            
            # 3. 执行交易
            if signal:
                trade_result = self.execute_trade(signal)
                if trade_result['executed'] and i % 5000 == 0:
                    print(f"💱 {signal.strategy_type}: {signal.action} {signal.quantity:.3f} @ {signal.price:.2f}")
            
            # 4. 更新组合价值
            self.portfolio_value = self.cash + self.position * current_price
            self.portfolio_history.append({
                'timestamp': current_time,
                'value': self.portfolio_value,
                'cash': self.cash,
                'position': self.position,
                'price': current_price,
                'state': self.current_state.value
            })
        
        print(f"\n✅ 回测完成！")
        print(f"📊 市场状态变化次数: {state_changes}")
        print(f"💼 执行交易次数: {len([t for t in self.trades if t['executed']])}")
        
        return self._calculate_performance()
    
    def _calculate_performance(self) -> Dict:
        """计算绩效指标"""
        if not self.portfolio_history:
            return {}
        
        portfolio_df = pd.DataFrame(self.portfolio_history)
        portfolio_df.set_index('timestamp', inplace=True)
        
        # 基础收益指标
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        # 计算日收益率
        portfolio_df['daily_return'] = portfolio_df['value'].pct_change()
        
        # 时间相关指标
        start_date = portfolio_df.index[0]
        end_date = portfolio_df.index[-1]
        days = (end_date - start_date).days
        
        annual_return = (self.portfolio_value / self.initial_capital) ** (365 / days) - 1 if days > 0 else 0
        monthly_return = (self.portfolio_value / self.initial_capital) ** (30 / days) - 1 if days > 0 else 0
        
        # 风险指标
        rolling_max = portfolio_df['value'].expanding().max()
        drawdown = (portfolio_df['value'] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 夏普比率
        risk_free_rate = 0.02
        excess_returns = portfolio_df['daily_return'] - risk_free_rate / 365
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(365) if excess_returns.std() > 0 else 0
        
        # 交易统计
        executed_trades = [t for t in self.trades if t['executed']]
        total_trades = len(executed_trades)
        
        profitable_trades = len([t for t in executed_trades if 
                               (t['action'] == 'sell' and t.get('target_profit', 0) > 0)])
        
        win_rate = profitable_trades / total_trades if total_trades > 0 else 0
        
        # 策略分解
        grid_trades = len([t for t in executed_trades if t['strategy'] == 'grid'])
        trend_trades = len([t for t in executed_trades if t['strategy'] == 'trend_follow'])
        
        # 状态统计
        state_stats = {}
        for state in MarketState:
            count = len([s for s in self.state_history if s['state'] == state])
            state_stats[state.value] = count / len(self.state_history) if self.state_history else 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'grid_trades': grid_trades,
            'trend_trades': trend_trades,
            'final_value': self.portfolio_value,
            'market_state_distribution': state_stats,
            'portfolio_series': portfolio_df['value'],
            'drawdown_series': drawdown,
            'state_history': self.state_history
        }


def main():
    """主函数：优化智能策略回测"""
    
    print("🧠 智能行情识别与动态策略切换系统 v2.0 - 优化版")
    print("=" * 70)
    print("💡 核心优化：精准识别行情，降低交易频率，严格风控")
    print("🎯 目标：智能识别行情，动态调整策略，控制风险")
    print("=" * 70)
    
    # 初始化系统
    trading_system = OptimizedTradingSystem(
        initial_capital=100000,
        commission_rate=0.0005,  # 0.05% 手续费
        slippage_rate=0.0002     # 0.02% 滑点
    )
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = trading_system.load_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 运行回测
    try:
        results = trading_system.backtest(df)
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return
    
    # 输出结果
    print("\n" + "=" * 70)
    print("📊 优化智能策略回测结果")
    print("=" * 70)
    
    print(f"\n💰 收益表现:")
    print(f"   总收益: {results['total_return']:.2%}")
    print(f"   年化收益: {results['annual_return']:.2%}")
    print(f"   月化收益: {results['monthly_return']:.2%}")
    print(f"   最终资产: ${results['final_value']:,.2f}")
    
    print(f"\n🛡️ 风险控制:")
    print(f"   最大回撤: {results['max_drawdown']:.2%}")
    print(f"   夏普比率: {results['sharpe_ratio']:.2f}")
    
    print(f"\n📈 交易统计:")
    print(f"   总交易次数: {results['total_trades']}")
    if results['total_trades'] > 0:
        print(f"   网格交易: {results['grid_trades']} ({results['grid_trades']/results['total_trades']:.1%})")
        print(f"   趋势交易: {results['trend_trades']} ({results['trend_trades']/results['total_trades']:.1%})")
    print(f"   胜率: {results['win_rate']:.2%}")
    
    print(f"\n🎯 市场状态分布:")
    for state, percentage in results['market_state_distribution'].items():
        print(f"   {state}: {percentage:.1%}")
    
    # 策略评估
    print(f"\n🔍 策略评估:")
    
    # 检查是否符合目标
    target_met = (results['max_drawdown'] >= -0.15 and 
                  results['monthly_return'] >= 0.10)
    
    if target_met:
        print(f"   ✅ 恭喜！策略达到目标条件")
        print(f"   🎯 回撤控制: {results['max_drawdown']:.2%} (目标: ≤15%)")
        print(f"   🎯 月化收益: {results['monthly_return']:.2%} (目标: ≥10%)")
    else:
        print(f"   🔧 策略需要进一步优化")
        if results['max_drawdown'] < -0.15:
            print(f"   ⚠️ 回撤超限: {results['max_drawdown']:.2%} (目标: ≤15%)")
        if results['monthly_return'] < 0.10:
            print(f"   ⚠️ 收益不足: {results['monthly_return']:.2%} (目标: ≥10%)")
    
    # 优化建议
    print(f"\n🤖 优化效果分析:")
    
    if results['total_trades'] < 1000:
        print(f"   ✅ 交易频率已大幅降低，避免过度交易")
    
    if results['max_drawdown'] > -0.50:
        print(f"   ✅ 风险控制有所改善")
    
    if results['win_rate'] > 0.45:
        print(f"   ✅ 胜率表现良好")
    
    print(f"\n🎊 优化智能策略回测完成！")
    
    return trading_system, results


if __name__ == "__main__":
    trading_system, results = main() 