#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能策略极致版 - 冲击月化收益10%
目标：月化收益10%+，年化收益300%+
核心理念：激进参数+多策略融合+动态风控+超短线盈利
作者：顶尖量化交易师
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Optional
import time
from dataclasses import dataclass
from enum import Enum


class MarketState(Enum):
    """市场状态"""
    SIDEWAYS = "震荡"
    UPTREND = "上涨趋势" 
    DOWNTREND = "下跌趋势"
    VOLATILE = "高波动"
    BREAKOUT = "突破"


@dataclass
class Trade:
    """交易记录"""
    timestamp: pd.Timestamp
    action: str  # 'buy', 'sell'
    price: float
    quantity: float
    strategy: str  # 'grid', 'trend', 'scalp', 'breakout', 'momentum'
    executed: bool = True


class AggressiveTradingSystem:
    """极致版交易系统"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.position = 0.0
        self.portfolio_value = initial_capital
        
        # 交易成本
        self.commission_rate = 0.0005  # 0.05%
        self.slippage_rate = 0.0002    # 0.02%
        
        # 极致激进参数
        self.grid_spacing = 0.006      # 网格间距0.6% - 极致频繁
        self.trend_profit = 0.003      # 趋势止盈0.3% - 快速获利
        self.trend_stop_loss = 0.003   # 趋势止损0.3% - 严格控损
        self.trend_threshold = 0.012   # 趋势判断阈值1.2% - 更敏感
        self.max_position_ratio = 0.95 # 最大仓位95% - 极致利用
        
        # 超短线策略参数
        self.scalp_profit = 0.001      # 超短线止盈0.1%
        self.scalp_threshold = 0.002   # 超短线触发阈值0.2%
        self.breakout_threshold = 0.008 # 突破阈值0.8%
        self.momentum_factor = 0.005   # 动量因子0.5%
        
        # 动态风控参数
        self.max_drawdown_limit = 0.30  # 最大回撤限制30%
        self.position_scale_factor = 1.2 # 仓位放大因子
        self.volatility_boost = 1.5    # 波动率加成
        
        # 状态变量
        self.last_grid_price = None
        self.trend_entry_price = None
        self.scalp_entry_price = None
        self.trend_position = 0
        self.scalp_position = 0
        self.max_portfolio_value = initial_capital
        self.consecutive_wins = 0  # 连胜计数
        self.consecutive_losses = 0 # 连亏计数
        
        # 记录
        self.trades = []
        self.portfolio_history = []
    
    def load_and_prepare_data(self, file_path: str) -> pd.DataFrame:
        """加载并预处理数据"""
        print(f"📊 加载数据: {file_path}")
        
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print("📊 预计算极致技术指标...")
        
        # 多周期技术指标
        for period in [5, 10, 20, 60]:
            df[f'ma_{period}'] = df['close'].rolling(window=period).mean()
        
        # 多周期RSI
        for period in [7, 14, 21]:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # 多周期价格变化率
        for period in [1, 4, 8, 16, 32]:
            df[f'price_change_{period}'] = df['close'].pct_change(period)
        
        # 波动率指标
        for period in [5, 10, 20]:
            df[f'volatility_{period}'] = df['close'].rolling(window=period).std() / df['close'].rolling(window=period).mean()
        
        # 动量指标
        df['momentum_short'] = df['close'] / df['close'].shift(2) - 1  # 30分钟动量
        df['momentum_medium'] = df['close'] / df['close'].shift(8) - 1  # 2小时动量
        df['momentum_long'] = df['close'] / df['close'].shift(16) - 1   # 4小时动量
        
        # MACD多周期
        for fast, slow, signal in [(6, 13, 4), (12, 26, 9), (24, 52, 18)]:
            exp1 = df['close'].ewm(span=fast).mean()
            exp2 = df['close'].ewm(span=slow).mean()
            df[f'macd_{fast}_{slow}'] = exp1 - exp2
            df[f'macd_signal_{fast}_{slow}'] = df[f'macd_{fast}_{slow}'].ewm(span=signal).mean()
            df[f'macd_hist_{fast}_{slow}'] = df[f'macd_{fast}_{slow}'] - df[f'macd_signal_{fast}_{slow}']
        
        # 布林带
        df['bb_mid'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_mid'] + 2 * bb_std
        df['bb_lower'] = df['bb_mid'] - 2 * bb_std
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_mid']
        
        # 成交量指标
        df['volume_ma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        df['volume_surge'] = df['volume'] > df['volume_ma'] * 1.5
        
        # 价格强度指标
        df['price_strength'] = (df['close'] - df['close'].rolling(20).min()) / (df['close'].rolling(20).max() - df['close'].rolling(20).min())
        
        print("✅ 极致技术指标计算完成")
        return df
    
    def detect_market_state(self, row: pd.Series) -> MarketState:
        """极致敏感的市场状态检测"""
        # 多周期价格变化
        price_change_1 = row.get('price_change_1', 0)
        price_change_4 = row.get('price_change_4', 0)
        price_change_16 = row.get('price_change_16', 0)
        
        # 动量指标
        momentum_short = row.get('momentum_short', 0)
        momentum_medium = row.get('momentum_medium', 0)
        
        # 技术指标
        volatility_5 = row.get('volatility_5', 0)
        bb_width = row.get('bb_width', 0)
        volume_surge = row.get('volume_surge', False)
        macd_hist_6_13 = row.get('macd_hist_6_13', 0)
        
        # 移动平均线
        ma_5 = row.get('ma_5', 0)
        ma_20 = row.get('ma_20', 0)
        ma_60 = row.get('ma_60', 0)
        
        # 检查突破状态
        if (abs(price_change_1) > self.breakout_threshold or 
            abs(momentum_short) > self.breakout_threshold * 1.5 or
            (volume_surge and abs(price_change_4) > self.breakout_threshold * 0.5)):
            return MarketState.BREAKOUT
        
        # 检查高波动状态
        if volatility_5 > 0.025 or bb_width > 0.05:
            return MarketState.VOLATILE
        
        # 趋势检测 - 更敏感
        trend_signals = 0
        trend_direction = 0
        
        # 价格变化信号
        if abs(price_change_4) > self.trend_threshold:
            trend_signals += 2
            trend_direction += 2 if price_change_4 > 0 else -2
        
        if abs(price_change_1) > self.trend_threshold * 0.3:
            trend_signals += 1
            trend_direction += 1 if price_change_1 > 0 else -1
        
        # 动量信号
        if abs(momentum_short) > self.trend_threshold * 0.8:
            trend_signals += 1
            trend_direction += 1 if momentum_short > 0 else -1
        
        # 移动平均线信号
        if ma_5 > ma_20 > ma_60:
            trend_signals += 1
            trend_direction += 1
        elif ma_5 < ma_20 < ma_60:
            trend_signals += 1
            trend_direction -= 1
        
        # MACD信号
        if abs(macd_hist_6_13) > 0.0005:
            trend_signals += 1
            trend_direction += 1 if macd_hist_6_13 > 0 else -1
        
        # 降低趋势判断门槛
        if trend_signals >= 2:
            if trend_direction > 0:
                return MarketState.UPTREND
            elif trend_direction < 0:
                return MarketState.DOWNTREND
        
        return MarketState.SIDEWAYS
    
    def dynamic_position_size(self, base_size: float, market_state: MarketState, volatility: float) -> float:
        """动态仓位大小计算"""
        # 基础仓位
        position_size = base_size
        
        # 根据市场状态调整
        if market_state == MarketState.BREAKOUT:
            position_size *= 1.5  # 突破时加大仓位
        elif market_state == MarketState.VOLATILE:
            position_size *= 0.7  # 高波动时减小仓位
        
        # 根据连胜连亏调整
        if self.consecutive_wins > 3:
            position_size *= 1.2  # 连胜时适度加仓
        elif self.consecutive_losses > 2:
            position_size *= 0.8  # 连亏时减仓
        
        # 根据波动率调整
        if volatility < 0.01:
            position_size *= 1.1  # 低波动时稍微加仓
        elif volatility > 0.03:
            position_size *= 0.8  # 高波动时减仓
        
        return position_size
    
    def grid_strategy(self, current_price: float, timestamp: pd.Timestamp, volatility: float) -> Optional[Trade]:
        """极致网格策略"""
        if self.last_grid_price is None:
            self.last_grid_price = current_price
            return None
        
        # 动态网格间距
        dynamic_spacing = self.grid_spacing * (1 + volatility * 2)
        price_change = abs(current_price - self.last_grid_price) / self.last_grid_price
        
        if price_change >= dynamic_spacing:
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            
            if current_price > self.last_grid_price:
                # 价格上涨，卖出
                if position_ratio > 0.03:
                    action = 'sell'
                    base_quantity = min(0.06, self.position * 0.7)
                    quantity = self.dynamic_position_size(base_quantity, MarketState.SIDEWAYS, volatility)
                else:
                    return None
            else:
                # 价格下跌，买入
                if position_ratio < self.max_position_ratio:
                    action = 'buy'
                    base_buy = (current_value * 0.1) / current_price
                    max_buy = self.dynamic_position_size(base_buy, MarketState.SIDEWAYS, volatility)
                    quantity = min(max_buy, 0.06)
                else:
                    return None
            
            self.last_grid_price = current_price
            
            return Trade(
                timestamp=timestamp,
                action=action,
                price=current_price,
                quantity=quantity,
                strategy='grid'
            )
        
        return None
    
    def trend_strategy(self, current_price: float, state: MarketState, 
                      row: pd.Series, timestamp: pd.Timestamp) -> Optional[Trade]:
        """极致趋势策略"""
        rsi_7 = row.get('rsi_7', 50)
        rsi_14 = row.get('rsi_14', 50)
        momentum_short = row.get('momentum_short', 0)
        volume_ratio = row.get('volume_ratio', 1)
        volatility_5 = row.get('volatility_5', 0)
        
        # 开仓逻辑 - 更激进的条件
        if state == MarketState.UPTREND and self.trend_position <= 0:
            if (20 < rsi_14 < 80 and momentum_short > -0.005):
                self.trend_position = 1
                self.trend_entry_price = current_price
                current_value = self.cash + self.position * current_price
                
                # 动态仓位大小
                base_position = current_value * 0.2  # 20%资金做趋势
                position_value = self.dynamic_position_size(base_position, state, volatility_5)
                quantity = position_value / current_price
                
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='trend'
                )
        
        elif state == MarketState.DOWNTREND and self.trend_position >= 0:
            if (20 < rsi_14 < 80 and momentum_short < 0.005):
                self.trend_position = -1
                self.trend_entry_price = current_price
                if self.position > 0:
                    base_quantity = min(0.1, self.position * 0.5)
                    quantity = self.dynamic_position_size(base_quantity, state, volatility_5)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
        
        # 快速止盈止损
        elif self.trend_position != 0 and self.trend_entry_price:
            if self.trend_position > 0:  # 多头仓位
                profit_rate = (current_price - self.trend_entry_price) / self.trend_entry_price
                if profit_rate >= self.trend_profit:  # 快速止盈
                    self.trend_position = 0
                    self.trend_entry_price = None
                    self.consecutive_wins += 1
                    self.consecutive_losses = 0
                    quantity = min(0.08, self.position * 0.6)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
                elif profit_rate <= -self.trend_stop_loss:  # 快速止损
                    self.trend_position = 0
                    self.trend_entry_price = None
                    self.consecutive_losses += 1
                    self.consecutive_wins = 0
                    quantity = min(0.08, self.position * 0.6)
                    return Trade(
                        timestamp=timestamp,
                        action='sell',
                        price=current_price,
                        quantity=quantity,
                        strategy='trend'
                    )
        
        return None
    
    def scalping_strategy(self, current_price: float, row: pd.Series, 
                         timestamp: pd.Timestamp) -> Optional[Trade]:
        """超短线策略"""
        price_change_1 = row.get('price_change_1', 0)
        momentum_short = row.get('momentum_short', 0)
        rsi_7 = row.get('rsi_7', 50)
        volatility_5 = row.get('volatility_5', 0)
        
        # 超短线机会识别
        if abs(price_change_1) > self.scalp_threshold:
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            
            if (price_change_1 > 0 and rsi_7 < 75 and position_ratio < 0.8):
                # 向上脉冲，买入
                buy_value = current_value * 0.08
                quantity = self.dynamic_position_size(buy_value / current_price, MarketState.VOLATILE, volatility_5)
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='scalp'
                )
            elif (price_change_1 < 0 and rsi_7 > 25 and position_ratio > 0.1):
                # 向下脉冲，卖出
                base_quantity = min(0.05, self.position * 0.4)
                quantity = self.dynamic_position_size(base_quantity, MarketState.VOLATILE, volatility_5)
                return Trade(
                    timestamp=timestamp,
                    action='sell',
                    price=current_price,
                    quantity=quantity,
                    strategy='scalp'
                )
        
        return None
    
    def breakout_strategy(self, current_price: float, row: pd.Series, 
                         timestamp: pd.Timestamp) -> Optional[Trade]:
        """突破策略"""
        momentum_short = row.get('momentum_short', 0)
        volume_surge = row.get('volume_surge', False)
        bb_position = row.get('bb_position', 0.5)
        price_strength = row.get('price_strength', 0.5)
        volatility_5 = row.get('volatility_5', 0)
        
        # 突破信号确认
        breakout_score = 0
        direction = 0
        
        if abs(momentum_short) > self.breakout_threshold:
            breakout_score += 2
            direction += 2 if momentum_short > 0 else -2
        
        if volume_surge:
            breakout_score += 1
            
        if bb_position > 0.9 or bb_position < 0.1:
            breakout_score += 1
            direction += 1 if bb_position > 0.9 else -1
        
        if price_strength > 0.8 or price_strength < 0.2:
            breakout_score += 1
            direction += 1 if price_strength > 0.8 else -1
        
        # 突破交易
        if breakout_score >= 3:
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            
            if direction > 0 and position_ratio < 0.9:
                # 向上突破，重仓买入
                buy_value = current_value * 0.15
                quantity = self.dynamic_position_size(buy_value / current_price, MarketState.BREAKOUT, volatility_5)
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='breakout'
                )
            elif direction < 0 and position_ratio > 0.05:
                # 向下突破，卖出
                base_quantity = min(0.1, self.position * 0.6)
                quantity = self.dynamic_position_size(base_quantity, MarketState.BREAKOUT, volatility_5)
                return Trade(
                    timestamp=timestamp,
                    action='sell',
                    price=current_price,
                    quantity=quantity,
                    strategy='breakout'
                )
        
        return None
    
    def momentum_strategy(self, current_price: float, row: pd.Series, 
                         timestamp: pd.Timestamp) -> Optional[Trade]:
        """动量策略"""
        momentum_medium = row.get('momentum_medium', 0)
        macd_hist_12_26 = row.get('macd_hist_12_26', 0)
        volume_ratio = row.get('volume_ratio', 1)
        volatility_10 = row.get('volatility_10', 0)
        
        # 动量信号
        if (abs(momentum_medium) > self.momentum_factor and 
            volume_ratio > 1.1 and volatility_10 < 0.03):
            
            current_value = self.cash + self.position * current_price
            position_ratio = (self.position * current_price) / current_value
            
            if momentum_medium > 0 and position_ratio < 0.85:
                # 正动量，买入
                buy_value = current_value * 0.06
                quantity = self.dynamic_position_size(buy_value / current_price, MarketState.UPTREND, volatility_10)
                return Trade(
                    timestamp=timestamp,
                    action='buy',
                    price=current_price,
                    quantity=quantity,
                    strategy='momentum'
                )
            elif momentum_medium < 0 and position_ratio > 0.1:
                # 负动量，卖出
                base_quantity = min(0.04, self.position * 0.3)
                quantity = self.dynamic_position_size(base_quantity, MarketState.DOWNTREND, volatility_10)
                return Trade(
                    timestamp=timestamp,
                    action='sell',
                    price=current_price,
                    quantity=quantity,
                    strategy='momentum'
                )
        
        return None
    
    def execute_trade(self, trade: Trade) -> bool:
        """执行交易"""
        trade_value = trade.price * trade.quantity
        cost = trade_value * (self.commission_rate + self.slippage_rate)
        
        if trade.action == 'buy':
            total_cost = trade_value + cost
            if self.cash >= total_cost:
                self.cash -= total_cost
                self.position += trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        elif trade.action == 'sell':
            if self.position >= trade.quantity:
                proceeds = trade_value - cost
                self.cash += proceeds
                self.position -= trade.quantity
                trade.executed = True
            else:
                trade.executed = False
        
        self.trades.append(trade)
        return trade.executed
    
    def backtest(self, df: pd.DataFrame) -> Dict:
        """运行极致回测"""
        print("🚀 开始极致智能策略回测...")
        
        total_rows = len(df)
        start_time = time.time()
        
        # 初始化计数器
        strategy_trades = {
            'grid': 0, 'trend': 0, 'scalp': 0, 'breakout': 0, 'momentum': 0
        }
        state_counts = {state: 0 for state in MarketState}
        
        # 主回测循环 - 从更早开始
        for i in range(60, total_rows):
            current_row = df.iloc[i]
            current_price = current_row['close']
            current_time = df.index[i]
            
            # 显示进度
            if i % 12000 == 0:
                progress = i / total_rows * 100
                elapsed = time.time() - start_time
                eta = elapsed / (i - 59) * (total_rows - i) if i > 60 else 0
                print(f"⏳ 进度: {progress:.1f}% | 用时: {elapsed:.0f}s | 预计剩余: {eta:.0f}s")
            
            # 风险控制 - 简化版
            current_value = self.cash + self.position * current_price
            if current_value > self.max_portfolio_value:
                self.max_portfolio_value = current_value
            
            drawdown = (current_value - self.max_portfolio_value) / self.max_portfolio_value
            if drawdown < -self.max_drawdown_limit:
                continue  # 触发回撤限制，暂停交易
            
            # 检测市场状态
            market_state = self.detect_market_state(current_row)
            state_counts[market_state] += 1
            
            volatility = current_row.get('volatility_5', 0.01)
            
            # 多策略并行运行
            trades = []
            
            # 策略1: 网格策略
            if market_state == MarketState.SIDEWAYS:
                grid_trade = self.grid_strategy(current_price, current_time, volatility)
                if grid_trade:
                    trades.append(grid_trade)
            
            # 策略2: 趋势策略
            if market_state in [MarketState.UPTREND, MarketState.DOWNTREND]:
                trend_trade = self.trend_strategy(current_price, market_state, current_row, current_time)
                if trend_trade:
                    trades.append(trend_trade)
            
            # 策略3: 超短线策略
            scalp_trade = self.scalping_strategy(current_price, current_row, current_time)
            if scalp_trade:
                trades.append(scalp_trade)
            
            # 策略4: 突破策略
            if market_state == MarketState.BREAKOUT:
                breakout_trade = self.breakout_strategy(current_price, current_row, current_time)
                if breakout_trade:
                    trades.append(breakout_trade)
            
            # 策略5: 动量策略
            momentum_trade = self.momentum_strategy(current_price, current_row, current_time)
            if momentum_trade:
                trades.append(momentum_trade)
            
            # 执行所有交易
            for trade in trades:
                if self.execute_trade(trade) and trade.executed:
                    strategy_trades[trade.strategy] += 1
            
            # 更新组合价值
            self.portfolio_value = self.cash + self.position * current_price
            
            # 记录组合历史
            if i % 600 == 0:
                self.portfolio_history.append({
                    'timestamp': current_time,
                    'value': self.portfolio_value,
                    'cash': self.cash,
                    'position': self.position,
                    'price': current_price,
                    'state': market_state.value
                })
        
        elapsed_time = time.time() - start_time
        print(f"\n✅ 极致回测完成！用时: {elapsed_time:.1f}秒")
        print(f"📊 策略交易统计: {strategy_trades}")
        
        return self._calculate_performance(df, state_counts, strategy_trades)
    
    def _calculate_performance(self, df: pd.DataFrame, state_counts: Dict, strategy_trades: Dict) -> Dict:
        """计算绩效指标"""
        
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        start_date = df.index[60]
        end_date = df.index[-1]
        days = (end_date - start_date).days
        
        if days > 0:
            annual_return = (self.portfolio_value / self.initial_capital) ** (365 / days) - 1
            monthly_return = (self.portfolio_value / self.initial_capital) ** (30 / days) - 1
        else:
            annual_return = 0
            monthly_return = 0
        
        # 计算最大回撤
        if self.portfolio_history:
            portfolio_df = pd.DataFrame(self.portfolio_history)
            values = portfolio_df['value']
            rolling_max = values.expanding().max()
            drawdown = (values - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
        else:
            max_drawdown = 0
        
        # 交易统计
        executed_trades = [t for t in self.trades if t.executed]
        total_trades = len(executed_trades)
        
        # 市场状态分布
        total_states = sum(state_counts.values())
        state_distribution = {
            state.value: count / total_states if total_states > 0 else 0 
            for state, count in state_counts.items()
        }
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'strategy_trades': strategy_trades,
            'final_value': self.portfolio_value,
            'final_cash': self.cash,
            'final_position': self.position,
            'market_state_distribution': state_distribution,
            'consecutive_wins': self.consecutive_wins,
            'consecutive_losses': self.consecutive_losses
        }


def main():
    """主函数"""
    print("🚀 智能策略极致版 - 冲击月化收益10%")
    print("=" * 70)
    print("💡 极致改进：激进参数+多策略融合+动态风控+超短线盈利")
    print("🎯 目标：月化收益≥10%，年化收益≥300%")
    print("⚠️ 警告：极致版本风险较高，仅供学习研究")
    print("=" * 70)
    
    # 初始化极致系统
    trading_system = AggressiveTradingSystem(initial_capital=100000)
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = trading_system.load_and_prepare_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 运行极致回测
    try:
        results = trading_system.backtest(df)
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return
    
    # 输出结果
    print("\n" + "=" * 70)
    print("📊 极致智能策略回测结果")
    print("=" * 70)
    
    print(f"\n💰 收益表现:")
    print(f"   总收益: {results['total_return']:.2%}")
    print(f"   年化收益: {results['annual_return']:.2%}")
    print(f"   月化收益: {results['monthly_return']:.2%}")
    print(f"   最终资产: ${results['final_value']:,.2f}")
    
    print(f"\n🛡️ 风险控制:")
    print(f"   最大回撤: {results['max_drawdown']:.2%}")
    
    print(f"\n📈 极致多策略交易统计:")
    print(f"   总交易次数: {results['total_trades']}")
    for strategy, count in results['strategy_trades'].items():
        if results['total_trades'] > 0:
            print(f"   {strategy}策略: {count} ({count/results['total_trades']:.1%})")
    
    print(f"\n🎲 交易心理统计:")
    print(f"   连胜次数: {results['consecutive_wins']}")
    print(f"   连亏次数: {results['consecutive_losses']}")
    
    print(f"\n🎯 市场状态分布:")
    for state, percentage in results['market_state_distribution'].items():
        print(f"   {state}: {percentage:.1%}")
    
    # 终极目标评估
    print(f"\n🔍 终极目标评估:")
    monthly_ultimate = results['monthly_return'] >= 0.10
    annual_ultimate = results['annual_return'] >= 3.0
    drawdown_acceptable = results['max_drawdown'] >= -0.50  # 放宽到50%
    
    score = sum([monthly_ultimate, annual_ultimate, drawdown_acceptable])
    
    if monthly_ultimate:
        print(f"   🎉 恭喜！月化收益目标达成！")
        print(f"   ✅ 月化收益: {results['monthly_return']:.2%} ≥ 10.0%")
    else:
        print(f"   🎯 月化收益: {results['monthly_return']:.2%} < 10.0%")
    
    if annual_ultimate:
        print(f"   🚀 年化收益超预期！{results['annual_return']:.2%} ≥ 300%")
    else:
        print(f"   📈 年化收益: {results['annual_return']:.2%}")
    
    print(f"   {'✅' if drawdown_acceptable else '⚠️'} 最大回撤: {results['max_drawdown']:.2%}")
    
    # 风险警示
    if results['max_drawdown'] < -0.40:
        print(f"\n⚠️ 风险警示:")
        print(f"   回撤超过40%，实盘使用需谨慎")
        print(f"   建议降低仓位或优化风控参数")
    
    # 与买入持有对比
    buy_hold_return = (df['close'].iloc[-1] - df['close'].iloc[60]) / df['close'].iloc[60]
    print(f"\n📊 策略对比:")
    print(f"   极致策略收益: {results['total_return']:.2%}")
    print(f"   买入持有收益: {buy_hold_return:.2%}")
    
    if results['total_return'] > buy_hold_return:
        print(f"   🎯 极致策略跑赢买入持有 {(results['total_return'] - buy_hold_return):.2%}")
    else:
        print(f"   ❌ 极致策略跑输买入持有 {(buy_hold_return - results['total_return']):.2%}")
    
    print(f"\n🎊 极致策略回测完成！")
    print(f"💡 记住：高收益伴随高风险，实盘请谨慎评估")
    
    return results


if __name__ == "__main__":
    results = main() 