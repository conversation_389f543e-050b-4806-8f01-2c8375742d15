import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 格式化数字显示
export function formatNumber(value: number, decimals = 2): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value)
}

// 格式化货币
export function formatCurrency(value: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(value)
}

// 格式化百分比
export function formatPercentage(value: number, decimals = 2): string {
  return `${(value * 100).toFixed(decimals)}%`
}

// 格式化大数字 (1K, 1M, 1B)
export function formatLargeNumber(value: number): string {
  const units = ['', 'K', 'M', 'B', 'T']
  let unitIndex = 0
  let scaledValue = value

  while (Math.abs(scaledValue) >= 1000 && unitIndex < units.length - 1) {
    scaledValue /= 1000
    unitIndex++
  }

  return `${scaledValue.toFixed(1)}${units[unitIndex]}`
}

// 计算收益率颜色
export function getReturnColor(value: number): string {
  if (value > 0) return 'text-success-500'
  if (value < 0) return 'text-danger-500'
  return 'text-muted-foreground'
}

// 计算风险等级
export function getRiskLevel(value: number): { level: string; color: string; label: string } {
  if (value < 0.01) return { level: 'low', color: 'success', label: '低风险' }
  if (value < 0.03) return { level: 'medium', color: 'warning', label: '中风险' }
  if (value < 0.05) return { level: 'high', color: 'danger', label: '高风险' }
  return { level: 'extreme', color: 'danger', label: '极端风险' }
}

// 时间格式化
export function formatTime(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(date)
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(date)
}

export function formatDateTime(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(date)
}

// 延迟函数
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): T {
  let timeout: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }) as T
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): T {
  let inThrottle: boolean
  return ((...args: any[]) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }) as T
} 