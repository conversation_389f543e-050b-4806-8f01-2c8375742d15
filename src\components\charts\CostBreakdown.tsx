

interface CostBreakdownProps {
  totalCosts?: number;
}

export function CostBreakdown({ totalCosts = 0 }: CostBreakdownProps) {
  const costData = {
    commission: totalCosts * 0.67, // 67% 手续费
    slippage: totalCosts * 0.19,   // 19% 滑点
    funding: totalCosts * 0.14,    // 14% 资金费用
    total: totalCosts
  }
  
  const percentages = {
    commission: (costData.commission / costData.total) * 100,
    slippage: (costData.slippage / costData.total) * 100,
    funding: (costData.funding / costData.total) * 100
  }

  return (
    <div className="space-y-4">
      {/* 总成本展示 */}
      <div className="text-center space-y-1">
        <p className="text-2xl font-bold">${costData.total}</p>
        <p className="text-sm text-muted-foreground">本月总成本</p>
      </div>

      {/* 成本分解饼图（简化版） */}
      <div className="relative w-32 h-32 mx-auto">
        <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
          {/* 手续费部分 */}
          <circle
            cx="50"
            cy="50"
            r="40"
            fill="none"
            stroke="hsl(var(--warning))"
            strokeWidth="8"
            strokeDasharray={`${percentages.commission * 2.51}, 251`}
            strokeDashoffset="0"
          />
          {/* 滑点部分 */}
          <circle
            cx="50"
            cy="50"
            r="40"
            fill="none"
            stroke="hsl(var(--danger))"
            strokeWidth="8"
            strokeDasharray={`${percentages.slippage * 2.51}, 251`}
            strokeDashoffset={`-${percentages.commission * 2.51}`}
          />
          {/* 资金费用部分 */}
          <circle
            cx="50"
            cy="50"
            r="40"
            fill="none"
            stroke="hsl(var(--muted))"
            strokeWidth="8"
            strokeDasharray={`${percentages.funding * 2.51}, 251`}
            strokeDashoffset={`-${(percentages.commission + percentages.slippage) * 2.51}`}
          />
        </svg>
        
        {/* 中心总额 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <p className="text-lg font-bold">${costData.total}</p>
            <p className="text-xs text-muted-foreground">总计</p>
          </div>
        </div>
      </div>

      {/* 成本明细 */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm bg-warning-500"></div>
            <span className="text-sm">交易手续费</span>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium">${costData.commission}</p>
            <p className="text-xs text-muted-foreground">{percentages.commission.toFixed(1)}%</p>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm bg-danger-500"></div>
            <span className="text-sm">滑点损失</span>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium">${costData.slippage}</p>
            <p className="text-xs text-muted-foreground">{percentages.slippage.toFixed(1)}%</p>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm bg-muted-foreground"></div>
            <span className="text-sm">资金费用</span>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium">${costData.funding}</p>
            <p className="text-xs text-muted-foreground">{percentages.funding.toFixed(1)}%</p>
          </div>
        </div>
      </div>

      {/* 成本趋势 */}
      <div className="pt-2 border-t border-border">
        <div className="flex items-center justify-between text-xs">
          <span className="text-muted-foreground">较上月</span>
          <span className="text-success-500">-12.3%</span>
        </div>
      </div>
    </div>
  )
} 