import { useState } from 'react'

interface Order {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  type: 'market' | 'limit' | 'stop'
  quantity: number
  price?: number
  status: 'pending' | 'partially_filled' | 'filled' | 'cancelled' | 'rejected'
  createdAt: number
  updatedAt: number
  executedQuantity: number
  avgExecutionPrice?: number
}

interface ExecutionMetrics {
  totalOrders: number
  successRate: number
  avgExecutionTime: number
  slippage: {
    avg: number
    max: number
  }
  fillRate: number
}

export function OrderExecution() {
  const [pendingOrders] = useState<Order[]>([])
  const [recentOrders] = useState<Order[]>([])
  const [metrics] = useState<ExecutionMetrics>({
    totalOrders: 0,
    successRate: 0,
    avgExecutionTime: 0,
    slippage: {
      avg: 0,
      max: 0
    },
    fillRate: 0
  })

  // 已移除模拟数据生成逻辑，数据现在为初始静态状态

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 bg-yellow-400/10'
      case 'partially_filled': return 'text-blue-400 bg-blue-400/10'
      case 'filled': return 'text-green-400 bg-green-400/10'
      case 'cancelled': return 'text-gray-400 bg-gray-400/10'
      case 'rejected': return 'text-red-400 bg-red-400/10'
      default: return 'text-gray-400 bg-gray-400/10'
    }
  }

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending': return '待执行'
      case 'partially_filled': return '部分成交'
      case 'filled': return '完全成交'
      case 'cancelled': return '已取消'
      case 'rejected': return '被拒绝'
      default: return '未知'
    }
  }

  return (
    <div className="space-y-6">
      {/* 执行指标概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <h3 className="text-sm font-medium text-slate-400 mb-2">成功率</h3>
          <div className="text-3xl font-bold text-green-400 mb-1">
            {metrics.successRate.toFixed(1)}%
          </div>
          <div className="text-xs text-slate-400">
            总订单: {metrics.totalOrders}
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <h3 className="text-sm font-medium text-slate-400 mb-2">平均执行时间</h3>
          <div className="text-3xl font-bold text-blue-400 mb-1">
            {metrics.avgExecutionTime.toFixed(0)}ms
          </div>
          <div className={`text-xs ${
            metrics.avgExecutionTime < 100 ? 'text-green-400' : 
            metrics.avgExecutionTime < 200 ? 'text-yellow-400' : 'text-red-400'
          }`}>
            {metrics.avgExecutionTime < 100 ? '优秀' : 
             metrics.avgExecutionTime < 200 ? '良好' : '需优化'}
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <h3 className="text-sm font-medium text-slate-400 mb-2">平均滑点</h3>
          <div className="text-3xl font-bold text-yellow-400 mb-1">
            {metrics.slippage.avg.toFixed(3)}%
          </div>
          <div className="text-xs text-slate-400">
            最大: {metrics.slippage.max.toFixed(3)}%
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
          <h3 className="text-sm font-medium text-slate-400 mb-2">成交率</h3>
          <div className="text-3xl font-bold text-cyan-400 mb-1">
            {metrics.fillRate.toFixed(1)}%
          </div>
          <div className={`text-xs ${
            metrics.fillRate > 95 ? 'text-green-400' : 
            metrics.fillRate > 90 ? 'text-yellow-400' : 'text-red-400'
          }`}>
            {metrics.fillRate > 95 ? '优秀' : 
             metrics.fillRate > 90 ? '良好' : '需关注'}
          </div>
        </div>
      </div>

      {/* 待执行订单 */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            ⏳ 待执行订单
          </h2>
          <div className="text-sm text-slate-400">
            {pendingOrders.length} 个订单
          </div>
        </div>

        {pendingOrders.length > 0 ? (
          <div className="space-y-3">
            {pendingOrders.map((order) => (
              <div key={order.id} className="bg-slate-700/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-white">{order.symbol}</span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      order.side === 'buy' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                    }`}>
                      {order.side === 'buy' ? '买入' : '卖出'}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(order.status)}`}>
                      {getStatusText(order.status)}
                    </span>
                  </div>
                  
                  <div className="text-xs text-slate-400">
                    {new Date(order.createdAt).toLocaleTimeString()}
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="text-slate-400">类型</div>
                    <div className="text-white font-medium capitalize">{order.type}</div>
                  </div>
                  <div>
                    <div className="text-slate-400">数量</div>
                    <div className="text-white font-medium">{order.quantity.toFixed(4)}</div>
                  </div>
                  <div>
                    <div className="text-slate-400">价格</div>
                    <div className="text-white font-medium">
                      {order.price ? `$${order.price.toFixed(2)}` : '市价'}
                    </div>
                  </div>
                  <div>
                    <div className="text-slate-400">已成交</div>
                    <div className="text-white font-medium">
                      {order.executedQuantity.toFixed(4)} ({((order.executedQuantity / order.quantity) * 100).toFixed(1)}%)
                    </div>
                  </div>
                </div>

                {/* 进度条 */}
                {order.status === 'partially_filled' && (
                  <div className="mt-3">
                    <div className="w-full bg-slate-600 rounded-full h-2">
                      <div 
                        className="bg-blue-400 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(order.executedQuantity / order.quantity) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-slate-400 py-8">
            暂无待执行订单
          </div>
        )}
      </div>

      {/* 最近订单历史 */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
        <h2 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
          📋 最近订单
        </h2>

        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-slate-600">
                <th className="text-left text-slate-400 pb-3">时间</th>
                <th className="text-left text-slate-400 pb-3">交易对</th>
                <th className="text-left text-slate-400 pb-3">方向</th>
                <th className="text-left text-slate-400 pb-3">数量</th>
                <th className="text-left text-slate-400 pb-3">价格</th>
                <th className="text-left text-slate-400 pb-3">状态</th>
              </tr>
            </thead>
            <tbody className="space-y-2">
              {recentOrders.map((order) => (
                <tr key={order.id} className="border-b border-slate-700/50">
                  <td className="py-3 text-slate-300">
                    {new Date(order.updatedAt).toLocaleTimeString()}
                  </td>
                  <td className="py-3 text-white font-medium">{order.symbol}</td>
                  <td className="py-3">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      order.side === 'buy' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                    }`}>
                      {order.side === 'buy' ? '买入' : '卖出'}
                    </span>
                  </td>
                  <td className="py-3 text-white">{order.quantity.toFixed(4)}</td>
                  <td className="py-3 text-white">
                    {order.avgExecutionPrice ? `$${order.avgExecutionPrice.toFixed(2)}` : '-'}
                  </td>
                  <td className="py-3">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(order.status)}`}>
                      {getStatusText(order.status)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
} 