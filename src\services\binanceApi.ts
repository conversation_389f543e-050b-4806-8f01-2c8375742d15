// 币安API服务
export interface BinanceAccount {
  totalWalletBalance: number
  availableBalance: number
  unrealizedPnL: number
  positions: BinancePosition[]
}

export interface BinancePosition {
  symbol: string
  positionAmt: number
  entryPrice: number
  markPrice: number
  unRealizedProfit: number
  percentage: number
}

export interface BinanceTrade {
  symbol: string
  orderId: number
  side: 'BUY' | 'SELL'
  quantity: number
  price: number
  commission: number
  time: number
  realizedPnl: number
}

export interface LiveTradingMetrics {
  accountBalance: number
  availableBalance: number
  currentPosition: number
  unrealizedPnL: number
  realizedPnL: number
  todayTrades: number
  currentDrawdown: number
  maxDrawdownToday: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  isConnected: boolean
  isTrading: boolean
}

class BinanceApiService {
  private _apiKey: string = ''
  private _apiSecret: string = ''
  private isConnected: boolean = false
  private testMode: boolean = true // 测试模式
  private proxyConfig: { enabled: boolean; host: string; port: string } = {
    enabled: false,
    host: '127.0.0.1',
    port: '7890'
  }

  // 设置代理配置
  setProxyConfig(enabled: boolean, host: string = '127.0.0.1', port: string = '7890'): void {
    this.proxyConfig = { enabled, host, port }
  }

  // 获取代理配置
  getProxyConfig(): { enabled: boolean; host: string; port: string } {
    return { ...this.proxyConfig }
  }

  // 连接币安API
  async connect(apiKey: string, apiSecret: string, proxyConfig?: { enabled: boolean; host: string; port: string }): Promise<boolean> {
    try {
      this._apiKey = apiKey
      this._apiSecret = apiSecret
      
      if (proxyConfig) {
        this.proxyConfig = proxyConfig
      }
      
      if (this.testMode) {
        // 测试模式：模拟连接成功
        await new Promise(resolve => setTimeout(resolve, 2000))
        this.isConnected = true
        return true
      }
      
      // 实际连接逻辑（生产环境）
      // const response = await fetch('/api/binance/account', {
      //   headers: { 'X-MBX-APIKEY': apiKey }
      // })
      // this.isConnected = response.ok
      
      return this.isConnected
    } catch (error) {
      console.error('币安API连接失败:', error)
      return false
    }
  }

  // 断开连接
  disconnect(): void {
    this.isConnected = false
    this._apiKey = ''
    this._apiSecret = ''
  }

  // 获取账户信息
  async getAccountInfo(): Promise<BinanceAccount | null> {
    if (!this.isConnected) return null

    if (this.testMode) {
      // 测试模式：返回基础的空状态，等待真实API配置
      return {
        totalWalletBalance: 0,
        availableBalance: 0,
        unrealizedPnL: 0,
        positions: []
      }
    }

          // 实际API调用（生产环境）
      // try {
      //   const response = await fetch('/api/binance/account', {
      //     headers: { 'X-MBX-APIKEY': this._apiKey }
      //   })
      //   return await response.json()
      // } catch (error) {
      //   console.error('获取账户信息失败:', error)
      //   return null
      // }

    return null
  }

  // 获取今日交易记录
  async getTodayTrades(): Promise<BinanceTrade[]> {
    if (!this.isConnected) return []

    if (this.testMode) {
      // 测试模式：返回空交易记录，等待真实API配置
      return []
    }

    return []
  }

  // 计算实时风险指标
  async getLiveTradingMetrics(): Promise<LiveTradingMetrics | null> {
    if (!this.isConnected) {
      return {
        accountBalance: 0,
        availableBalance: 0,
        currentPosition: 0,
        unrealizedPnL: 0,
        realizedPnL: 0,
        todayTrades: 0,
        currentDrawdown: 0,
        maxDrawdownToday: 0,
        riskLevel: 'LOW',
        isConnected: false,
        isTrading: false
      }
    }

    try {
      const account = await this.getAccountInfo()
      const trades = await this.getTodayTrades()

      if (!account) return null

      // 计算今日已实现PnL
      const todayRealizedPnL = trades.reduce((sum, trade) => sum + trade.realizedPnl, 0)

      // 计算当前仓位占比
      const totalPositionValue = account.positions.reduce(
        (sum, pos) => sum + Math.abs(pos.positionAmt * pos.markPrice), 0
      )
      const positionPercentage = (totalPositionValue / account.totalWalletBalance) * 100

      // 计算当前回撤（简化版）
      const currentDrawdown = Math.max(0, -account.unrealizedPnL / account.totalWalletBalance * 100)

      // 风险等级评估
      let riskLevel: LiveTradingMetrics['riskLevel'] = 'LOW'
      if (currentDrawdown > 20 || positionPercentage > 80) riskLevel = 'CRITICAL'
      else if (currentDrawdown > 10 || positionPercentage > 60) riskLevel = 'HIGH'
      else if (currentDrawdown > 5 || positionPercentage > 40) riskLevel = 'MEDIUM'

      return {
        accountBalance: account.totalWalletBalance,
        availableBalance: account.availableBalance,
        currentPosition: positionPercentage,
        unrealizedPnL: account.unrealizedPnL,
        realizedPnL: todayRealizedPnL,
        todayTrades: trades.length,
        currentDrawdown,
        maxDrawdownToday: currentDrawdown, // 简化版，实际需要历史最大值
        riskLevel,
        isConnected: this.isConnected,
        isTrading: trades.length > 0 // 如果有交易记录说明在交易
      }
    } catch (error) {
      console.error('获取实时指标失败:', error)
      return null
    }
  }

  // 开始交易（启动策略）
  async startTrading(): Promise<boolean> {
    if (!this.isConnected) return false

    if (this.testMode) {
      console.log('策略终极版已启动（测试模式）')
      return true
    }

    // 实际启动策略逻辑
    return false
  }

  // 停止交易
  async stopTrading(): Promise<boolean> {
    if (!this.isConnected) return false

    if (this.testMode) {
      console.log('策略终极版已停止（测试模式）')
      return true
    }

    // 实际停止策略逻辑
    return false
  }

  // 紧急停止
  async emergencyStop(): Promise<boolean> {
    if (!this.isConnected) return false

    if (this.testMode) {
      console.log('紧急停止执行（测试模式）')
      return true
    }

    // 实际紧急停止逻辑：平仓所有头寸
    return false
  }

  // 获取连接状态
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  // 设置测试模式
  setTestMode(enabled: boolean): void {
    this.testMode = enabled
  }

  // 获取API配置状态（用于调试）
  getApiConfig(): { hasApiKey: boolean; hasSecret: boolean; isConnected: boolean } {
    return {
      hasApiKey: this._apiKey.length > 0,
      hasSecret: this._apiSecret.length > 0,
      isConnected: this.isConnected
    }
  }
}

// 导出单例实例
export const binanceApi = new BinanceApiService() 