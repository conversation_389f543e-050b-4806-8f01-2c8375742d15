import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import App from './App.tsx'
import './styles/globals.css'

// Main app render
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>,
)

// Stagewise toolbar integration (development only)
if (process.env.NODE_ENV === 'development') {
  import('@stagewise/toolbar-react').then(({ StagewiseToolbar }) => {
    // Create stagewise config
    const stagewiseConfig = {
      plugins: []
    }

    // Create a separate div for the toolbar
    const toolbarRoot = document.createElement('div')
    toolbarRoot.id = 'stagewise-toolbar-root'
    document.body.appendChild(toolbarRoot)

    // Render the toolbar in its own React root
    const toolbarReactRoot = ReactDOM.createRoot(toolbarRoot)
    toolbarReactRoot.render(
      <StagewiseToolbar config={stagewiseConfig} />
    )
  }).catch((error) => {
    console.warn('Failed to load stagewise toolbar:', error)
  })
} 