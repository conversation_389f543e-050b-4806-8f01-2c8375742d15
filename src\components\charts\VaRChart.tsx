interface VaRChartProps {
  currentVaR?: number;
}

export function VaRChart({ currentVaR = 0 }: VaRChartProps) {
  const targetVaR = 2.0
  const displayRiskLevel = currentVaR > targetVaR ? 'warning' : 'success'
  
  return (
    <div className="space-y-4">
      {/* VaR 指标显示 */}
      <div className="text-center space-y-2">
        <div className="space-y-1">
          <p className="text-2xl font-bold">
            <span className={currentVaR > targetVaR ? 'text-warning-500' : 'text-success-500'}>
              {currentVaR.toFixed(2)}%
            </span>
          </p>
          <p className="text-sm text-muted-foreground">95% VaR (日)</p>
        </div>
        
        {/* 风险等级指示 */}
        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${
          displayRiskLevel === 'warning' 
            ? 'bg-warning-100 text-warning-700' 
            : 'bg-success-100 text-success-700'
        }`}>
          <div className={`w-2 h-2 rounded-full ${
            displayRiskLevel === 'warning' ? 'bg-warning-500' : 'bg-success-500'
          }`}></div>
          {displayRiskLevel === 'warning' ? '中等风险' : '低风险'}
        </div>
      </div>

      {/* VaR 历史趋势 */}
      <div className="relative h-32 bg-muted/20 rounded-lg p-3">
        <div className="absolute inset-3">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            {/* 目标线 */}
            <line
              x1="0" y1="60" x2="100" y2="60"
              stroke="hsl(var(--muted-foreground))"
              strokeWidth="1"
              strokeDasharray="2,2"
            />
            {/* VaR趋势线 */}
            <polyline
              fill="none"
              stroke={currentVaR > targetVaR ? 'hsl(var(--warning))' : 'hsl(var(--success))'}
              strokeWidth="2"
              points="0,70 20,65 40,55 60,50 80,58 100,62"
            />
            {/* 填充区域 */}
            <polygon
              fill={currentVaR > targetVaR ? 'hsl(var(--warning) / 0.1)' : 'hsl(var(--success) / 0.1)'}
              points="0,100 0,70 20,65 40,55 60,50 80,58 100,62 100,100"
            />
          </svg>
        </div>
        
        {/* 图例 */}
        <div className="absolute bottom-1 left-3 text-xs text-muted-foreground">
          <span>7天趋势</span>
        </div>
      </div>

      {/* 风险提示 */}
      {currentVaR > targetVaR && (
        <div className="p-3 bg-warning-50/20 border border-warning-200/30 rounded-lg">
          <div className="flex items-start gap-2">
            <div className="w-4 h-4 mt-0.5 text-warning-500">⚠️</div>
            <div className="text-xs">
              <p className="font-medium text-warning-700">风险提醒</p>
              <p className="text-warning-600/80">当前VaR超过目标阈值，建议关注仓位风险</p>
            </div>
          </div>
        </div>
      )}

      {/* 详细统计 */}
      <div className="grid grid-cols-2 gap-3 text-xs">
        <div className="text-center p-2 bg-muted/30 rounded">
          <p className="font-semibold">99% VaR</p>
          <p className="text-muted-foreground">{(currentVaR * 1.5).toFixed(2)}%</p>
        </div>
        <div className="text-center p-2 bg-muted/30 rounded">
          <p className="font-semibold">目标阈值</p>
          <p className="text-muted-foreground">{targetVaR.toFixed(1)}%</p>
        </div>
      </div>
    </div>
  )
} 