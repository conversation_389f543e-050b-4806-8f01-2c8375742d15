import React from 'react'
import { Sidebar } from './Sidebar'
import { Header } from './Header'

interface AppLayoutProps {
  children: React.ReactNode
}

export function AppLayout({ children }: AppLayoutProps) {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="flex h-screen">
        {/* 左侧导航栏 */}
        <Sidebar />
        
        {/* 右侧内容区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 顶部状态栏 */}
          <Header />
          
          {/* 主内容区 */}
          <main className="flex-1 overflow-y-auto p-6 scrollbar-thin">
            <div className="w-full space-y-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  )
} 