import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { MetricCard } from '../common/MetricCard'
import { DollarSign } from 'lucide-react'

describe('MetricCard', () => {
  it('renders title and value correctly', () => {
    render(
      <MetricCard
        title="测试指标"
        value={12345.67}
        icon={DollarSign}
      />
    )

    expect(screen.getByText('测试指标')).toBeInTheDocument()
    expect(screen.getByText('$12,345.67')).toBeInTheDocument()
  })

  it('renders change with correct color for positive value', () => {
    render(
      <MetricCard
        title="收益率"
        value="5.67%"
        change={123.45}
        changeType="currency"
      />
    )

    const changeElement = screen.getByText('+$123.45')
    expect(changeElement).toBeInTheDocument()
    expect(changeElement).toHaveClass('text-success-500')
  })

  it('renders change with correct color for negative value', () => {
    render(
      <MetricCard
        title="回撤"
        value="-2.34%"
        change={-45.67}
        changeType="currency"
      />
    )

    const changeElement = screen.getByText('-$45.67')
    expect(changeElement).toBeInTheDocument()
    expect(changeElement).toHaveClass('text-danger-500')
  })

  it('renders description when provided', () => {
    render(
      <MetricCard
        title="夏普比率"
        value="1.45"
        description="风险调整后收益"
      />
    )

    expect(screen.getByText('风险调整后收益')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(
      <MetricCard
        title="自定义样式"
        value="测试"
        className="custom-class"
      />
    )

    const cardElement = container.firstChild
    expect(cardElement).toHaveClass('custom-class')
  })
}) 