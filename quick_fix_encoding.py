#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复Unicode编码问题
"""

import os
import re

def fix_unicode_in_file(filepath):
    """修复文件中的Unicode编码问题"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换Unicode emoji为简单文本
        replacements = {
            '🧪': '[TEST]',
            '🛡️': '[SHIELD]', 
            '🎯': '[TARGET]',
            '🔬': '[SCOPE]',
            '✅': '[OK]',
            '⚠️': '[WARN]',
            '🚀': '[ROCKET]',
            '📊': '[CHART]',
            '🔥': '[FIRE]',
            '🗳️': '[VOTE]',
            '🌲': '[TREE]',
            '🔗': '[LINK]',
            '🔄': '[CYCLE]',
            '🏆': '[TROPHY]',
            '🚶‍♂️': '[WALK]',
            '🎲': '[DICE]',
            '📋': '[BOARD]'
        }
        
        for emoji, replacement in replacements.items():
            content = content.replace(emoji, replacement)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
            
        print(f"[OK] 修复完成: {filepath}")
        return True
        
    except Exception as e:
        print(f"[ERROR] 修复失败: {filepath} - {e}")
        return False

def main():
    """主函数"""
    print("开始修复Unicode编码问题...")
    
    files_to_fix = [
        'improved_cost_model.py',
        'gap_risk_protection.py',
        'feature_selection_optimizer.py', 
        'robustness_validator.py'
    ]
    
    success_count = 0
    for filepath in files_to_fix:
        if os.path.exists(filepath):
            if fix_unicode_in_file(filepath):
                success_count += 1
        else:
            print(f"[ERROR] 文件不存在: {filepath}")
    
    print(f"\n修复完成: {success_count}/{len(files_to_fix)} 个文件")
    print("现在可以重新运行测试了!")

if __name__ == "__main__":
    main() 