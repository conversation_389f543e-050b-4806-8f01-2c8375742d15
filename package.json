{"name": "quant-trading-monitor", "private": true, "version": "1.0.0", "type": "module", "description": "量化交易监控系统前端", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.6.0", "lucide-react": "^0.300.0", "plotly.js": "^2.27.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^6.21.0", "recharts": "^2.10.3", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.7"}, "devDependencies": {"@stagewise/toolbar-react": "^0.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/plotly.js": "^2.12.29", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.1.0", "@vitest/ui": "^1.1.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.1.0"}}