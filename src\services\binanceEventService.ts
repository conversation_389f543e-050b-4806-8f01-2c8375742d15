// 币安WebSocket事件监听服务 - 符合官方事件标准
import { accountService } from './accountService'

// ORDER_TRADE_UPDATE 事件接口 - 符合币安官方标准
export interface OrderUpdateEvent {
  e: "ORDER_TRADE_UPDATE"        // 事件类型
  E: number                      // 事件时间
  T: number                      // 撮合时间
  o: {
    s: string                    // 交易对
    c: string                    // 客户端订单ID
    S: "BUY" | "SELL"           // 订单方向
    o: string                    // 订单类型
    f: string                    // 有效方式
    q: string                    // 订单原始数量
    p: string                    // 订单原始价格
    ap: string                   // 订单平均价格
    sp: string                   // 条件订单触发价格
    x: string                    // 本次事件的具体执行类型
    X: string                    // 订单的当前状态
    i: number                    // 订单ID
    l: string                    // 订单末次成交量
    z: string                    // 订单累计成交量
    L: string                    // 订单末次成交价格
    n: string                    // 手续费
    N: string                    // 手续费资产类别
    T: number                    // 成交时间
    t: number                    // 成交ID
    b: string                    // 买单净值
    a: string                    // 卖单净值
    m: boolean                   // 该成交是作为挂单成交吗?
    R: boolean                   // 是否是只减仓单
    wt: string                   // 触发价类型
    ot: string                   // 原始订单类型
    ps: string                   // 持仓方向
    cp: boolean                  // 是否为触发平仓单
    AP: string                   // 追踪止损激活价格
    cr: string                   // 追踪止损回调比率
    rp: string                   // 该交易实现盈亏
  }
}

// ACCOUNT_UPDATE 事件接口 - 符合币安官方标准
export interface AccountUpdateEvent {
  e: "ACCOUNT_UPDATE"            // 事件类型
  E: number                      // 事件时间
  T: number                      // 撮合时间
  a: {
    m: string                    // 事件推出原因
    B: Array<{                   // 余额信息
      a: string                  // 资产名称
      wb: string                 // 钱包余额
      cw: string                 // 全仓余额
      bc: string                 // 除去盈亏与手续费的余额变化量
    }>
    P: Array<{                   // 持仓信息
      s: string                  // 交易对
      pa: string                 // 持仓数量
      ep: string                 // 持仓成本价
      bep: string                // 盈亏平衡价
      cr: string                 // (费前)累计实现损益
      up: string                 // 持仓未实现盈亏
      mt: string                 // 保证金模式
      iw: string                 // 若为逐仓，仓位保证金
      ps: string                 // 持仓方向
    }>
  }
}

// MARGIN_CALL 事件接口
export interface MarginCallEvent {
  e: "MARGIN_CALL"               // 事件类型
  E: number                      // 事件时间
  cw: string                     // 全仓钱包余额
  p: Array<{                     // 持仓信息
    s: string                    // 交易对
    ps: string                   // 持仓方向
    pa: string                   // 持仓数量
    mt: string                   // 保证金模式
    iw: string                   // 若为逐仓，仓位保证金
    mp: string                   // 标记价格
    up: string                   // 持仓未实现盈亏
    mm: string                   // 维持保证金
  }>
}

// ACCOUNT_CONFIG_UPDATE 事件接口
export interface AccountConfigUpdateEvent {
  e: "ACCOUNT_CONFIG_UPDATE"     // 事件类型
  E: number                      // 事件时间
  T: number                      // 撮合时间
  ac?: {                         // 账户配置
    s: string                    // 交易对
    l: number                    // 杠杆倍数
  }
  ai?: {                         // 账户配置
    j: boolean                   // 联合保证金状态
  }
}

// listenKey 响应接口
export interface ListenKeyResponse {
  listenKey: string
}

// 事件回调类型
export type OrderUpdateCallback = (event: OrderUpdateEvent) => void
export type AccountUpdateCallback = (event: AccountUpdateEvent) => void
export type MarginCallCallback = (event: MarginCallEvent) => void
export type AccountConfigUpdateCallback = (event: AccountConfigUpdateEvent) => void

class BinanceEventService {
  private baseURL: string = 'https://fapi.binance.com'
  private wsURL: string = 'wss://fstream.binance.com/ws'
  private listenKey: string | null = null
  private ws: WebSocket | null = null
  private isConnected: boolean = false
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 10
  private listenKeyInterval: NodeJS.Timeout | null = null
  
  // 事件回调存储
  private orderUpdateCallbacks: OrderUpdateCallback[] = []
  private accountUpdateCallbacks: AccountUpdateCallback[] = []
  private marginCallCallbacks: MarginCallCallback[] = []
  private accountConfigUpdateCallbacks: AccountConfigUpdateCallback[] = []

  constructor() {
    console.log('🎧 币安事件监听服务初始化')
  }

  // 1. 创建 listenKey
  async createListenKey(): Promise<string> {
    try {
      const config = accountService.getSystemConfigStatus()
      if (!config.isValid) {
        throw new Error('API凭证未配置')
      }

      const savedConfig = localStorage.getItem('binance_api_config')
      if (!savedConfig) {
        throw new Error('无法获取API配置')
      }

      const apiConfig = JSON.parse(savedConfig)
      const axiosConfig = {
        timeout: 30000,
        headers: {
          'X-MBX-APIKEY': apiConfig.apiKey,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }

      // 添加代理配置
      if (config.useProxy && config.proxyHost && config.proxyPort) {
        (axiosConfig as any).proxy = {
          protocol: 'http',
          host: config.proxyHost,
          port: parseInt(config.proxyPort)
        }
      }

      const axios = (await import('axios')).default
      const response = await axios.post(`${this.baseURL}/fapi/v1/listenKey`, '', axiosConfig)
      
      this.listenKey = response.data.listenKey
      console.log('✅ listenKey 创建成功:', this.listenKey?.substring(0, 8) + '***')
      
      return this.listenKey!
    } catch (error: any) {
      console.error('❌ 创建 listenKey 失败:', error)
      if (error.response) {
        console.error('🚫 API响应:', error.response.status, error.response.data)
      }
      throw error
    }
  }

  // 2. 延长 listenKey 有效期
  async extendListenKey(listenKey?: string): Promise<boolean> {
    try {
      const keyToExtend = listenKey || this.listenKey
      if (!keyToExtend) {
        throw new Error('没有可用的 listenKey')
      }

      const config = accountService.getSystemConfigStatus()
      if (!config.isValid) {
        throw new Error('API凭证未配置')
      }

      const savedConfig = localStorage.getItem('binance_api_config')
      if (!savedConfig) {
        throw new Error('无法获取API配置')
      }

      const apiConfig = JSON.parse(savedConfig)
      const axiosConfig = {
        timeout: 30000,
        headers: {
          'X-MBX-APIKEY': apiConfig.apiKey,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }

      // 添加代理配置
      if (config.useProxy && config.proxyHost && config.proxyPort) {
        (axiosConfig as any).proxy = {
          protocol: 'http',
          host: config.proxyHost,
          port: parseInt(config.proxyPort)
        }
      }

      const axios = (await import('axios')).default
      await axios.put(`${this.baseURL}/fapi/v1/listenKey`, '', axiosConfig)
      
      console.log('✅ listenKey 延长成功')
      return true
    } catch (error: any) {
      console.error('❌ 延长 listenKey 失败:', error)
      return false
    }
  }

  // 3. 删除 listenKey
  async deleteListenKey(listenKey?: string): Promise<boolean> {
    try {
      const keyToDelete = listenKey || this.listenKey
      if (!keyToDelete) {
        return true // 没有 listenKey 就不需要删除
      }

      const config = accountService.getSystemConfigStatus()
      if (!config.isValid) {
        throw new Error('API凭证未配置')
      }

      const savedConfig = localStorage.getItem('binance_api_config')
      if (!savedConfig) {
        throw new Error('无法获取API配置')
      }

      const apiConfig = JSON.parse(savedConfig)
      const axiosConfig = {
        timeout: 30000,
        headers: {
          'X-MBX-APIKEY': apiConfig.apiKey,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }

      // 添加代理配置
      if (config.useProxy && config.proxyHost && config.proxyPort) {
        (axiosConfig as any).proxy = {
          protocol: 'http',
          host: config.proxyHost,
          port: parseInt(config.proxyPort)
        }
      }

      const axios = (await import('axios')).default
      await axios.delete(`${this.baseURL}/fapi/v1/listenKey`, axiosConfig)
      
      console.log('✅ listenKey 删除成功')
      this.listenKey = null
      return true
    } catch (error: any) {
      console.error('❌ 删除 listenKey 失败:', error)
      return false
    }
  }

  // 4. 启动事件监听
  async startEventListening(): Promise<boolean> {
    try {
      // 确保有有效的 listenKey
      if (!this.listenKey) {
        await this.createListenKey()
      }

      if (!this.listenKey) {
        throw new Error('无法获取 listenKey')
      }

      // 连接 WebSocket
      await this.connectWebSocket()

      // 启动 listenKey 维护（每30分钟延长一次）
      this.startListenKeyMaintenance()

      console.log('✅ 事件监听服务启动成功')
      return true
    } catch (error) {
      console.error('❌ 启动事件监听失败:', error)
      return false
    }
  }

  // 5. 停止事件监听
  async stopEventListening(): Promise<void> {
    console.log('⏹️ 停止事件监听服务')

    // 停止 listenKey 维护
    if (this.listenKeyInterval) {
      clearInterval(this.listenKeyInterval)
      this.listenKeyInterval = null
    }

    // 关闭 WebSocket 连接
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }

    // 删除 listenKey
    await this.deleteListenKey()

    this.isConnected = false
    this.reconnectAttempts = 0
  }

  // 6. 连接 WebSocket
  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.listenKey) {
        reject(new Error('listenKey 未创建'))
        return
      }

      const wsUrl = `${this.wsURL}/${this.listenKey}`
      console.log('🔗 连接用户数据流 WebSocket:', wsUrl.replace(this.listenKey, this.listenKey.substring(0, 8) + '***'))

      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = () => {
        console.log('✅ 用户数据流 WebSocket 连接成功')
        this.isConnected = true
        this.reconnectAttempts = 0
        resolve()
      }

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleWebSocketMessage(data)
        } catch (error) {
          console.error('❌ WebSocket 消息解析错误:', error)
        }
      }

      this.ws.onclose = (event) => {
        console.log(`🔌 WebSocket 连接关闭: code=${event.code}, reason=${event.reason}`)
        this.isConnected = false
        
        // 尝试重连（除非是主动关闭）
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.attemptReconnect()
        }
      }

      this.ws.onerror = (error) => {
        console.error('❌ WebSocket 连接错误:', error)
        reject(error)
      }

      // 超时处理
      const timeoutId = setTimeout(() => {
        if (!this.isConnected) {
          console.error('❌ WebSocket 连接超时，清理资源')
          if (this.ws) {
            this.ws.close()
            this.ws = null
          }
          reject(new Error('WebSocket 连接超时'))
        }
      }, 10000)

      // 连接成功后清除超时
      const originalOnOpen = this.ws.onopen
      this.ws.onopen = (event) => {
        clearTimeout(timeoutId)
        if (originalOnOpen && this.ws) {
          originalOnOpen.call(this.ws, event)
        }
      }
    })
  }

  // 7. 处理 WebSocket 消息
  private handleWebSocketMessage(data: any): void {
    try {
      switch (data.e) {
        case 'ORDER_TRADE_UPDATE':
          console.log('📦 收到订单更新事件:', data)
          this.orderUpdateCallbacks.forEach(callback => {
            try {
              callback(data as OrderUpdateEvent)
            } catch (error) {
              console.error('❌ 订单更新回调执行失败:', error)
            }
          })
          break

        case 'ACCOUNT_UPDATE':
          console.log('💰 收到账户更新事件:', data)
          this.accountUpdateCallbacks.forEach(callback => {
            try {
              callback(data as AccountUpdateEvent)
            } catch (error) {
              console.error('❌ 账户更新回调执行失败:', error)
            }
          })
          break

        case 'MARGIN_CALL':
          console.log('⚠️ 收到保证金通知事件:', data)
          this.marginCallCallbacks.forEach(callback => {
            try {
              callback(data as MarginCallEvent)
            } catch (error) {
              console.error('❌ 保证金通知回调执行失败:', error)
            }
          })
          break

        case 'ACCOUNT_CONFIG_UPDATE':
          console.log('⚙️ 收到账户配置更新事件:', data)
          this.accountConfigUpdateCallbacks.forEach(callback => {
            try {
              callback(data as AccountConfigUpdateEvent)
            } catch (error) {
              console.error('❌ 账户配置更新回调执行失败:', error)
            }
          })
          break

        default:
          console.log('📨 收到未知事件类型:', data.e, data)
      }
    } catch (error) {
      console.error('❌ 处理 WebSocket 消息失败:', error)
    }
  }

  // 8. 尝试重连
  private async attemptReconnect(): Promise<void> {
    this.reconnectAttempts++
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000) // 指数退避，最大30秒

    console.log(`🔄 尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，${delay/1000}秒后重试...`)

    setTimeout(async () => {
      try {
        // 重新创建 listenKey（旧的可能已失效）
        await this.createListenKey()
        await this.connectWebSocket()
      } catch (error) {
        console.error('❌ 重连失败:', error)
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.attemptReconnect()
        } else {
          console.error('❌ 重连次数已达上限，停止重连')
        }
      }
    }, delay)
  }

  // 9. 启动 listenKey 维护
  private startListenKeyMaintenance(): void {
    // 每30分钟延长一次 listenKey（币安要求每60分钟延长一次，我们提前一半时间）
    this.listenKeyInterval = setInterval(async () => {
      console.log('🔄 维护 listenKey...')
      const success = await this.extendListenKey()
      if (!success) {
        console.warn('⚠️ listenKey 维护失败，尝试重新创建...')
        try {
          await this.createListenKey()
          if (this.ws) {
            this.ws.close()
          }
          await this.connectWebSocket()
        } catch (error) {
          console.error('❌ listenKey 重新创建失败:', error)
        }
      }
    }, 30 * 60 * 1000) // 30分钟
  }

  // 10. 注册事件回调
  onOrderUpdate(callback: OrderUpdateCallback): void {
    this.orderUpdateCallbacks.push(callback)
  }

  onAccountUpdate(callback: AccountUpdateCallback): void {
    this.accountUpdateCallbacks.push(callback)
  }

  onMarginCall(callback: MarginCallCallback): void {
    this.marginCallCallbacks.push(callback)
  }

  onAccountConfigUpdate(callback: AccountConfigUpdateCallback): void {
    this.accountConfigUpdateCallbacks.push(callback)
  }

  // 11. 移除事件回调
  removeOrderUpdateCallback(callback: OrderUpdateCallback): void {
    const index = this.orderUpdateCallbacks.indexOf(callback)
    if (index > -1) {
      this.orderUpdateCallbacks.splice(index, 1)
    }
  }

  removeAccountUpdateCallback(callback: AccountUpdateCallback): void {
    const index = this.accountUpdateCallbacks.indexOf(callback)
    if (index > -1) {
      this.accountUpdateCallbacks.splice(index, 1)
    }
  }

  removeMarginCallCallback(callback: MarginCallCallback): void {
    const index = this.marginCallCallbacks.indexOf(callback)
    if (index > -1) {
      this.marginCallCallbacks.splice(index, 1)
    }
  }

  removeAccountConfigUpdateCallback(callback: AccountConfigUpdateCallback): void {
    const index = this.accountConfigUpdateCallbacks.indexOf(callback)
    if (index > -1) {
      this.accountConfigUpdateCallbacks.splice(index, 1)
    }
  }

  // 12. 清除所有回调
  clearAllCallbacks(): void {
    this.orderUpdateCallbacks = []
    this.accountUpdateCallbacks = []
    this.marginCallCallbacks = []
    this.accountConfigUpdateCallbacks = []
  }

  // 13. 获取连接状态
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  // 14. 获取当前 listenKey
  getCurrentListenKey(): string | null {
    return this.listenKey
  }

  // 15. 获取统计信息
  getStats(): {
    isConnected: boolean
    listenKey: string | null
    reconnectAttempts: number
    callbackCounts: {
      orderUpdate: number
      accountUpdate: number
      marginCall: number
      accountConfigUpdate: number
    }
  } {
    return {
      isConnected: this.isConnected,
      listenKey: this.listenKey ? this.listenKey.substring(0, 8) + '***' : null,
      reconnectAttempts: this.reconnectAttempts,
      callbackCounts: {
        orderUpdate: this.orderUpdateCallbacks.length,
        accountUpdate: this.accountUpdateCallbacks.length,
        marginCall: this.marginCallCallbacks.length,
        accountConfigUpdate: this.accountConfigUpdateCallbacks.length
      }
    }
  }
}

// 导出单例
export const binanceEventService = new BinanceEventService()
export default binanceEventService 