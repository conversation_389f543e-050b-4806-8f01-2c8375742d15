// 🔍 WebSocket实时诊断脚本 - 在浏览器控制台中运行
// 复制此代码到浏览器开发者工具的控制台中执行

console.log('🚀 开始WebSocket实时诊断...')

// 保存原始价格，用于检测变化
let lastPrice = null
let priceUpdateCount = 0
let diagnosticInterval = null

// 开始实时监控
function startRealTimeDiagnosis() {
  console.log('📡 启动实时WebSocket监控...')
  
  // 1. 强制确保localStorage设置正确
  localStorage.setItem('trading_status', 'active')
  localStorage.setItem('selected_symbol', 'BTCUSDT')
  console.log('✅ localStorage状态已设置')
  
  // 2. 强制重连全局价格服务
  if (typeof globalPriceService !== 'undefined') {
    console.log('🔄 强制重连全局价格服务...')
    globalPriceService.reconnect()
  }
  
  // 3. 启动实时监控
  diagnosticInterval = setInterval(() => {
    console.group(`📊 实时诊断 [${new Date().toLocaleTimeString()}]`)
    
    try {
      // 检查全局价格服务状态
      if (typeof globalPriceService !== 'undefined') {
        const currentPrice = globalPriceService.getCurrentPrice()
        const isConnected = globalPriceService.isConnected()
        const currentSymbol = globalPriceService.getCurrentSymbol()
        const currentState = globalPriceService.getCurrentState()
        
        console.log('🌐 全局价格服务状态:')
        console.log(`  连接状态: ${isConnected ? '✅ 已连接' : '❌ 未连接'}`)
        console.log(`  当前币种: ${currentSymbol || '未设置'}`)
        console.log(`  当前价格: $${currentPrice.toFixed(2)}`)
        console.log(`  最后更新: ${new Date(currentState.lastUpdate).toLocaleTimeString()}`)
        
        // 检测价格变化
        if (lastPrice === null) {
          lastPrice = currentPrice
          console.log('📌 设置基准价格:', lastPrice)
        } else if (lastPrice !== currentPrice) {
          priceUpdateCount++
          console.log(`🎯 价格变化检测 #${priceUpdateCount}: ${lastPrice} → ${currentPrice}`)
          lastPrice = currentPrice
        } else {
          console.log('⚠️ 价格未变化，仍为:', currentPrice)
        }
      } else {
        console.error('❌ globalPriceService未定义')
      }
      
      // 检查WebSocket服务状态
      if (typeof binanceWebSocket !== 'undefined') {
        const wsStatus = binanceWebSocket.getConnectionStatus()
        const activeSymbol = binanceWebSocket.getActiveSymbol()
        const allConnections = binanceWebSocket.getAllConnectionStatus()
        
        console.log('🔌 WebSocket服务状态:')
        console.log(`  连接状态: ${wsStatus ? '✅ 已连接' : '❌ 未连接'}`)
        console.log(`  活跃币种: ${activeSymbol || '未设置'}`)
        console.log('  所有连接:', allConnections)
        
        // 检查具体的ticker连接
        const tickerConnection = allConnections['btcusdt@ticker']
        if (tickerConnection === undefined) {
          console.warn('⚠️ 未找到BTCUSDT ticker连接')
        } else {
          console.log(`  BTCUSDT Ticker: ${tickerConnection ? '✅ 活跃' : '❌ 断开'}`)
        }
      } else {
        console.error('❌ binanceWebSocket未定义')
      }
      
      // 检查localStorage
      const tradingStatus = localStorage.getItem('trading_status')
      const selectedSymbol = localStorage.getItem('selected_symbol')
      console.log('💾 localStorage状态:')
      console.log(`  trading_status: ${tradingStatus}`)
      console.log(`  selected_symbol: ${selectedSymbol}`)
      
    } catch (error) {
      console.error('❌ 诊断过程中出错:', error)
    }
    
    console.groupEnd()
  }, 3000) // 每3秒检查一次
  
  console.log('✅ 实时监控已启动，每3秒输出一次诊断信息')
  console.log('💡 停止监控: stopRealTimeDiagnosis()')
}

// 停止实时监控
function stopRealTimeDiagnosis() {
  if (diagnosticInterval) {
    clearInterval(diagnosticInterval)
    diagnosticInterval = null
    console.log('⏹️ 实时监控已停止')
  }
}

// 手动测试WebSocket连接
function testWebSocketConnection() {
  console.log('🧪 开始手动测试WebSocket连接...')
  
  const testWs = new WebSocket('wss://fstream.binance.com/ws/btcusdt@ticker')
  
  testWs.onopen = () => {
    console.log('✅ 直接WebSocket连接成功!')
    console.log('🔗 连接地址: wss://fstream.binance.com/ws/btcusdt@ticker')
  }
  
  testWs.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      if (data.e === '24hrTicker') {
        console.log('📨 收到实时Ticker数据:', {
          symbol: data.s,
          price: parseFloat(data.c),
          bid: parseFloat(data.b),
          ask: parseFloat(data.a),
          time: new Date(data.E).toLocaleTimeString()
        })
        
        // 只接收一条消息就关闭，证明连接正常
        testWs.close()
        console.log('✅ WebSocket连接测试成功，数据流正常!')
      }
    } catch (error) {
      console.error('❌ 数据解析错误:', error)
    }
  }
  
  testWs.onerror = (error) => {
    console.error('❌ WebSocket连接错误:', error)
  }
  
  testWs.onclose = () => {
    console.log('🔌 测试WebSocket连接已关闭')
  }
  
  // 10秒后自动关闭
  setTimeout(() => {
    if (testWs.readyState === WebSocket.OPEN) {
      testWs.close()
      console.log('⏰ 测试超时，连接已关闭')
    }
  }, 10000)
}

// 强制刷新价格数据
function forceRefreshPrice() {
  console.log('🔄 强制刷新价格数据...')
  
  // 1. 重置localStorage
  localStorage.setItem('trading_status', 'active')
  localStorage.setItem('selected_symbol', 'BTCUSDT')
  
  // 2. 重连全局价格服务
  if (typeof globalPriceService !== 'undefined') {
    globalPriceService.reconnect()
    console.log('✅ 全局价格服务已重连')
  }
  
  // 3. 等待2秒后检查结果
  setTimeout(() => {
    if (typeof globalPriceService !== 'undefined') {
      const currentPrice = globalPriceService.getCurrentPrice()
      const isConnected = globalPriceService.isConnected()
      console.log('📊 刷新结果:')
      console.log(`  连接状态: ${isConnected ? '✅ 已连接' : '❌ 未连接'}`)
      console.log(`  当前价格: $${currentPrice.toFixed(2)}`)
      console.log(`  更新时间: ${new Date().toLocaleTimeString()}`)
      
      if (isConnected && currentPrice > 0) {
        console.log('✅ 价格刷新成功!')
      } else {
        console.warn('⚠️ 价格刷新可能失败，请检查网络连接')
      }
    }
  }, 2000)
}

// 导出到全局作用域
window.startRealTimeDiagnosis = startRealTimeDiagnosis
window.stopRealTimeDiagnosis = stopRealTimeDiagnosis
window.testWebSocketConnection = testWebSocketConnection
window.forceRefreshPrice = forceRefreshPrice

console.log('🎯 WebSocket诊断工具已准备就绪!')
console.log('📋 可用命令:')
console.log('  startRealTimeDiagnosis() - 开始实时监控')
console.log('  stopRealTimeDiagnosis() - 停止实时监控')
console.log('  testWebSocketConnection() - 测试WebSocket连接')
console.log('  forceRefreshPrice() - 强制刷新价格')
console.log('')
console.log('💡 建议执行顺序:')
console.log('  1. testWebSocketConnection() - 测试网络连接')
console.log('  2. forceRefreshPrice() - 强制刷新价格')
console.log('  3. startRealTimeDiagnosis() - 开始实时监控') 