// 币安订单服务 - 符合官方API标准
import axios from 'axios'
import { accountService } from './accountService'

// 订单参数接口 - 符合币安官方标准
export interface NewOrderParams {
  symbol: string                 // 交易对
  side: "BUY" | "SELL"          // 买卖方向
  type: "LIMIT" | "MARKET" | "STOP" | "STOP_MARKET" | "TAKE_PROFIT" | "TAKE_PROFIT_MARKET" | "TRAILING_STOP_MARKET"
  quantity?: string              // 下单数量
  price?: string                 // 委托价格
  timeInForce?: "GTC" | "IOC" | "FOK" | "GTX"
  positionSide?: "BOTH" | "LONG" | "SHORT"
  reduceOnly?: boolean           // true, 只减仓模式
  newClientOrderId?: string      // 客户端订单ID
  stopPrice?: string             // 触发价
  closePosition?: boolean        // true, 全部平仓
  activationPrice?: string       // 追踪止损激活价格
  callbackRate?: string          // 追踪止损回调比率
  workingType?: "MARK_PRICE" | "CONTRACT_PRICE"
  priceProtect?: boolean         // 是否开启价格保护
  newOrderRespType?: "ACK" | "RESULT"
  timestamp?: number
  signature?: string
}

// 订单响应接口 - 符合币安官方标准
export interface OrderResponse {
  orderId: number
  symbol: string
  status: "NEW" | "PARTIALLY_FILLED" | "FILLED" | "CANCELED" | "REJECTED" | "EXPIRED"
  clientOrderId: string
  price: string
  avgPrice: string
  origQty: string
  executedQty: string
  cumQuote: string
  timeInForce: string
  type: string
  reduceOnly: boolean
  closePosition: boolean
  side: string
  positionSide: string
  stopPrice: string
  workingType: string
  priceProtect: boolean
  origType: string
  updateTime: number
}

// 订单查询响应
export interface OrderInfo extends OrderResponse {
  time: number
  goodTillDate?: number
}

// 取消订单响应
export interface CancelResponse {
  orderId: number
  symbol: string
  status: string
  clientOrderId: string
  price: string
  avgPrice: string
  origQty: string
  executedQty: string
  cumQuote: string
  timeInForce: string
  type: string
  reduceOnly: boolean
  closePosition: boolean
  side: string
  positionSide: string
  stopPrice: string
  workingType: string
  priceProtect: boolean
  origType: string
  updateTime: number
}

// 批量订单响应
export interface BatchOrderResponse {
  orders: OrderResponse[]
  errors: Array<{
    code: number
    msg: string
    data?: any
  }>
}

class BinanceOrderService {
  private baseURL: string = 'https://fapi.binance.com'
  private isConnected: boolean = false

  constructor() {
    // 检查账户服务连接状态
    this.isConnected = accountService.getConnectionStatus()
  }

  // 生成API签名
  private async generateSignature(queryString: string): Promise<string> {
    try {
      const config = accountService.getSystemConfigStatus()
      if (!config.isValid) {
        throw new Error('API凭证未配置')
      }

      const savedConfig = localStorage.getItem('binance_api_config')
      if (!savedConfig) {
        throw new Error('无法获取API配置')
      }

      const apiConfig = JSON.parse(savedConfig)
      const secretKey = apiConfig.secretKey

      if (!secretKey) {
        throw new Error('Secret Key未配置')
      }

      const encoder = new TextEncoder()
      const keyData = encoder.encode(secretKey)
      const messageData = encoder.encode(queryString)

      const cryptoKey = await crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'HMAC', hash: 'SHA-256' },
        false,
        ['sign']
      )

      const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData)
      return Array.from(new Uint8Array(signature))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')
    } catch (error) {
      console.error('❌ API签名生成失败:', error)
      throw new Error(`签名生成失败: ${error}`)
    }
  }

  // 获取API配置
  private getApiConfig(): any {
    const systemConfig = accountService.getSystemConfigStatus()
    
    const baseConfig: any = {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-MBX-APIKEY': ''
      }
    }

    try {
      const savedConfig = localStorage.getItem('binance_api_config')
      if (savedConfig) {
        const apiConfig = JSON.parse(savedConfig)
        baseConfig.headers['X-MBX-APIKEY'] = apiConfig.apiKey || ''
        
        // 代理配置
        if (systemConfig.useProxy && systemConfig.proxyHost && systemConfig.proxyPort) {
          baseConfig.proxy = {
            protocol: 'http',
            host: systemConfig.proxyHost,
            port: parseInt(systemConfig.proxyPort),
            auth: undefined
          }
        }
      }
    } catch (error) {
      console.error('获取API配置失败:', error)
    }

    return baseConfig
  }

  // 通用API请求方法
  private async apiRequest(method: 'GET' | 'POST' | 'DELETE', endpoint: string, params: any = {}): Promise<any> {
    if (!this.isConnected) {
      throw new Error('账户服务未连接，请先连接币安账户')
    }

    try {
      const timestamp = Date.now()
      const queryParams = { ...params, timestamp }
      
      // 生成查询字符串
      const queryString = Object.keys(queryParams)
        .sort()
        .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
        .join('&')
      
      // 生成签名
      const signature = await this.generateSignature(queryString)
      queryParams.signature = signature

      const config = this.getApiConfig()
      const url = `${this.baseURL}${endpoint}`

      let response: any
      if (method === 'GET' || method === 'DELETE') {
        response = await axios({
          method,
          url,
          params: queryParams,
          ...config
        })
      } else if (method === 'POST') {
        const formData = new URLSearchParams()
        Object.keys(queryParams).forEach(key => {
          formData.append(key, queryParams[key])
        })
        
        response = await axios({
          method,
          url,
          data: formData,
          ...config
        })
      }

      return response.data
    } catch (error: any) {
      console.error(`❌ API请求失败 [${method} ${endpoint}]:`, error)
      
      if (error.response) {
        const { status, data } = error.response
        console.error(`🚫 HTTP ${status}:`, data)
        
        // 处理常见错误
        if (status === 401) {
          throw new Error('认证失败：API Key或签名错误')
        } else if (status === 403) {
          throw new Error('权限不足：API Key没有期货交易权限')
        } else if (status === 429) {
          throw new Error('请求频率限制：请稍后重试')
        } else if (data?.code) {
          throw new Error(`币安API错误 ${data.code}: ${data.msg}`)
        }
      }
      
      throw error
    }
  }

  // 1. 下单 - 核心功能
  async placeOrder(params: NewOrderParams): Promise<OrderResponse> {
    console.log('📝 提交订单:', params)
    
    // 参数验证
    if (!params.symbol || !params.side || !params.type) {
      throw new Error('缺少必需参数: symbol, side, type')
    }

    // 市价单必须有数量
    if (params.type === 'MARKET' && !params.quantity) {
      throw new Error('市价单必须指定数量')
    }

    // 限价单必须有价格和数量
    if (params.type === 'LIMIT' && (!params.price || !params.quantity)) {
      throw new Error('限价单必须指定价格和数量')
    }

    // 设置默认值
    const orderParams: NewOrderParams = {
      ...params,
      newOrderRespType: params.newOrderRespType || 'RESULT',
      timeInForce: params.type === 'LIMIT' ? (params.timeInForce || 'GTC') : undefined,
      positionSide: params.positionSide || 'BOTH'
    }

    // 生成客户端订单ID（如果未提供）
    if (!orderParams.newClientOrderId) {
      orderParams.newClientOrderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    try {
      const response = await this.apiRequest('POST', '/fapi/v1/order', orderParams)
      console.log('✅ 订单提交成功:', response)
      return response
    } catch (error) {
      console.error('❌ 下单失败:', error)
      throw error
    }
  }

  // 2. 查询订单
  async getOrder(symbol: string, orderId?: number, origClientOrderId?: string): Promise<OrderInfo> {
    if (!orderId && !origClientOrderId) {
      throw new Error('必须提供 orderId 或 origClientOrderId')
    }

    const params: any = { symbol }
    if (orderId) params.orderId = orderId
    if (origClientOrderId) params.origClientOrderId = origClientOrderId

    try {
      const response = await this.apiRequest('GET', '/fapi/v1/order', params)
      console.log('📊 订单查询成功:', response)
      return response
    } catch (error) {
      console.error('❌ 订单查询失败:', error)
      throw error
    }
  }

  // 3. 撤销订单
  async cancelOrder(symbol: string, orderId?: number, origClientOrderId?: string): Promise<CancelResponse> {
    if (!orderId && !origClientOrderId) {
      throw new Error('必须提供 orderId 或 origClientOrderId')
    }

    const params: any = { symbol }
    if (orderId) params.orderId = orderId
    if (origClientOrderId) params.origClientOrderId = origClientOrderId

    try {
      const response = await this.apiRequest('DELETE', '/fapi/v1/order', params)
      console.log('🗑️ 订单撤销成功:', response)
      return response
    } catch (error) {
      console.error('❌ 订单撤销失败:', error)
      throw error
    }
  }

  // 4. 撤销所有挂单
  async cancelAllOrders(symbol: string): Promise<{ code: number; msg: string }> {
    try {
      const response = await this.apiRequest('DELETE', '/fapi/v1/allOpenOrders', { symbol })
      console.log('🗑️ 所有挂单撤销成功:', response)
      return response
    } catch (error) {
      console.error('❌ 撤销所有挂单失败:', error)
      throw error
    }
  }

  // 5. 查询当前挂单
  async getOpenOrders(symbol?: string): Promise<OrderInfo[]> {
    const params = symbol ? { symbol } : {}
    
    try {
      const response = await this.apiRequest('GET', '/fapi/v1/openOrders', params)
      console.log('📋 当前挂单查询成功:', response)
      return response
    } catch (error) {
      console.error('❌ 挂单查询失败:', error)
      throw error
    }
  }

  // 6. 查询所有订单
  async getAllOrders(symbol: string, orderId?: number, startTime?: number, endTime?: number, limit: number = 500): Promise<OrderInfo[]> {
    const params: any = { symbol, limit }
    if (orderId) params.orderId = orderId
    if (startTime) params.startTime = startTime
    if (endTime) params.endTime = endTime

    try {
      const response = await this.apiRequest('GET', '/fapi/v1/allOrders', params)
      console.log('📜 历史订单查询成功:', response)
      return response
    } catch (error) {
      console.error('❌ 历史订单查询失败:', error)
      throw error
    }
  }

  // 7. 批量下单（最多5个订单）
  async batchOrders(orders: NewOrderParams[]): Promise<BatchOrderResponse> {
    if (orders.length === 0 || orders.length > 5) {
      throw new Error('批量订单数量必须在1-5个之间')
    }

    // 为每个订单生成客户端ID
    const batchList = orders.map((order, index) => ({
      ...order,
      newClientOrderId: order.newClientOrderId || `batch_${Date.now()}_${index}`,
      newOrderRespType: order.newOrderRespType || 'RESULT'
    }))

    const params = { batchOrders: JSON.stringify(batchList) }

    try {
      const response = await this.apiRequest('POST', '/fapi/v1/batchOrders', params)
      console.log('📦 批量下单成功:', response)
      return { orders: response, errors: [] }
    } catch (error: any) {
      console.error('❌ 批量下单失败:', error)
      
      // 处理部分成功的情况
      if (error.response?.data && Array.isArray(error.response.data)) {
        const results = error.response.data
        const orders = results.filter((r: any) => !r.code)
        const errors = results.filter((r: any) => r.code).map((r: any) => ({
          code: r.code,
          msg: r.msg,
          data: r
        }))
        return { orders, errors }
      }
      
      throw error
    }
  }

  // 8. 快捷方法：市价买入
  async marketBuy(symbol: string, quantity: number, positionSide: "BOTH" | "LONG" | "SHORT" = "BOTH"): Promise<OrderResponse> {
    return this.placeOrder({
      symbol,
      side: "BUY",
      type: "MARKET",
      quantity: quantity.toString(),
      positionSide
    })
  }

  // 9. 快捷方法：市价卖出
  async marketSell(symbol: string, quantity: number, positionSide: "BOTH" | "LONG" | "SHORT" = "BOTH"): Promise<OrderResponse> {
    return this.placeOrder({
      symbol,
      side: "SELL",
      type: "MARKET",
      quantity: quantity.toString(),
      positionSide
    })
  }

  // 10. 快捷方法：限价买入
  async limitBuy(symbol: string, quantity: number, price: number, positionSide: "BOTH" | "LONG" | "SHORT" = "BOTH"): Promise<OrderResponse> {
    return this.placeOrder({
      symbol,
      side: "BUY",
      type: "LIMIT",
      quantity: quantity.toString(),
      price: price.toString(),
      timeInForce: "GTC",
      positionSide
    })
  }

  // 11. 快捷方法：限价卖出
  async limitSell(symbol: string, quantity: number, price: number, positionSide: "BOTH" | "LONG" | "SHORT" = "BOTH"): Promise<OrderResponse> {
    return this.placeOrder({
      symbol,
      side: "SELL",
      type: "LIMIT",
      quantity: quantity.toString(),
      price: price.toString(),
      timeInForce: "GTC",
      positionSide
    })
  }

  // 12. 快捷方法：平仓
  async closePosition(symbol: string, positionSide: "LONG" | "SHORT" = "LONG"): Promise<OrderResponse> {
    const side = positionSide === "LONG" ? "SELL" : "BUY"
    return this.placeOrder({
      symbol,
      side,
      type: "MARKET",
      positionSide,
      closePosition: true
    })
  }

  // 13. 快捷方法：止损单
  async stopLossOrder(symbol: string, quantity: number, stopPrice: number, side: "BUY" | "SELL", positionSide: "BOTH" | "LONG" | "SHORT" = "BOTH"): Promise<OrderResponse> {
    return this.placeOrder({
      symbol,
      side,
      type: "STOP_MARKET",
      quantity: quantity.toString(),
      stopPrice: stopPrice.toString(),
      positionSide,
      reduceOnly: true
    })
  }

  // 14. 快捷方法：止盈单
  async takeProfitOrder(symbol: string, quantity: number, stopPrice: number, side: "BUY" | "SELL", positionSide: "BOTH" | "LONG" | "SHORT" = "BOTH"): Promise<OrderResponse> {
    return this.placeOrder({
      symbol,
      side,
      type: "TAKE_PROFIT_MARKET",
      quantity: quantity.toString(),
      stopPrice: stopPrice.toString(),
      positionSide,
      reduceOnly: true
    })
  }

  // 更新连接状态
  updateConnectionStatus(): void {
    this.isConnected = accountService.getConnectionStatus()
  }

  // 获取连接状态
  getConnectionStatus(): boolean {
    return this.isConnected
  }
}

// 导出单例
export const binanceOrderService = new BinanceOrderService()
export default binanceOrderService 