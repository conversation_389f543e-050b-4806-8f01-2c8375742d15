#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风险控制导向的量化交易策略系统 v4.0
专注：最大回撤≤15%，力求在严格风控下获得稳定收益
核心理念：先控制风险，再追求收益
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from datetime import datetime, timedelta


class RiskControlIndicators:
    """风险控制技术指标"""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """简单移动平均线"""
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """指数移动平均线"""
        return data.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """相对强弱指标"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """平均真实波动范围"""
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        return tr.rolling(window=period).mean()
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """布林带"""
        sma = RiskControlIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band
    
    @staticmethod
    def rolling_drawdown(data: pd.Series, window: int = 96) -> pd.Series:
        """滚动回撤计算 (96个15分钟 = 1天)"""
        rolling_max = data.rolling(window=window).max()
        drawdown = (data - rolling_max) / rolling_max
        return drawdown


class RiskControlledBacktester:
    """风险控制回测引擎"""
    
    def __init__(self, initial_capital: float = 100000, 
                 max_drawdown_limit: float = 0.15,  # 最大回撤限制15%
                 commission_rate: float = 0.0005,
                 slippage_rate: float = 0.0002):
        """
        初始化风险控制回测引擎
        
        Args:
            initial_capital: 初始资金
            max_drawdown_limit: 最大回撤限制 (15%)
            commission_rate: 手续费率 (0.05%)
            slippage_rate: 滑点率 (0.02%)
        """
        self.initial_capital = initial_capital
        self.max_drawdown_limit = max_drawdown_limit
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        self.data = None
        
    def load_data(self, csv_path: str) -> pd.DataFrame:
        """加载K线数据"""
        print(f"📊 正在加载数据: {csv_path}")
        
        # 读取数据
        df = pd.read_csv(csv_path)
        
        # 数据预处理
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        # 计算技术指标
        df = self._calculate_indicators(df)
        
        self.data = df
        print(f"✅ 数据加载完成，共 {len(df)} 条记录")
        print(f"📅 时间范围: {df.index[0]} 至 {df.index[-1]}")
        
        return df
        
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算风险控制指标"""
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # 移动平均线系统
        for period in [10, 20, 50, 100, 200]:
            df[f'ma_{period}'] = RiskControlIndicators.sma(close, period)
            df[f'ema_{period}'] = RiskControlIndicators.ema(close, period)
        
        # RSI
        df['rsi_14'] = RiskControlIndicators.rsi(close, 14)
        df['rsi_21'] = RiskControlIndicators.rsi(close, 21)
        
        # ATR (波动率)
        df['atr'] = RiskControlIndicators.atr(high, low, close, 14)
        df['atr_pct'] = df['atr'] / close * 100  # ATR百分比
        
        # 布林带
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = RiskControlIndicators.bollinger_bands(close, 20)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        
        # 价格变化率
        df['returns_1h'] = close.pct_change(4)   # 1小时收益率
        df['returns_4h'] = close.pct_change(16)  # 4小时收益率
        df['returns_1d'] = close.pct_change(96)  # 1天收益率
        
        # 成交量指标
        df['volume_ma20'] = RiskControlIndicators.sma(volume, 20)
        df['volume_ratio'] = volume / df['volume_ma20']
        
        # 波动率指标
        df['volatility_20'] = close.pct_change().rolling(20).std() * np.sqrt(96)  # 日波动率
        
        # 趋势强度
        df['trend_strength'] = (close - df['ma_50']) / df['ma_50']
        
        return df


class RiskControlledStrategies:
    """风险控制策略集合"""
    
    @staticmethod
    def dynamic_position_strategy(df: pd.DataFrame, max_drawdown: float = 0.15) -> pd.DataFrame:
        """动态仓位管理策略 - 根据回撤动态调整仓位"""
        signals = pd.DataFrame(index=df.index)
        
        # 基础趋势判断
        uptrend = (df['ema_20'] > df['ema_50']) & (df['close'] > df['ema_20'])
        
        # 波动率过滤 - 高波动时减少仓位
        low_volatility = df['atr_pct'] < df['atr_pct'].quantile(0.7)
        
        # RSI过滤
        rsi_ok = (df['rsi_14'] > 35) & (df['rsi_14'] < 70)
        
        # 基础仓位信号
        base_position = np.where(uptrend & low_volatility & rsi_ok, 1, 0)
        
        # 动态仓位调整 - 根据近期表现
        signals['base_position'] = base_position
        signals['position'] = 0.0
        
        # 初始仓位
        signals['position'].iloc[0] = signals['base_position'].iloc[0] * 0.2  # 初始低仓位
        
        # 动态调整逻辑
        for i in range(1, len(signals)):
            if signals['base_position'].iloc[i] == 1:
                # 如果基础信号看涨
                if df['returns_1d'].iloc[i] > 0:
                    # 如果最近表现好，增加仓位
                    signals['position'].iloc[i] = min(0.5, signals['position'].iloc[i-1] + 0.1)
                else:
                    # 如果最近表现不好，保持或减少仓位
                    signals['position'].iloc[i] = max(0.1, signals['position'].iloc[i-1] - 0.05)
            else:
                # 如果基础信号看跌，快速减仓
                signals['position'].iloc[i] = max(0, signals['position'].iloc[i-1] - 0.2)
        
        signals['signal'] = signals['position'].diff()
        return signals
    
    @staticmethod
    def volatility_adjusted_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """波动率调整策略 - 高波动时降低仓位"""
        signals = pd.DataFrame(index=df.index)
        
        # 趋势确认
        long_trend = (df['ma_50'] > df['ma_100']) & (df['ma_100'] > df['ma_200'])
        short_trend = (df['ema_10'] > df['ema_20']) & (df['close'] > df['ema_10'])
        
        # 波动率调整因子
        vol_factor = np.clip(1 / (df['volatility_20'] / df['volatility_20'].median()), 0.1, 1.0)
        
        # 基础仓位
        base_signal = np.where(long_trend & short_trend, 1, 0)
        
        # 应用波动率调整
        signals['position'] = base_signal * vol_factor * 0.3  # 最大30%仓位
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def conservative_dca_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """保守定投策略 - 分批建仓，严格止损"""
        signals = pd.DataFrame(index=df.index)
        signals['position'] = 0.0
        
        # 每周定投一次 (7*24*4 = 672个15分钟周期)
        dca_interval = 672
        
        # 趋势过滤 - 只在上升趋势中定投
        uptrend = (df['ma_50'] > df['ma_100']) & (df['ema_20'] > df['ema_50'])
        
        # RSI过滤 - 避免高位定投
        rsi_ok = df['rsi_14'] < 60
        
        position = 0.0
        max_position = 0.4  # 最大40%仓位
        single_investment = 0.05  # 每次5%仓位
        
        for i in range(len(df)):
            # 定期定投
            if i % dca_interval == 0 and uptrend.iloc[i] and rsi_ok.iloc[i]:
                if position < max_position:
                    position += single_investment
                    position = min(position, max_position)
            
            # 止损机制 - RSI过高或趋势转弱时减仓
            elif df['rsi_14'].iloc[i] > 75 or not uptrend.iloc[i]:
                position *= 0.8  # 减仓20%
            
            signals['position'].iloc[i] = position
        
        signals['signal'] = signals['position'].diff()
        return signals
    
    @staticmethod
    def stop_loss_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """严格止损策略 - 固定止损点"""
        signals = pd.DataFrame(index=df.index)
        
        # 入场条件
        entry_condition = (
            (df['close'] > df['ema_20']) & 
            (df['ema_20'] > df['ema_50']) &
            (df['rsi_14'] > 40) & 
            (df['rsi_14'] < 65) &
            (df['volume_ratio'] > 1.1)
        )
        
        signals['position'] = 0.0
        signals['entry_price'] = 0.0
        
        position = 0.0
        entry_price = 0.0
        stop_loss_pct = 0.05  # 5%止损
        
        for i in range(len(df)):
            current_price = df['close'].iloc[i]
            
            if position == 0 and entry_condition.iloc[i]:
                # 入场
                position = 0.3  # 30%仓位
                entry_price = current_price
            elif position > 0:
                # 检查止损
                if current_price < entry_price * (1 - stop_loss_pct):
                    position = 0.0  # 止损出场
                    entry_price = 0.0
                # 检查止盈或退出条件
                elif df['rsi_14'].iloc[i] > 75 or current_price < df['ema_20'].iloc[i]:
                    position = 0.0  # 正常出场
                    entry_price = 0.0
            
            signals['position'].iloc[i] = position
            signals['entry_price'].iloc[i] = entry_price
        
        signals['signal'] = signals['position'].diff()
        return signals
    
    @staticmethod
    def trend_filter_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """多重趋势过滤策略 - 严格的趋势确认"""
        signals = pd.DataFrame(index=df.index)
        
        # 多重趋势过滤
        ma_trend = (df['ma_20'] > df['ma_50']) & (df['ma_50'] > df['ma_100']) & (df['ma_100'] > df['ma_200'])
        ema_trend = (df['ema_10'] > df['ema_20']) & (df['ema_20'] > df['ema_50'])
        price_trend = df['close'] > df['ma_20']
        
        # 综合趋势确认
        strong_uptrend = ma_trend & ema_trend & price_trend
        
        # 动量确认
        momentum_ok = (df['returns_1h'] > 0) & (df['returns_4h'] > 0)
        
        # RSI确认
        rsi_confirm = (df['rsi_14'] > 45) & (df['rsi_14'] < 70)
        
        # 成交量确认
        volume_ok = df['volume_ratio'] > 0.8
        
        # 低波动率确认
        low_vol = df['atr_pct'] < df['atr_pct'].quantile(0.6)
        
        # 综合信号
        buy_signal = strong_uptrend & momentum_ok & rsi_confirm & volume_ok & low_vol
        
        # 保守仓位
        signals['position'] = np.where(buy_signal, 0.25, 0)  # 最大25%仓位
        signals['signal'] = signals['position'].diff()
        
        return signals
    
    @staticmethod
    def mean_reversion_controlled(df: pd.DataFrame) -> pd.DataFrame:
        """风控版均值回归策略"""
        signals = pd.DataFrame(index=df.index)
        
        # 确保在大趋势向上时才做均值回归
        major_uptrend = df['ma_200'] > df['ma_200'].shift(200)  # 长期趋势向上
        
        # 短期超卖机会
        oversold = (df['rsi_14'] < 30) & (df['close'] < df['bb_lower'])
        
        # 成交量放大确认
        volume_spike = df['volume_ratio'] > 1.5
        
        # 买入条件：长期上涨 + 短期超卖 + 成交量确认
        buy_condition = major_uptrend & oversold & volume_spike
        
        # 卖出条件：RSI回到中性区域或接近布林带中轨
        sell_condition = (df['rsi_14'] > 50) | (df['close'] > df['bb_middle'])
        
        signals['position'] = 0.0
        position = 0.0
        
        for i in range(len(df)):
            if buy_condition.iloc[i] and position == 0:
                position = 0.2  # 20%仓位
            elif sell_condition.iloc[i] and position > 0:
                position = 0.0
            
            signals['position'].iloc[i] = position
        
        signals['signal'] = signals['position'].diff()
        return signals
    
    @staticmethod
    def minimal_risk_strategy(df: pd.DataFrame) -> pd.DataFrame:
        """极低风险策略 - 严格的入场和出场条件"""
        signals = pd.DataFrame(index=df.index)
        
        # 超级严格的入场条件
        perfect_setup = (
            (df['close'] > df['ma_20']) &
            (df['ma_20'] > df['ma_50']) &
            (df['ma_50'] > df['ma_100']) &
            (df['ma_100'] > df['ma_200']) &  # 完美多头排列
            (df['rsi_14'] > 50) & (df['rsi_14'] < 60) &  # RSI适中
            (df['returns_1d'] > 0.01) &  # 1天涨幅>1%
            (df['volume_ratio'] > 1.2) &  # 成交量放大
            (df['atr_pct'] < df['atr_pct'].quantile(0.5))  # 低波动率
        )
        
        # 极低仓位
        signals['position'] = np.where(perfect_setup, 0.15, 0)  # 最大15%仓位
        signals['signal'] = signals['position'].diff()
        
        return signals


class RiskAwarePerformanceAnalyzer:
    """风险感知绩效分析器"""
    
    @staticmethod
    def calculate_risk_controlled_returns(df: pd.DataFrame, signals: pd.DataFrame, 
                                        initial_capital: float, commission_rate: float, 
                                        slippage_rate: float, max_drawdown_limit: float) -> Dict:
        """计算风险控制策略收益"""
        
        df_copy = df.copy()
        df_copy['position'] = signals['position'].fillna(0)
        df_copy['signal'] = signals['signal'].fillna(0)
        
        # 价格变化
        df_copy['price_change'] = df_copy['close'].pct_change()
        
        # 策略收益
        df_copy['strategy_return'] = df_copy['position'].shift(1) * df_copy['price_change']
        
        # 交易成本
        trade_mask = df_copy['signal'] != 0
        total_cost_rate = commission_rate + slippage_rate
        df_copy['trade_cost'] = 0
        df_copy.loc[trade_mask, 'trade_cost'] = total_cost_rate * abs(df_copy.loc[trade_mask, 'signal'])
        
        # 净收益
        df_copy['net_return'] = df_copy['strategy_return'] - df_copy['trade_cost']
        
        # 累计收益
        df_copy['cumulative_return'] = (1 + df_copy['net_return']).cumprod()
        df_copy['portfolio_value'] = initial_capital * df_copy['cumulative_return']
        
        # 动态回撤控制 - 如果回撤超过限制，强制减仓
        rolling_max = df_copy['portfolio_value'].expanding().max()
        drawdown = (df_copy['portfolio_value'] - rolling_max) / rolling_max
        
        # 强制风控：回撤超限时清仓
        emergency_exit = drawdown < -max_drawdown_limit
        if emergency_exit.any():
            first_exit_idx = emergency_exit.idxmax() if emergency_exit.any() else len(df_copy)
            df_copy.loc[first_exit_idx:, 'position'] = 0
            # 重新计算从强制清仓后的收益
            df_copy.loc[first_exit_idx:, 'strategy_return'] = 0
            df_copy.loc[first_exit_idx:, 'net_return'] = 0
            df_copy.loc[first_exit_idx:, 'cumulative_return'] = df_copy['cumulative_return'].iloc[first_exit_idx-1]
            df_copy.loc[first_exit_idx:, 'portfolio_value'] = df_copy['portfolio_value'].iloc[first_exit_idx-1]
        
        # 重新计算回撤
        rolling_max = df_copy['portfolio_value'].expanding().max()
        drawdown = (df_copy['portfolio_value'] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 计算其他指标
        total_return = df_copy['cumulative_return'].iloc[-1] - 1
        days = (df_copy.index[-1] - df_copy.index[0]).days
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        monthly_return = (1 + total_return) ** (30 / days) - 1 if days > 0 else 0
        
        # 夏普比率
        risk_free_rate = 0.02
        excess_return = df_copy['net_return'] - risk_free_rate / (365 * 24 * 4)
        sharpe_ratio = excess_return.mean() / excess_return.std() * np.sqrt(365 * 24 * 4) if excess_return.std() > 0 else 0
        
        # 其他指标
        winning_trades = (df_copy['net_return'] > 0).sum()
        total_trades = (df_copy['net_return'] != 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        trade_count = (df_copy['signal'].abs() > 0).sum()
        total_cost = df_copy['trade_cost'].sum()
        
        # 风险调整后收益指标
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0
        
        # 最大仓位
        max_position = df_copy['position'].max()
        avg_position = df_copy['position'].mean()
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'monthly_return': monthly_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'calmar_ratio': calmar_ratio,
            'win_rate': win_rate,
            'trade_count': trade_count,
            'total_cost': total_cost,
            'max_position': max_position,
            'avg_position': avg_position,
            'final_value': df_copy['portfolio_value'].iloc[-1],
            'portfolio_series': df_copy['portfolio_value'],
            'drawdown_series': drawdown,
            'emergency_exit_triggered': emergency_exit.any()
        }


def main():
    """主函数：风险控制策略回测"""
    
    print("🛡️ 启动风险控制导向策略回测系统 v4.0")
    print("=" * 60)
    print("🎯 目标：最大回撤≤15%，追求风险调整后收益")
    print("=" * 60)
    
    # 初始化风险控制回测引擎
    backester = RiskControlledBacktester(
        initial_capital=100000,
        max_drawdown_limit=0.15,  # 15%回撤限制
        commission_rate=0.0005,   # 0.05% 手续费
        slippage_rate=0.0002      # 0.02% 滑点
    )
    
    # 加载数据
    data_path = "K线数据/BTCUSDT_15m_189773.csv"
    try:
        df = backester.load_data(data_path)
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 风险控制策略集合
    strategies = {
        '动态仓位策略': RiskControlledStrategies.dynamic_position_strategy,
        '波动率调整策略': RiskControlledStrategies.volatility_adjusted_strategy,
        '保守定投策略': RiskControlledStrategies.conservative_dca_strategy,
        '严格止损策略': RiskControlledStrategies.stop_loss_strategy,
        '多重趋势过滤': RiskControlledStrategies.trend_filter_strategy,
        '风控均值回归': RiskControlledStrategies.mean_reversion_controlled,
        '极低风险策略': RiskControlledStrategies.minimal_risk_strategy
    }
    
    # 回测所有策略
    results = {}
    qualified_strategies = {}
    
    print("\n🔍 开始风险控制策略回测...")
    
    for strategy_name, strategy_func in strategies.items():
        print(f"\n🛡️ 测试策略: {strategy_name}")
        
        try:
            # 生成交易信号
            signals = strategy_func(df)
            
            # 计算绩效
            performance = RiskAwarePerformanceAnalyzer.calculate_risk_controlled_returns(
                df, signals, backester.initial_capital,
                backester.commission_rate, backester.slippage_rate,
                backester.max_drawdown_limit
            )
            
            results[strategy_name] = performance
            
            print(f"   💰 总收益: {performance['total_return']:.2%}")
            print(f"   📈 年化收益: {performance['annual_return']:.2%}")
            print(f"   📊 月化收益: {performance['monthly_return']:.2%}")
            print(f"   📉 最大回撤: {performance['max_drawdown']:.2%}")
            print(f"   ⚡ 夏普比率: {performance['sharpe_ratio']:.2f}")
            print(f"   📐 卡尔玛比率: {performance['calmar_ratio']:.2f}")
            print(f"   🎯 胜率: {performance['win_rate']:.2%}")
            print(f"   📊 最大仓位: {performance['max_position']:.1%}")
            print(f"   📊 平均仓位: {performance['avg_position']:.1%}")
            print(f"   🔄 交易次数: {performance['trade_count']}")
            
            if performance['emergency_exit_triggered']:
                print(f"   🚨 触发紧急止损")
            
            # 检查是否符合筛选条件
            if (performance['max_drawdown'] >= -0.15 and 
                performance['monthly_return'] >= 0.10):
                qualified_strategies[strategy_name] = performance
                print(f"   ✅ 符合筛选条件！")
            elif performance['max_drawdown'] >= -0.15:
                print(f"   🟡 回撤控制达标，但收益不足")
            else:
                print(f"   ❌ 回撤超限")
                
        except Exception as e:
            print(f"   ❌ 策略 {strategy_name} 回测失败: {e}")
    
    # 结果汇总
    print("\n" + "=" * 70)
    print("🎯 风险控制策略筛选结果")
    print("=" * 70)
    
    # 按卡尔玛比率排序（风险调整后收益）
    sorted_results = sorted(results.items(), 
                          key=lambda x: x[1]['calmar_ratio'] if x[1]['calmar_ratio'] > 0 else -999, 
                          reverse=True)
    
    # 创建结果表格
    print("\n📊 所有策略表现概览 (按风险调整收益排序):")
    summary_data = []
    for name, perf in sorted_results:
        符合条件 = "✅" if name in qualified_strategies else ("🟡" if perf['max_drawdown'] >= -0.15 else "❌")
        summary_data.append({
            '策略名称': name,
            '符合条件': 符合条件,
            '总收益': f"{perf['total_return']:.2%}",
            '月化收益': f"{perf['monthly_return']:.2%}",
            '最大回撤': f"{perf['max_drawdown']:.2%}",
            '夏普比率': f"{perf['sharpe_ratio']:.2f}",
            '卡尔玛比率': f"{perf['calmar_ratio']:.2f}",
            '平均仓位': f"{perf['avg_position']:.1%}",
            '交易次数': perf['trade_count']
        })
    
    summary_df = pd.DataFrame(summary_data)
    print(summary_df.to_string(index=False))
    
    # 输出符合条件的策略
    if qualified_strategies:
        print(f"\n🎉 找到 {len(qualified_strategies)} 个符合条件的策略！")
        
        best_strategy = max(qualified_strategies.items(), 
                          key=lambda x: x[1]['calmar_ratio'])
        
        print(f"\n🏆 最优风险控制策略: {best_strategy[0]}")
        perf = best_strategy[1]
        print(f"   💰 总收益: {perf['total_return']:.2%}")
        print(f"   📈 年化收益: {perf['annual_return']:.2%}")
        print(f"   📊 月化收益: {perf['monthly_return']:.2%}")
        print(f"   📉 最大回撤: {perf['max_drawdown']:.2%}")
        print(f"   ⚡ 夏普比率: {perf['sharpe_ratio']:.2f}")
        print(f"   📐 卡尔玛比率: {perf['calmar_ratio']:.2f}")
        print(f"   💎 最终资产: ${perf['final_value']:,.2f}")
        
    else:
        print("❌ 仍然没有策略完全符合严格条件")
        
        # 分析最接近的策略
        print("\n🔍 最接近条件的策略分析:")
        
        # 找出回撤控制最好的策略
        best_drawdown = min(results.items(), key=lambda x: abs(x[1]['max_drawdown']))
        print(f"\n🛡️ 回撤控制最佳: {best_drawdown[0]}")
        print(f"   最大回撤: {best_drawdown[1]['max_drawdown']:.2%}")
        print(f"   月化收益: {best_drawdown[1]['monthly_return']:.2%}")
        
        # 找出风险调整收益最好的策略
        best_calmar = max(results.items(), key=lambda x: x[1]['calmar_ratio'] if x[1]['calmar_ratio'] > 0 else -999)
        print(f"\n📐 风险调整收益最佳: {best_calmar[0]}")
        print(f"   卡尔玛比率: {best_calmar[1]['calmar_ratio']:.2f}")
        print(f"   最大回撤: {best_calmar[1]['max_drawdown']:.2%}")
        print(f"   月化收益: {best_calmar[1]['monthly_return']:.2%}")
    
    # 关键洞察
    print(f"\n💡 关键洞察:")
    print(f"   📊 所有策略均采用保守仓位控制 (≤50%)")
    print(f"   🛡️ 内置动态止损和回撤控制机制")
    print(f"   📈 重点关注风险调整后收益而非绝对收益")
    print(f"   🎯 追求稳定性和可持续性")
    
    print(f"\n🎊 风险控制策略回测完成！")
    
    return results, qualified_strategies


if __name__ == "__main__":
    main() 