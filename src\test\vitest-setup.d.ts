/// <reference types="vitest" />
/// <reference types="@testing-library/jest-dom" />
/// <reference types="vitest/globals" />

import type { TestingLibraryMatchers } from '@testing-library/jest-dom/matchers'
import 'vitest'

declare module 'vitest' {
  interface Assertion<T = any> extends jest.Matchers<void>, TestingLibraryMatchers<T, void> {}
  interface AsymmetricMatchersContaining extends jest.AsymmetricMatchers, TestingLibraryMatchers<any, void> {}
}

// 为window.alert添加jest mock支持
declare global {
  interface Window {
    alert: jest.Mock
  }
  
  namespace jest {
    interface SpyInstance<T = any, Y extends any[] = any> extends vi.MockedFunction<T> {}
  }
} 