#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据使用情况
"""

import pandas as pd

def check_data_usage():
    print("📊 检查数据使用情况")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('K线数据/BTCUSDT_15m_189773.csv')
    
    print(f"🔍 文件实际行数: {len(df)}")
    print(f"📅 数据时间范围: {df.iloc[0]['datetime']} 到 {df.iloc[-1]['datetime']}")
    
    print(f"\n📊 各种策略的数据使用情况:")
    
    # 检查不同策略的起始点
    print(f"   原始数据总量: {len(df)} 条")
    print(f"   基础策略(从第60行开始): 使用 {len(df) - 60} 条数据 ({(len(df) - 60)/len(df)*100:.1f}%)")
    print(f"   优化策略(从第120行开始): 使用 {len(df) - 120} 条数据 ({(len(df) - 120)/len(df)*100:.1f}%)")
    
    # 计算跳过的数据对应的时间
    df['datetime'] = pd.to_datetime(df['datetime'])
    
    print(f"\n⏰ 跳过的时间段:")
    print(f"   基础策略跳过: {df.iloc[0]['datetime']} 到 {df.iloc[59]['datetime']}")
    print(f"   优化策略跳过: {df.iloc[0]['datetime']} 到 {df.iloc[119]['datetime']}")
    
    # 计算跳过的天数
    skip_60_days = (df.iloc[59]['datetime'] - df.iloc[0]['datetime']).days
    skip_120_days = (df.iloc[119]['datetime'] - df.iloc[0]['datetime']).days
    
    print(f"\n📈 跳过的天数:")
    print(f"   基础策略跳过: {skip_60_days} 天")
    print(f"   优化策略跳过: {skip_120_days} 天")
    
    # 总时间跨度
    total_days = (df.iloc[-1]['datetime'] - df.iloc[0]['datetime']).days
    print(f"   数据总时间跨度: {total_days} 天")
    
    print(f"\n💡 数据使用分析:")
    print(f"   我们确实没有使用全部数据")
    print(f"   跳过前面数据的原因: 确保技术指标(如MA120)有足够的历史数据计算")
    print(f"   这是量化策略的标准做法，称为'预热期'")

if __name__ == "__main__":
    check_data_usage() 